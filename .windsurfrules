0. I hate use of "try:" and "except:" in python code
1. My codes are always compact and to the point
2. I do not like bulky codes
3. I do not like codes with unnecessary comments
4. I like modular code
5. I like code that is easy to read and understand
6. I like code that is easy to test
7. I like code that is easy to maintain
8. I like code that is easy to debug
9. I like code that is easy to scale
10. I like code that is easy to deploy
11. I like code that is easy to integrate with other systems
12. I like code that is easy to learn and use
13. I like code that is easy to modify
