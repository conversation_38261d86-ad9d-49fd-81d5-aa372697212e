"""
Project Completion Tracker

Ensures that projects are truly complete before allowing the main LLM to stop.
This is critical for preventing premature completion claims.
"""

import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from enum import Enum

from shared.models import Task, TaskStatus, TaskType
from shared.conversation_state import ConversationStateManager


class CompletionCriteria(str, Enum):
    """Criteria that must be met for true completion"""
    ALL_TASKS_COMPLETE = "all_tasks_complete"
    TESTS_PASSING = "tests_passing"
    CODE_QUALITY_VERIFIED = "code_quality_verified"
    DOCUMENTATION_COMPLETE = "documentation_complete"
    ERROR_HANDLING_IMPLEMENTED = "error_handling_implemented"
    INTEGRATION_TESTED = "integration_tested"
    PERFORMANCE_VERIFIED = "performance_verified"


class ProjectCompletionTracker:
    """
    Tracks project completion and prevents premature stopping
    
    This is the guardian that ensures the main LLM never stops
    until the project is truly, completely finished.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Completion requirements
        self.required_criteria = set(config.get("required_criteria", [
            CompletionCriteria.ALL_TASKS_COMPLETE,
            CompletionCriteria.TESTS_PASSING,
            CompletionCriteria.CODE_QUALITY_VERIFIED,
            CompletionCriteria.ERROR_HANDLING_IMPLEMENTED
        ]))
        
        # Tracking state
        self.completion_status: Dict[CompletionCriteria, bool] = {
            criteria: False for criteria in CompletionCriteria
        }
        
        self.completion_evidence: Dict[CompletionCriteria, List[str]] = {
            criteria: [] for criteria in CompletionCriteria
        }
        
        # Integration with conversation state
        self.conversation_manager: Optional[ConversationStateManager] = None
        
    def set_conversation_manager(self, manager: ConversationStateManager):
        """Set the conversation state manager"""
        self.conversation_manager = manager
    
    def evaluate_task_completion(self, tasks: List[Task]) -> Dict[str, Any]:
        """
        Evaluate if tasks are truly complete
        
        Args:
            tasks: List of all tasks
            
        Returns:
            Completion evaluation result
        """
        self.logger.info("Evaluating project completion status")
        
        # Reset status
        for criteria in self.completion_status:
            self.completion_status[criteria] = False
            self.completion_evidence[criteria] = []
        
        # Check each completion criteria
        self._check_all_tasks_complete(tasks)
        self._check_tests_passing(tasks)
        self._check_code_quality(tasks)
        self._check_documentation(tasks)
        self._check_error_handling(tasks)
        self._check_integration(tasks)
        self._check_performance(tasks)
        
        # Calculate overall completion
        required_met = sum(1 for criteria in self.required_criteria 
                          if self.completion_status[criteria])
        total_required = len(self.required_criteria)
        completion_percentage = (required_met / total_required) * 100 if total_required > 0 else 0
        
        is_truly_complete = all(self.completion_status[criteria] 
                               for criteria in self.required_criteria)
        
        result = {
            "is_complete": is_truly_complete,
            "completion_percentage": completion_percentage,
            "required_criteria_met": required_met,
            "total_required_criteria": total_required,
            "criteria_status": dict(self.completion_status),
            "evidence": dict(self.completion_evidence),
            "blocking_issues": self._get_blocking_issues(),
            "next_actions": self._get_next_actions()
        }
        
        if is_truly_complete:
            self.logger.info("🎉 PROJECT IS TRULY COMPLETE!")
        else:
            self.logger.warning(f"⚠️ Project not complete: {completion_percentage:.1f}% ({required_met}/{total_required})")
        
        return result
    
    def _check_all_tasks_complete(self, tasks: List[Task]):
        """Check if all tasks are truly complete"""
        incomplete_tasks = [task for task in tasks if task.status != TaskStatus.COMPLETED]
        
        if not incomplete_tasks:
            self.completion_status[CompletionCriteria.ALL_TASKS_COMPLETE] = True
            self.completion_evidence[CompletionCriteria.ALL_TASKS_COMPLETE].append(
                f"All {len(tasks)} tasks completed"
            )
        else:
            for task in incomplete_tasks:
                self.completion_evidence[CompletionCriteria.ALL_TASKS_COMPLETE].append(
                    f"Task '{task.title}' is {task.status.value}"
                )
    
    def _check_tests_passing(self, tasks: List[Task]):
        """Check if tests are implemented and passing"""
        test_tasks = [task for task in tasks if task.task_type == TaskType.TESTING]
        
        if test_tasks:
            passing_tests = [task for task in test_tasks if task.status == TaskStatus.COMPLETED]
            if len(passing_tests) == len(test_tasks):
                self.completion_status[CompletionCriteria.TESTS_PASSING] = True
                self.completion_evidence[CompletionCriteria.TESTS_PASSING].append(
                    f"All {len(test_tasks)} test tasks completed"
                )
            else:
                self.completion_evidence[CompletionCriteria.TESTS_PASSING].append(
                    f"Only {len(passing_tests)}/{len(test_tasks)} test tasks completed"
                )
        else:
            # If no explicit test tasks, check if code generation tasks have tests
            code_tasks = [task for task in tasks if task.task_type == TaskType.CODE_GENERATION]
            tested_tasks = [task for task in code_tasks if 'test' in task.metadata.get('verification', '')]
            
            if len(tested_tasks) == len(code_tasks) and code_tasks:
                self.completion_status[CompletionCriteria.TESTS_PASSING] = True
                self.completion_evidence[CompletionCriteria.TESTS_PASSING].append(
                    f"All {len(code_tasks)} code tasks have tests"
                )
            else:
                self.completion_evidence[CompletionCriteria.TESTS_PASSING].append(
                    "No explicit testing tasks found and code tasks lack test verification"
                )
    
    def _check_code_quality(self, tasks: List[Task]):
        """Check if code quality is verified"""
        code_tasks = [task for task in tasks if task.task_type == TaskType.CODE_GENERATION]
        
        if code_tasks:
            quality_verified = [task for task in code_tasks 
                              if task.quality_score >= 0.8]  # 80% quality threshold
            
            if len(quality_verified) == len(code_tasks):
                self.completion_status[CompletionCriteria.CODE_QUALITY_VERIFIED] = True
                self.completion_evidence[CompletionCriteria.CODE_QUALITY_VERIFIED].append(
                    f"All {len(code_tasks)} code tasks meet quality threshold"
                )
            else:
                low_quality = len(code_tasks) - len(quality_verified)
                self.completion_evidence[CompletionCriteria.CODE_QUALITY_VERIFIED].append(
                    f"{low_quality} code tasks below quality threshold"
                )
        else:
            self.completion_status[CompletionCriteria.CODE_QUALITY_VERIFIED] = True
            self.completion_evidence[CompletionCriteria.CODE_QUALITY_VERIFIED].append(
                "No code generation tasks to verify"
            )
    
    def _check_documentation(self, tasks: List[Task]):
        """Check if documentation is complete"""
        doc_tasks = [task for task in tasks if task.task_type == TaskType.DOCUMENTATION]
        
        if doc_tasks:
            completed_docs = [task for task in doc_tasks if task.status == TaskStatus.COMPLETED]
            if len(completed_docs) == len(doc_tasks):
                self.completion_status[CompletionCriteria.DOCUMENTATION_COMPLETE] = True
                self.completion_evidence[CompletionCriteria.DOCUMENTATION_COMPLETE].append(
                    f"All {len(doc_tasks)} documentation tasks completed"
                )
            else:
                self.completion_evidence[CompletionCriteria.DOCUMENTATION_COMPLETE].append(
                    f"Only {len(completed_docs)}/{len(doc_tasks)} documentation tasks completed"
                )
        else:
            # Check if code tasks have documentation
            code_tasks = [task for task in tasks if task.task_type == TaskType.CODE_GENERATION]
            documented_tasks = [task for task in code_tasks 
                              if 'docstring' in task.metadata.get('features', [])]
            
            if len(documented_tasks) >= len(code_tasks) * 0.8:  # 80% documented
                self.completion_status[CompletionCriteria.DOCUMENTATION_COMPLETE] = True
                self.completion_evidence[CompletionCriteria.DOCUMENTATION_COMPLETE].append(
                    f"{len(documented_tasks)}/{len(code_tasks)} code tasks have documentation"
                )
            else:
                self.completion_evidence[CompletionCriteria.DOCUMENTATION_COMPLETE].append(
                    f"Only {len(documented_tasks)}/{len(code_tasks)} code tasks documented"
                )
    
    def _check_error_handling(self, tasks: List[Task]):
        """Check if error handling is implemented"""
        code_tasks = [task for task in tasks if task.task_type == TaskType.CODE_GENERATION]
        
        if code_tasks:
            error_handled_tasks = [task for task in code_tasks 
                                 if 'error_handling' in task.metadata.get('features', [])]
            
            if len(error_handled_tasks) >= len(code_tasks) * 0.9:  # 90% with error handling
                self.completion_status[CompletionCriteria.ERROR_HANDLING_IMPLEMENTED] = True
                self.completion_evidence[CompletionCriteria.ERROR_HANDLING_IMPLEMENTED].append(
                    f"{len(error_handled_tasks)}/{len(code_tasks)} code tasks have error handling"
                )
            else:
                self.completion_evidence[CompletionCriteria.ERROR_HANDLING_IMPLEMENTED].append(
                    f"Only {len(error_handled_tasks)}/{len(code_tasks)} code tasks have error handling"
                )
        else:
            self.completion_status[CompletionCriteria.ERROR_HANDLING_IMPLEMENTED] = True
            self.completion_evidence[CompletionCriteria.ERROR_HANDLING_IMPLEMENTED].append(
                "No code generation tasks requiring error handling"
            )
    
    def _check_integration(self, tasks: List[Task]):
        """Check if integration testing is complete"""
        # This is a simplified check - in practice, would verify actual integration
        all_tasks_complete = all(task.status == TaskStatus.COMPLETED for task in tasks)
        
        if all_tasks_complete and len(tasks) > 1:
            self.completion_status[CompletionCriteria.INTEGRATION_TESTED] = True
            self.completion_evidence[CompletionCriteria.INTEGRATION_TESTED].append(
                "All tasks completed - integration assumed verified"
            )
        else:
            self.completion_evidence[CompletionCriteria.INTEGRATION_TESTED].append(
                "Integration testing not verified"
            )
    
    def _check_performance(self, tasks: List[Task]):
        """Check if performance is verified"""
        # This is a simplified check - in practice, would run performance tests
        code_tasks = [task for task in tasks if task.task_type == TaskType.CODE_GENERATION]
        
        if code_tasks:
            # Assume performance is verified if quality score is high
            high_quality_tasks = [task for task in code_tasks if task.quality_score >= 0.9]
            
            if len(high_quality_tasks) >= len(code_tasks) * 0.8:
                self.completion_status[CompletionCriteria.PERFORMANCE_VERIFIED] = True
                self.completion_evidence[CompletionCriteria.PERFORMANCE_VERIFIED].append(
                    f"{len(high_quality_tasks)}/{len(code_tasks)} tasks have high quality scores"
                )
            else:
                self.completion_evidence[CompletionCriteria.PERFORMANCE_VERIFIED].append(
                    f"Only {len(high_quality_tasks)}/{len(code_tasks)} tasks have high quality"
                )
        else:
            self.completion_status[CompletionCriteria.PERFORMANCE_VERIFIED] = True
            self.completion_evidence[CompletionCriteria.PERFORMANCE_VERIFIED].append(
                "No performance-critical tasks to verify"
            )
    
    def _get_blocking_issues(self) -> List[str]:
        """Get list of issues blocking completion"""
        blocking_issues = []
        
        for criteria in self.required_criteria:
            if not self.completion_status[criteria]:
                evidence = self.completion_evidence[criteria]
                if evidence:
                    blocking_issues.extend(evidence)
                else:
                    blocking_issues.append(f"{criteria.value} not verified")
        
        return blocking_issues
    
    def _get_next_actions(self) -> List[str]:
        """Get list of next actions needed for completion"""
        actions = []
        
        for criteria in self.required_criteria:
            if not self.completion_status[criteria]:
                if criteria == CompletionCriteria.ALL_TASKS_COMPLETE:
                    actions.append("🎯 Complete all remaining tasks")
                elif criteria == CompletionCriteria.TESTS_PASSING:
                    actions.append("🧪 Implement and run comprehensive tests")
                elif criteria == CompletionCriteria.CODE_QUALITY_VERIFIED:
                    actions.append("🔍 Improve code quality to meet standards")
                elif criteria == CompletionCriteria.DOCUMENTATION_COMPLETE:
                    actions.append("📚 Complete all documentation")
                elif criteria == CompletionCriteria.ERROR_HANDLING_IMPLEMENTED:
                    actions.append("🛡️ Implement robust error handling")
                elif criteria == CompletionCriteria.INTEGRATION_TESTED:
                    actions.append("🔗 Verify system integration")
                elif criteria == CompletionCriteria.PERFORMANCE_VERIFIED:
                    actions.append("⚡ Verify performance requirements")
        
        if not actions:
            actions.append("🎉 Project is ready for final verification!")
        
        return actions
    
    def generate_completion_report(self, tasks: List[Task]) -> str:
        """Generate a comprehensive completion report"""
        evaluation = self.evaluate_task_completion(tasks)
        
        report = f"""
🎯 PROJECT COMPLETION REPORT
{'='*50}

📊 OVERALL STATUS: {'✅ COMPLETE' if evaluation['is_complete'] else '⚠️ INCOMPLETE'}
📈 Completion: {evaluation['completion_percentage']:.1f}% ({evaluation['required_criteria_met']}/{evaluation['total_required_criteria']})

📋 CRITERIA STATUS:
"""
        
        for criteria, status in evaluation['criteria_status'].items():
            icon = "✅" if status else "❌"
            report += f"{icon} {criteria.value.replace('_', ' ').title()}\n"
        
        if evaluation['blocking_issues']:
            report += f"\n🚫 BLOCKING ISSUES:\n"
            for issue in evaluation['blocking_issues']:
                report += f"   • {issue}\n"
        
        if evaluation['next_actions']:
            report += f"\n🎯 NEXT ACTIONS:\n"
            for action in evaluation['next_actions']:
                report += f"   • {action}\n"
        
        if evaluation['is_complete']:
            report += f"\n🎉 CONGRATULATIONS! Project is truly complete and ready for delivery!"
        else:
            report += f"\n⚠️ DO NOT STOP! Continue working until all criteria are met."
        
        return report
