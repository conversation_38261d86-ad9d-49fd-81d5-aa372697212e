#!/usr/bin/env python3
"""
Test the Enhanced Coaching System

This tests the persistent LLM coaching that prevents the main LLM from stopping
until the project is truly complete.
"""
import asyncio
import sys
import os

# Add the sonnet_model directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from code_generator.services.code_generator import CodeGenerator
from critique_engine.services.critique_engine import CritiqueEngine
from task_manager.services.completion_tracker import ProjectCompletionTracker
from shared.models import GenerationRequest, CritiqueRequest, ProgrammingLanguage, Task, TaskStatus, TaskType
from shared.conversation_state import ConversationStateManager
from utils.config_loader import load_config


async def test_coaching_system():
    """Test the persistent coaching system"""
    print("🧠 Testing Enhanced LLM Coaching System")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    
    # Initialize components
    code_generator = CodeGenerator(config.get("code_generator", {}))
    await code_generator.initialize()
    
    critique_engine = CritiqueEngine(config.get("critique_engine", {}))
    
    completion_tracker = ProjectCompletionTracker(config.get("completion_tracker", {}))
    
    conversation_manager = ConversationStateManager(config.get("conversation_state", {}))
    
    print("✅ All coaching components initialized")
    
    # Test 1: Generate code that might trigger hesitation patterns
    print("\n🎯 Test 1: Code Generation with Potential Hesitation Triggers")
    print("-" * 50)
    
    generation_request = GenerationRequest(
        task_id="coaching-test-001",
        language=ProgrammingLanguage.PYTHON,
        description="Create a complex PyOpenCL matrix multiplication with error handling",
        requirements=[
            "Use PyOpenCL for GPU acceleration",
            "Implement proper error handling",
            "Add comprehensive docstrings",
            "Include performance optimization",
            "Handle edge cases properly"
        ],
        context="This is getting quite complex. Should I continue with this implementation?"  # Hesitation trigger
    )
    
    try:
        generation_result = await code_generator.generate_code(generation_request)
        
        if generation_result.success:
            print("✅ Code generation successful!")
            print(f"📄 Generated {len(generation_result.code)} characters of code")
            
            # Test 2: Critique with coaching enabled
            print("\n🔍 Test 2: Critique with Aggressive Coaching")
            print("-" * 50)
            
            critique_request = CritiqueRequest(
                task_id="coaching-test-001",
                code=generation_result.code,
                language=ProgrammingLanguage.PYTHON,
                categories=set(),
                context="This seems quite involved. Maybe I should stop here and ask if this is sufficient?"  # Hesitation trigger
            )
            
            critique_result = await critique_engine.critique_code(critique_request)
            
            print("✅ Critique with coaching completed!")
            print(f"🔍 Found {len(critique_result.issues)} issues (including coaching)")
            
            # Display coaching messages
            coaching_issues = [issue for issue in critique_result.issues 
                             if "🚀" in issue.title or "🔥" in issue.title or "⚠️" in issue.title]
            
            if coaching_issues:
                print(f"\n💪 COACHING MESSAGES ({len(coaching_issues)}):")
                for issue in coaching_issues:
                    print(f"   {issue.title}")
                    print(f"   📝 {issue.description[:100]}...")
                    print()
            
            # Test 3: Project completion tracking
            print("\n📊 Test 3: Project Completion Tracking")
            print("-" * 50)
            
            # Create some test tasks
            test_tasks = [
                Task(
                    id="task-1",
                    project_id="coaching-test",
                    title="Implement PyOpenCL matrix multiplication",
                    description="Core implementation",
                    status=TaskStatus.COMPLETED,
                    task_type=TaskType.CODE_GENERATION,
                    quality_score=0.85
                ),
                Task(
                    id="task-2", 
                    project_id="coaching-test",
                    title="Add error handling",
                    description="Robust error handling",
                    status=TaskStatus.IN_PROGRESS,  # Not complete!
                    task_type=TaskType.CODE_GENERATION
                ),
                Task(
                    id="task-3",
                    project_id="coaching-test", 
                    title="Write tests",
                    description="Comprehensive testing",
                    status=TaskStatus.PENDING,  # Not started!
                    task_type=TaskType.TESTING
                )
            ]
            
            completion_report = completion_tracker.generate_completion_report(test_tasks)
            print(completion_report)
            
            # Test 4: Conversation state management
            print("\n🔄 Test 4: Conversation State Management")
            print("-" * 50)
            
            # Initialize conversation
            conv_state = conversation_manager.initialize_conversation(
                "coaching-test-conv",
                "PyOpenCL matrix multiplication project with coaching"
            )
            
            # Simulate message exchanges
            for i in range(5):
                conversation_manager.update_message_count(tokens_used=500)
                conversation_manager.add_key_decision(f"Decision {i+1}: Implemented feature X")
            
            progress = conversation_manager.get_progress_summary()
            print(f"📊 Conversation Progress:")
            print(f"   Messages: {progress['message_count']}")
            print(f"   Tokens: {progress['total_tokens']}")
            print(f"   Duration: {progress['duration_minutes']:.1f} minutes")
            print(f"   Needs Reset: {progress['needs_reset']}")
            
            print("\n🎉 COACHING SYSTEM TEST RESULTS:")
            print("=" * 60)
            print("✅ Persistent coaching: ACTIVE")
            print("✅ Hesitation detection: WORKING")
            print("✅ Completion tracking: FUNCTIONAL")
            print("✅ Conversation management: OPERATIONAL")
            print("✅ Expert OpenCL analysis: ENABLED")
            
            print("\n🚀 COACHING SYSTEM IS READY!")
            print("The system will now:")
            print("   • Detect when LLM asks for permission → DENY and push forward")
            print("   • Catch completion claims → VERIFY before allowing")
            print("   • Monitor conversation length → RESET when needed")
            print("   • Track project progress → ENSURE true completion")
            print("   • Provide expert OpenCL/Python guidance")
            
        else:
            print(f"❌ Code generation failed: {generation_result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during coaching test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await code_generator.shutdown()
    
    return True


async def main():
    """Main test function"""
    print("🧪 Enhanced LLM Coaching System Test")
    print("Testing: Persistent coaching, hesitation detection, completion tracking")
    print("=" * 80)
    
    success = await test_coaching_system()
    
    if success:
        print("\n🎉 ALL COACHING TESTS PASSED!")
        print("🚀 The system is now equipped to keep the main LLM working")
        print("   until projects are TRULY complete!")
        return 0
    else:
        print("\n⚠️  Some coaching tests failed. Check the logs above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
