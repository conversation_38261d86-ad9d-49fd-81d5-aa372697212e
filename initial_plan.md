I want to create agentic system which will have very basic three parts.  one part take the input from user as a plan or todo list and maintain it, then it will connect to 2nd part which will be connected to an LLM model to write code, then third part will take output from part 2 and test it and give the feedback to part1 accordingly the part 1 will instruct the part two to write again or correct the mistake. and this loop will continue until final product is ready and all the checklist is ticked main important role will be of part3 a critique feedback giving system.

My special emphasis is on the local llm model for critique, it should be best at  critique 
To implement this system, I will design a modular architecture where each part is implemented as a separate component with clear interfaces. The local LLM model for critique will be fine-tuned to provide detailed and actionable feedback, ensuring the iterative loop leads to a high-quality final product.
The system will be built using Python, FastAPI for the API layer, Redis/SQLite for state management, and Docker for containerization. The Critique Engine will leverage local LLM models like CodeLlama-34B-Instruct, Deepseek-Coder-33B-Instruct, and WizardCoder-34B for providing actionable feedback.
The system will consist of three core components: a Task Manager to manage plans and tasks, a Code Generator to generate code using LLMs, and a Critique Engine to test and provide actionable feedback. The iterative process will continue until all tasks are completed and the final product meets the required standards.
The system will follow a modular architecture with three interconnected components: a Task Manager, a Code Generator, and a Critique Engine. Each component will have clearly defined roles and interfaces to ensure seamless integration and functionality.
The system will be designed to iteratively generate, critique, and refine code until all tasks in the plan are completed and the final product meets the desired quality standards.


## System Architecture

**Part 1: Task Manager/Orchestrator**
- Maintains the todo/plan state
- Tracks completion status of each task
- Routes tasks between components
- Decides when to terminate the loop

**Part 2: Code Generation Agent**
- Interfaces with LLM for code generation
- Takes requirements from Part 1
- Incorporates feedback from Part 3

**Part 3: Critique/Testing Agent** 
- Runs tests on generated code
- Provides detailed feedback
- Most critical component for quality

## Implementation Approach

### Technology Stack
```python
# Core framework
- Python with asyncio for async communication
- Redis/SQLite for state management
- Docker for isolated testing environments
- FastAPI for inter-agent communication
```

Multi-Agent Code Development System: Complete Implementation Plan
Table of Contents

System Overview
Architecture Design
Technology Stack
Development Phases
Component Implementation
Integration Plan
Testing Strategy
Deployment Guide
Monitoring & Maintenance
Troubleshooting

System Overview
Vision Statement
Create a fully autonomous code development system that can take high-level user requirements and produce production-ready code through iterative generation, critique, and refinement cycles.
Core Principles

Modularity: Each component is independently deployable and testable
Robustness: System handles failures gracefully and continues operation
Scalability: Can handle multiple concurrent projects
Quality Focus: Critique system ensures high code quality
Transparency: Full logging and traceability of decisions

Success Metrics

Code quality score (static analysis + critique)
Task completion rate
Average iterations per task
System uptime and reliability
User satisfaction with generated code

Architecture Design
High-Level Architecture
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Task Manager  │    │  Code Generator │    │ Critique Engine │
│    (Part 1)     │◄──►│    (Part 2)     │◄──►│    (Part 3)     │
│                 │    │                 │    │                 │
│ - Plan parsing  │    │ - LLM interface │    │ - Code analysis │
│ - State mgmt    │    │ - Code gen      │    │ - Testing       │
│ - Orchestration │    │ - Refinement    │    │ - Feedback gen  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Message Bus    │
                    │   (Redis/RMQ)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Data Layer    │
                    │ (PostgreSQL +   │
                    │  File Storage)  │
                    └─────────────────┘

## Component Interaction Flow

User Input → Task Manager parses and creates task queue
Task Manager → Sends task to Code Generator
Code Generator → Produces code using LLM
Critique Engine → Analyzes code and provides feedback
Feedback Loop → Continues until quality threshold met
Completion → Final code delivered to user

## Technology Stack

### Core Technologies
yamlProgramming Language: Python 3.11+
Framework: FastAPI + AsyncIO
Message Queue: Redis with Redis Streams
Database: PostgreSQL 15+
Containerization: Docker + Docker Compose
Process Management: Supervisor
Testing: pytest + pytest-asyncio
Logging: structlog + ELK stack
Monitoring: Prometheus + Grafana
AI/ML Stack
yamlLocal LLM Serving: vLLM or Text Generation Inference
Model Format: GGUF for efficient inference
Critique Models: 
  - Primary: CodeLlama-34B-Instruct
  - Fallback: DeepSeek-Coder-33B
  - Lightweight: StarCoder-15B
Static Analysis: 
  - Python: pylint, mypy, bandit, black
  - JavaScript: ESLint, TypeScript compiler
  - General: SonarQube
Infrastructure Requirements
yamlDevelopment Environment:
  - CPU: 16+ cores
  - RAM: 64GB minimum
  - GPU: RTX 4090 (24GB VRAM) or A100
  - Storage: 2TB NVMe SSD

Production Environment:
  - Load Balancer: Nginx
  - Container Orchestration: Docker Swarm or K8s
  - Monitoring: DataDog or New Relic
  - Backup: Automated daily snapshots

## Development Phases
Phase 1: Foundation (Weeks 1-3)
Objectives: Set up core infrastructure and basic components
Week 1: Environment Setup

 Set up development environment
 Configure Docker containers
 Set up database schema
 Configure Redis message bus
 Set up monitoring stack
 Create CI/CD pipeline

Week 2: Core Components Skeleton

 Task Manager basic structure
 Code Generator interface
 Critique Engine framework
 Message passing system
 Basic API endpoints
 Database models and migrations

Week 3: Integration Framework

 Inter-component communication
 State management system
 Error handling framework
 Logging and monitoring
 Configuration management
 Basic unit tests

Phase 2: Core Functionality (Weeks 4-8)
Objectives: Implement main business logic
Week 4-5: Task Manager Implementation

 Natural language plan parsing
 Task queue management
 Progress tracking
 Task prioritization
 Retry mechanisms
 State persistence

Week 6-7: Code Generator Implementation

 LLM integration and optimization
 Prompt engineering system
 Code template management
 Context window optimization
 Multi-language support
 Code formatting and validation

Week 8: Basic Critique Engine

 Static analysis integration
 Basic LLM critique
 Test execution framework
 Feedback generation
 Quality scoring system
 Integration with other components

Phase 3: Advanced Features (Weeks 9-12)
Objectives: Enhance system capabilities and reliability
Week 9-10: Advanced Critique System

 Multi-model critique ensemble
 Security vulnerability scanning
 Performance analysis
 Code smell detection
 Best practices enforcement
 Custom rule engine

Week 11-12: Optimization and Reliability

 Performance optimization
 Caching strategies
 Circuit breakers
 Rate limiting
 Resource management
 Comprehensive error recovery

Phase 4: Production Readiness (Weeks 13-16)
Objectives: Prepare for production deployment
Week 13-14: Testing and Quality Assurance

 Comprehensive test suite
 Integration testing
 Load testing
 Security testing
 User acceptance testing
 Performance benchmarking

Week 15-16: Deployment and Documentation

 Production deployment
 Monitoring setup
 Documentation completion
 User training materials
 Maintenance procedures
 Backup and recovery testing

## Component Implementation
### 1. Task Manager (Part 1)
Directory Structure
task_manager/
├── __init__.py
├── models/
│   ├── __init__.py
│   ├── task.py
│   ├── project.py
│   └── plan.py
├── services/
│   ├── __init__.py
│   ├── parser.py
│   ├── orchestrator.py
│   ├── state_manager.py
│   └── priority_engine.py
├── api/
│   ├── __init__.py
│   ├── routes.py
│   └── schemas.py
├── utils/
│   ├── __init__.py
│   ├── nlp_utils.py
│   └── validation.py
└── tests/
    ├── test_parser.py
    ├── test_orchestrator.py
    └── test_state_manager.py

### 2. Code Generator (Part 2)
Directory Structure
code_generator/
├── __init__.py
├── models/
│   ├── __init__.py
│   ├── generation_request.py
│   ├── generation_result.py
│   └── code_context.py
├── services/
│   ├── __init__.py
│   ├── llm_interface.py
│   ├── prompt_builder.py
│   ├── code_formatter.py
│   └── context_manager.py
├── templates/
│   ├── python/
│   ├── javascript/
│   ├── opencl/
│   ├── C++/
│   ├── fortran/
│   └── general/
├── utils/
│   ├── __init__.py
│   ├── code_utils.py
│   └── validation.py
└── tests/
    ├── test_llm_interface.py
    ├── test_prompt_builder.py
    └── test_code_formatter.py


### 3. Critique Engine (Part 3)
Directory Structure
critique_engine/
├── init.py
├── models/
│   ├── init.py
│   ├── critique_request.py
│   ├── critique_result.py
│   └── analysis_report.py
├── services/
│   ├── init.py
│   ├── static_analyzer.py
│   ├── llm_critic.py
│   ├── test_runner.py
│   ├── security_scanner.py
│   └── performance_analyzer.py
├── analyzers/
│   ├── init.py
│   ├── python_analyzer.py
│   ├── opencl_analyzer.py
│   ├── C++_analyzer.py
│   ├── javascript_analyzer.py
│   ├── fortran_analyzer.py
│   └── generic_analyzer.py
├── rules/
│   ├── init.py
│   ├── quality_rules.py
│   ├── security_rules.py
│   └── performance_rules.py
└── tests/
├── test_static_analyzer.py
├── test_llm_critic.py
└── test_integration.py






## Task Manager Core Models
python# models/task.py
from enum import Enum
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, DateTime, JSON, Enum as SQLEnum

class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    WAITING_REVIEW = "waiting_review"
    NEEDS_REVISION = "needs_revision"
    COMPLETED = "completed"
    FAILED = "failed"

class TaskPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class Task(BaseModel):
    id: str = Field(..., description="Unique task identifier")
    project_id: str = Field(..., description="Parent project ID")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Detailed task description")
    requirements: List[str] = Field(default_factory=list)
    acceptance_criteria: List[str] = Field(default_factory=list)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    assigned_to: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    due_date: Optional[datetime] = None
    dependencies: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    iteration_count: int = Field(default=0)
    max_iterations: int = Field(default=5)
    current_feedback: Optional[str] = None
    generated_code: Optional[str] = None
    test_results: Optional[Dict[str, Any]] = None

class Project(BaseModel):
    id: str = Field(..., description="Unique project identifier")
    name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description")
    language: str = Field(..., description="Primary programming language")
    framework: Optional[str] = None
    tasks: List[Task] = Field(default_factory=list)
    status: str = Field(default="active")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    config: Dict[str, Any] = Field(default_factory=dict)
Plan Parser Service
python# services/parser.py
import re
import spacy
from typing import List, Dict, Any
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.prompts import PromptTemplate

class PlanParser:
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )
        
    async def parse_user_input(self, user_input: str) -> Dict[str, Any]:
        """Parse natural language input into structured tasks"""
        
        # Extract project information
        project_info = await self._extract_project_info(user_input)
        
        # Extract tasks
        tasks = await self._extract_tasks(user_input)
        
        # Extract dependencies
        dependencies = await self._extract_dependencies(user_input, tasks)
        
        # Set priorities
        prioritized_tasks = await self._prioritize_tasks(tasks)
        
        return {
            "project": project_info,
            "tasks": prioritized_tasks,
            "dependencies": dependencies
        }
    
    async def _extract_project_info(self, text: str) -> Dict[str, Any]:
        """Extract project-level information"""
        doc = self.nlp(text)
        
        # Extract language mentions
        languages = self._extract_languages(text)
        frameworks = self._extract_frameworks(text)
        
        return {
            "name": self._extract_project_name(text),
            "language": languages[0] if languages else "python",
            "framework": frameworks[0] if frameworks else None,
            "description": self._extract_description(text)
        }
    
    async def _extract_tasks(self, text: str) -> List[Dict[str, Any]]:
        """Extract individual tasks from text"""
        tasks = []
        
        # Look for bullet points, numbered lists, etc.
        patterns = [
            r'^\d+\.\s+(.+)$',  # Numbered lists
            r'^[-*]\s+(.+)$',   # Bullet points
            r'^TODO:\s*(.+)$',  # TODO items
            r'^TASK:\s*(.+)$'   # Explicit tasks
        ]
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            for pattern in patterns:
                match = re.match(pattern, line, re.IGNORECASE)
                if match:
                    task_text = match.group(1)
                    task = await self._create_task_from_text(task_text)
                    tasks.append(task)
                    break
        
        # If no explicit tasks found, create from sentences
        if not tasks:
            tasks = await self._extract_implicit_tasks(text)
        
        return tasks
    
    async def _create_task_from_text(self, task_text: str) -> Dict[str, Any]:
        """Create task structure from text"""
        doc = self.nlp(task_text)
        
        # Extract requirements and acceptance criteria
        requirements = self._extract_requirements(task_text)
        acceptance_criteria = self._extract_acceptance_criteria(task_text)
        
        return {
            "title": self._extract_task_title(task_text),
            "description": task_text,
            "requirements": requirements,
            "acceptance_criteria": acceptance_criteria,
            "priority": self._infer_priority(task_text)
        }
    
    def _extract_languages(self, text: str) -> List[str]:
        """Extract programming languages mentioned"""
        languages = {
            'python': ['python', 'py', 'django', 'flask', 'fastapi'],
            'javascript': ['javascript', 'js', 'node', 'react', 'vue', 'angular'],
            'java': ['java', 'spring', 'maven', 'gradle'],
            'csharp': ['c#', 'csharp', '.net', 'dotnet'],
            'go': ['go', 'golang'],
            'rust': ['rust', 'cargo'],
            'typescript': ['typescript', 'ts']
        }
        
        text_lower = text.lower()
        found_languages = []
        
        for lang, keywords in languages.items():
            if any(keyword in text_lower for keyword in keywords):
                found_languages.append(lang)
        
        return found_languages
    
    def _extract_frameworks(self, text: str) -> List[str]:
        """Extract frameworks mentioned"""
        frameworks = [
            'django', 'flask', 'fastapi', 'react', 'vue', 'angular',
            'spring', 'express', 'nest', 'laravel', 'rails'
        ]
        
        text_lower = text.lower()
        return [fw for fw in frameworks if fw in text_lower]
    
    def _infer_priority(self, text: str) -> str:
        """Infer task priority from text"""
        high_priority_words = ['urgent', 'critical', 'important', 'asap', 'priority']
        low_priority_words = ['later', 'eventually', 'nice to have', 'optional']
        
        text_lower = text.lower()
        
        if any(word in text_lower for word in high_priority_words):
            return "high"
        elif any(word in text_lower for word in low_priority_words):
            return "low"
        else:
            return "medium"

## Orchestrator Service
python# services/orchestrator.py
import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from .state_manager import StateManager
from ..models.task import Task, TaskStatus, Project

logger = logging.getLogger(__name__)

class TaskOrchestrator:
    def __init__(self, state_manager: StateManager):
        self.state_manager = state_manager
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = 3
        
    async def start_project(self, project: Project) -> str:
        """Start processing a project"""
        logger.info(f"Starting project: {project.name}")
        
        # Save project to state
        await self.state_manager.save_project(project)
        
        # Create orchestration task
        task = asyncio.create_task(self._orchestrate_project(project))
        self.active_tasks[project.id] = task
        
        return project.id
    
    async def _orchestrate_project(self, project: Project):
        """Main orchestration loop for a project"""
        try:
            while True:
                # Get next available tasks
                ready_tasks = await self._get_ready_tasks(project.id)
                
                if not ready_tasks:
                    # Check if project is complete
                    if await self._is_project_complete(project.id):
                        logger.info(f"Project {project.id} completed successfully")
                        break
                    else:
                        # Wait for tasks to become ready
                        await asyncio.sleep(10)
                        continue
                
                # Process tasks with concurrency limit
                semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
                task_coroutines = [
                    self._process_task_with_semaphore(semaphore, task)
                    for task in ready_tasks[:self.max_concurrent_tasks]
                ]
                
                await asyncio.gather(*task_coroutines, return_exceptions=True)
                
        except Exception as e:
            logger.error(f"Error in project orchestration: {str(e)}")
            await self._handle_project_error(project.id, e)
    
    async def _process_task_with_semaphore(self, semaphore: asyncio.Semaphore, task: Task):
        """Process a task with concurrency control"""
        async with semaphore:
            await self._process_single_task(task)
    
    async def _process_single_task(self, task: Task):
        """Process a single task through the generation-critique loop"""
        logger.info(f"Processing task: {task.id}")
        
        try:
            # Update task status
            task.status = TaskStatus.IN_PROGRESS
            task.updated_at = datetime.utcnow()
            await self.state_manager.update_task(task)
            
            # Main processing loop
            for iteration in range(task.max_iterations):
                task.iteration_count = iteration + 1
                
                # Send to code generator
                generation_result = await self._send_to_generator(task)
                
                if not generation_result.success:
                    logger.error(f"Code generation failed for task {task.id}")
                    task.status = TaskStatus.FAILED
                    await self.state_manager.update_task(task)
                    return
                
                task.generated_code = generation_result.code
                task.status = TaskStatus.WAITING_REVIEW
                await self.state_manager.update_task(task)
                
                # Send to critique engine
                critique_result = await self._send_to_critic(task)
                
                if critique_result.passed:
                    # Task completed successfully
                    task.status = TaskStatus.COMPLETED
                    task.updated_at = datetime.utcnow()
                    await self.state_manager.update_task(task)
                    logger.info(f"Task {task.id} completed successfully")
                    return
                else:
                    # Needs revision
                    task.status = TaskStatus.NEEDS_REVISION
                    task.current_feedback = critique_result.feedback
                    await self.state_manager.update_task(task)
                    
                    if iteration < task.max_iterations - 1:
                        logger.info(f"Task {task.id} needs revision, iteration {iteration + 1}")
                    else:
                        logger.warning(f"Task {task.id} failed after {task.max_iterations} iterations")
                        task.status = TaskStatus.FAILED
                        await self.state_manager.update_task(task)
                        return
            
        except Exception as e:
            logger.error(f"Error processing task {task.id}: {str(e)}")
            task.status = TaskStatus.FAILED
            await self.state_manager.update_task(task)
    
    async def _get_ready_tasks(self, project_id: str) -> List[Task]:
        """Get tasks that are ready to be processed"""
        tasks = await self.state_manager.get_project_tasks(project_id)
        ready_tasks = []
        
        for task in tasks:
            if task.status == TaskStatus.PENDING:
                # Check if dependencies are satisfied
                if await self._dependencies_satisfied(task):
                    ready_tasks.append(task)
        
        return ready_tasks
    
    async def _dependencies_satisfied(self, task: Task) -> bool:
        """Check if all task dependencies are completed"""
        if not task.dependencies:
            return True
        
        for dep_id in task.dependencies:
            dep_task = await self.state_manager.get_task(dep_id)
            if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                return False
        
        return True
    
    async def _is_project_complete(self, project_id: str) -> bool:
        """Check if all project tasks are completed"""
        tasks = await self.state_manager.get_project_tasks(project_id)
        return all(task.status == TaskStatus.COMPLETED for task in tasks)
    
    async def _send_to_generator(self, task: Task) -> 'GenerationResult':
        """Send task to code generator"""
        # This will be implemented when integrating with Part 2
        pass
    
    async def _send_to_critic(self, task: Task) -> 'CritiqueResult':
        """Send task to critique engine"""
        # This will be implemented when integrating with Part 3
        pass

##  Code Generator Core Models
python# models/generation_request.py
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class ProgrammingLanguage(str, Enum):
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CSHARP = "csharp"
    GO = "go"
    RUST = "rust"

class GenerationRequest(BaseModel):
    task_id: str = Field(..., description="Task identifier")
    language: ProgrammingLanguage = Field(..., description="Programming language")
    framework: Optional[str] = None
    requirements: List[str] = Field(..., description="Functional requirements")
    acceptance_criteria: List[str] = Field(default_factory=list)
    context: Optional[str] = None
    previous_code: Optional[str] = None
    feedback: Optional[str] = None
    iteration: int = Field(default=1)
    style_guide: Optional[Dict[str, Any]] = None
    dependencies: List[str] = Field(default_factory=list)
    
class GenerationResult(BaseModel):
    task_id: str
    success: bool
    code: Optional[str] = None
    explanation: Optional[str] = None
    files: Dict[str, str] = Field(default_factory=dict)  # filename -> content
    dependencies: List[str] = Field(default_factory=list)
    error_message: Optional[str] = None
    confidence_score: float = Field(default=0.0)
    generation_time: float = Field(default=0.0)
    token_usage: Dict[str, int] = Field(default_factory=dict)
LLM Interface Service
python# services/llm_interface.py
import asyncio
import time
import logging
from typing import Optional, Dict, Any, List
import httpx
from transformers import AutoTokenizer
import torch
from ..models.generation_request import GenerationRequest, GenerationResult
from ..services.prompt_builder import PromptBuilder

logger = logging.getLogger(__name__)

class LLMInterface:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_endpoint = config.get("model_endpoint", "http://localhost:8000")
        self.model_name = config.get("model_name", "codellama/CodeLlama-34b-Instruct-hf")
        self.max_tokens = config.get("max_tokens", 4096)
        self.temperature = config.get("temperature", 0.1)
        self.timeout = config.get("timeout", 300)
        
        # Initialize tokenizer for token counting
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        except Exception as e:
            logger.warning(f"Could not load tokenizer: {e}")
            self.tokenizer = None
        
        self.prompt_builder = PromptBuilder()
        self.client = httpx.AsyncClient(timeout=self.timeout)
    
    async def generate_code(self, request: GenerationRequest) -> GenerationResult:
        """Generate code based on request"""
        start_time = time.time()
        
        try:
            # Build prompt
            prompt = await self.prompt_builder.build_prompt(request)
            
            # Count tokens
            token_count = self._count_tokens(prompt) if self.tokenizer else 0
            
            logger.info(f"Generating code for task {request.task_id}, "
                       f"iteration {request.iteration}, tokens: {token_count}")
            
            # Generate code
            response = await self._call_llm(prompt, request)
            
            if not response.get("success", False):
                return GenerationResult(
                    task_id=request.task_id,
                    success=False,
                    error_message=response.get("error", "Unknown error"),
                    generation_time=time.time() - start_time
                )
            
            # Extract and validate code
            generated_text = response.get("text", "")
            code_result = await self._extract_code(generated_text, request.language)
            
            # Calculate confidence score
            confidence = self._calculate_confidence(generated_text, request)
            
            generation_time = time.time() - start_time
            
            return GenerationResult(
                task_id=request.task_id,
                success=True,
                code=code_result.get("main_code"),
                explanation=code_result.get("explanation"),
                files=code_result.get("files", {}),
                dependencies=code_result.get("dependencies", []),
                confidence_score=confidence,
                generation_time=generation_time,
                token_usage={
                    "prompt_tokens": token_count,
                    "completion_tokens": self._count_tokens(generated_text) if self.tokenizer else 0
                }
            )
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout generating code for task {request.task_id}")
            return GenerationResult(
                task_id=request.task_id,
                success=False,
                error_message="Generation timeout",
                generation_time=time.time() - start_time
            )
        except Exception as e:
            logger.error(f"Error generating code for task {request.task_id}: {str(e)}")
            return GenerationResult(
                task_id=request.task_id,
                success=False,
                error_message=str(e),
                generation_time=time.time() - start_time
            )
    
    async def _call_llm(self, prompt: str, request: GenerationRequest) -> Dict[str, Any]:
        """Call the LLM API"""
        payload = {
            "prompt": prompt,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "stop": ["```", "\n\n---", "# END"],
            "model": self.model_name
        }
        
        try:
            response = await self.client.post(
                f"{self.model_endpoint}/v1/completions",
                json=payload
            )
            response.raise_for_status()
            
            data = response.json()
            
            if "choices" in data and len(data["choices"]) > 0:
                return {
                    "success": True,
                    "text": data["choices"][0]["text"],
                    "usage": data.get("usage", {})
                }
            else:
                return {"success": False, "error": "No response generated"}
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error calling LLM: {str(e)}")
            return {"success": False, "error": f"HTTP error: {str(e)}"}
        except Exception as e:
            logger.error(f"Unexpected error calling LLM: {str(e)}")
            return {"success": False, "error": f"Unexpected error: {str(e)}"}
    
    async def _extract_code(self, generated_text: str, language: str) -> Dict[str, Any]:
        """Extract code blocks and metadata from generated text"""
        import re
        
        # Look for code blocks
        code_pattern = r'```(?:' + language + r')?\n(.*?)\n```'
        code_matches = re.findall(code_pattern, generated_text, re.DOTALL)
        
        if not code_matches:
            # Try to extract code without markdown
            lines = generated_text.split('\n')
            code_lines = []
            in_code = False
            
            for line in lines:
                if any(keyword in line.lower() for keyword in ['def ', 'class ', 'function', 'import']):
                    in_code = True
                
                if in_code:
                    code_lines.append(line)
            
            main_code = '\n'.join(code_lines) if code_lines else generated_text
        else:
            main_code = code_matches[0]
        
        # Extract explanation (text before first code block)
        explanation_match = re.search(r'^(.*?)```', generated_text, re.DOTALL)
        explanation = explanation_match.group(1).strip() if explanation_match else ""
        
        # Extract dependencies
        dependencies = self._extract_dependencies(generated_text, language)
        
        return {
            "main_code": main_code,
            "explanation": explanation,
            "files": {"main": main_code},  # Can be expanded for multi-file generation
            "dependencies": dependencies
        }
    
    def _extract_dependencies(self, text: str, language: str) -> List[str]:
        """Extract dependencies from generated code"""
        dependencies = []
        
        if language == "python":
            import_pattern = r'(?:from\s+([^\s]+)|import\s+([^\s,]+))'RetryClaude does not have the ability to run the code it generates yet.IContinueEditpython           imports = re.findall(import_pattern, text)
           for from_import, direct_import in imports:
               dep = from_import or direct_import
               if dep and not dep.startswith('.') and dep not in ['os', 'sys', 'json', 'datetime']:
                   dependencies.append(dep)
       
       elif language == "javascript" or language == "typescript":
           # Extract npm packages
           require_pattern = r'require\([\'"]([^\'"]+)[\'"]\)'
           import_pattern = r'import.*?from\s+[\'"]([^\'"]+)[\'"]'
           
           requires = re.findall(require_pattern, text)
           imports = re.findall(import_pattern, text)
           
           for dep in requires + imports:
               if not dep.startswith('./') and not dep.startswith('../'):
                   dependencies.append(dep)
       
       return list(set(dependencies))
   
   def _count_tokens(self, text: str) -> int:
       """Count tokens in text"""
       if not self.tokenizer:
           return len(text.split()) * 1.3  # Rough estimate
       
       try:
           return len(self.tokenizer.encode(text))
       except Exception:
           return len(text.split()) * 1.3
   
   def _calculate_confidence(self, generated_text: str, request: GenerationRequest) -> float:
       """Calculate confidence score for generated code"""
       confidence = 0.5  # Base confidence
       
       # Check for code structure
       if any(keyword in generated_text.lower() for keyword in ['def ', 'class ', 'function']):
           confidence += 0.2
       
       # Check for explanations
       if len(generated_text.split('\n')) > 10:
           confidence += 0.1
       
       # Check for error handling
       if any(keyword in generated_text.lower() for keyword in ['try', 'except', 'catch', 'error']):
           confidence += 0.1
       
       # Check if requirements are mentioned
       req_keywords = [req.lower().split() for req in request.requirements]
       flat_keywords = [word for sublist in req_keywords for word in sublist]
       
       mentioned_keywords = sum(1 for keyword in flat_keywords if keyword in generated_text.lower())
       if flat_keywords:
           confidence += 0.1 * (mentioned_keywords / len(flat_keywords))
       
       return min(confidence, 1.0)

## Prompt Builder Service
python# services/prompt_builder.py
import json
from typing import Dict, Any, List
from datetime import datetime
from ..models.generation_request import GenerationRequest, ProgrammingLanguage

class PromptBuilder:
    def __init__(self):
        self.templates = self._load_templates()
        self.style_guides = self._load_style_guides()
    
    async def build_prompt(self, request: GenerationRequest) -> str:
        """Build optimized prompt for code generation"""
        
        # Select appropriate template
        template = self._select_template(request.language, request.framework)
        
        # Build context section
        context_section = self._build_context_section(request)
        
        # Build requirements section
        requirements_section = self._build_requirements_section(request)
        
        # Build feedback section (if iteration > 1)
        feedback_section = self._build_feedback_section(request)
        
        # Build style guide section
        style_section = self._build_style_section(request)
        
        # Build examples section
        examples_section = self._build_examples_section(request)
        
        # Assemble final prompt
        prompt_sections = [
            "# Code Generation Task",
            "",
            context_section,
            requirements_section,
            feedback_section,
            style_section,
            examples_section,
            "",
            "# Instructions",
            template,
            "",
            "Generate clean, well-documented, production-ready code that meets all requirements.",
            "Include error handling and follow best practices.",
            "Provide brief explanations for complex logic.",
            "",
            "# Generated Code:",
            ""
        ]
        
        return "\n".join(filter(None, prompt_sections))
    
    def _build_context_section(self, request: GenerationRequest) -> str:
        """Build context section of prompt"""
        sections = [f"## Context"]
        
        sections.append(f"- **Language**: {request.language.value}")
        
        if request.framework:
            sections.append(f"- **Framework**: {request.framework}")
        
        if request.iteration > 1:
            sections.append(f"- **Iteration**: {request.iteration} (revision)")
        
        if request.context:
            sections.append(f"- **Additional Context**: {request.context}")
        
        return "\n".join(sections)
    
    def _build_requirements_section(self, request: GenerationRequest) -> str:
        """Build requirements section"""
        sections = ["## Requirements"]
        
        for i, req in enumerate(request.requirements, 1):
            sections.append(f"{i}. {req}")
        
        if request.acceptance_criteria:
            sections.append("\n### Acceptance Criteria")
            for i, criteria in enumerate(request.acceptance_criteria, 1):
                sections.append(f"{i}. {criteria}")
        
        return "\n".join(sections)
    
    def _build_feedback_section(self, request: GenerationRequest) -> str:
        """Build feedback section for revisions"""
        if request.iteration <= 1 or not request.feedback:
            return ""
        
        sections = [
            "## Previous Iteration Feedback",
            f"The previous code had issues that need to be addressed:",
            request.feedback,
            "",
            "Please fix these issues in the new implementation."
        ]
        
        if request.previous_code:
            sections.extend([
                "",
                "### Previous Code (for reference):",
                "```" + request.language.value,
                request.previous_code,
                "```"
            ])
        
        return "\n".join(sections)
    
    def _build_style_section(self, request: GenerationRequest) -> str:
        """Build style guide section"""
        style_guide = self.style_guides.get(request.language.value, {})
        
        if not style_guide and not request.style_guide:
            return ""
        
        sections = ["## Style Guidelines"]
        
        # Add language-specific style guidelines
        if style_guide:
            for guideline in style_guide.get("guidelines", []):
                sections.append(f"- {guideline}")
        
        # Add custom style guidelines
        if request.style_guide:
            sections.append("### Additional Style Requirements:")
            for key, value in request.style_guide.items():
                sections.append(f"- {key}: {value}")
        
        return "\n".join(sections)
    
    def _build_examples_section(self, request: GenerationRequest) -> str:
        """Build examples section"""
        template_examples = self.templates.get(request.language.value, {}).get("examples", [])
        
        if not template_examples:
            return ""
        
        sections = ["## Examples"]
        
        for example in template_examples[:2]:  # Limit to 2 examples
            sections.extend([
                f"### {example['title']}",
                "```" + request.language.value,
                example["code"],
                "```",
                ""
            ])
        
        return "\n".join(sections)
    
    def _select_template(self, language: ProgrammingLanguage, framework: str = None) -> str:
        """Select appropriate template based on language and framework"""
        
        # Framework-specific templates
        if framework:
            framework_template = self.templates.get(f"{language.value}_{framework}")
            if framework_template:
                return framework_template["template"]
        
        # Language-specific templates
        lang_template = self.templates.get(language.value, {})
        if lang_template:
            return lang_template.get("template", self._get_default_template())
        
        return self._get_default_template()
    
    def _get_default_template(self) -> str:
        """Get default generation template"""
        return """
Write clean, efficient, and well-documented code that:
1. Implements all specified requirements
2. Follows language best practices
3. Includes proper error handling
4. Has clear variable and function names
5. Includes docstrings/comments for complex logic
6. Is production-ready and maintainable

Format your response as:
```[language]
[your code here]
Then provide a brief explanation of the implementation approach.
""".strip()
def _load_templates(self) -> Dict[str, Any]:
    """Load language and framework-specific templates"""
    return {
        "python": {
            "template": """
Write Python code that:

Follows PEP 8 style guidelines
Uses type hints where appropriate
Includes proper docstrings
Has comprehensive error handling
Uses context managers for resources
Follows the DRY principle

Structure your code with:

Clear function/class definitions
Proper imports at the top
Main execution block if needed
Unit test examples if complex

Format: ```python
[code]
                """.strip(),
                "examples": [
                    {
                        "title": "Function with Error Handling",
                        "code": '''def process_data(data: List[Dict[str, Any]]) -> List[str]:
    """Process input data and return formatted results.
    
    Args:
        data: List of dictionaries containing data to process
        
    Returns:
        List of formatted strings
        
    Raises:
        ValueError: If data format is invalid
    """
    if not data:
        raise ValueError("Data cannot be empty")
    
    results = []
    for item in data:
        try:
            processed = item.get("value", "").strip()
            if processed:
                results.append(f"Processed: {processed}")
        except Exception as e:
            logger.warning(f"Failed to process item {item}: {e}")
            continue
    
    return results'''
                    }
                ]
            },
            "javascript": {
                "template": """
Write JavaScript code that:
1. Uses modern ES6+ syntax
2. Follows consistent naming conventions
3. Includes proper error handling
4. Uses async/await for asynchronous operations
5. Has clear JSDoc comments
6. Is modular and testable

Structure your code with:
- Proper imports/exports
- Clear function definitions
- Error boundaries
- Input validation

Format: ```javascript
[code]
            """.strip(),
            "examples": [
                {
                    "title": "Async Function with Error Handling",
                    "code": '''/**

Fetches and processes user data
@param {string} userId - The user ID to fetch
@returns {Promise<Object>} Processed user data
@throws {Error} If user not found or network error
*/
async function fetchUserData(userId) {
if (!userId) {
throw new Error('User ID is required');
}
try {
const response = await fetch(/api/users/${userId});
 if (!response.ok) {
     throw new Error(`HTTP ${response.status}: ${response.statusText}`);
 }
 
 const userData = await response.json();
 
 return {
     id: userData.id,
     name: userData.name,
     email: userData.email,
     lastActive: new Date(userData.lastActive)
 };
} catch (error) {
console.error('Failed to fetch user data:', error);
throw error;
}
}'''
}
]
}
}
def _load_style_guides(self) -> Dict[str, Any]:
"""Load style guides for different languages"""
return {
"python": {
"guidelines": [
"Use snake_case for variables and functions",
"Use PascalCase for classes",
"Limit lines to 88 characters",
"Use double quotes for strings",
"Add type hints to function signatures",
"Use f-strings for string formatting"
]
},
"javascript": {
"guidelines": [
"Use camelCase for variables and functions",
"Use PascalCase for classes and constructors",
"Use const for immutable values, let for variables",
"Prefer arrow functions for callbacks",
"Use template literals for string interpolation",
"Add JSDoc comments for public functions"
]
}
}

### 

## Critique Engine (Part 3) 

### Critique Engine Core Models

```python
# models/critique_request.py
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum

class CritiqueSeverity(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class CritiqueCategory(str, Enum):
    CORRECTNESS = "correctness"
    PERFORMANCE = "performance"
    SECURITY = "security"
    MAINTAINABILITY = "maintainability"
    STYLE = "style"
    TESTING = "testing"
    DOCUMENTATION = "documentation"

class Issue(BaseModel):
    category: CritiqueCategory
    severity: CritiqueSeverity
    message: str
    line_number: Optional[int] = None
    column: Optional[int] = None
    rule_id: Optional[str] = None
    suggestion: Optional[str] = None
    code_snippet: Optional[str] = None

class CritiqueRequest(BaseModel):
    task_id: str = Field(..., description="Task identifier")
    code: str = Field(..., description="Code to analyze")
    language: str = Field(..., description="Programming language")
    framework: Optional[str] = None
    requirements: List[str] = Field(default_factory=list)
    acceptance_criteria: List[str] = Field(default_factory=list)
    context: Optional[str] = None
    previous_issues: List[Issue] = Field(default_factory=list)
    iteration: int = Field(default=1)
    analysis_config: Dict[str, Any] = Field(default_factory=dict)

class CritiqueResult(BaseModel):
    task_id: str
    passed: bool
    overall_score: float = Field(ge=0, le=100)
    issues: List[Issue] = Field(default_factory=list)
    feedback: str
    suggestions: List[str] = Field(default_factory=list)
    analysis_time: float = Field(default=0.0)
    analysis_details: Dict[str, Any] = Field(default_factory=dict)
    test_results: Optional[Dict[str, Any]] = None
    security_report: Optional[Dict[str, Any]] = None
    performance_metrics: Optional[Dict[str, Any]] = None

### Main Critique Engine
python# services/critique_engine.py
import asyncio
import time
import logging
from typing import List, Dict, Any, Optional
from ..models.critique_request import CritiqueRequest, CritiqueResult, Issue, CritiqueSeverity
from .static_analyzer import StaticAnalyzer
from .llm_critic import LLMCritic
from .test_runner import TestRunner
from .security_scanner import SecurityScanner
from .performance_analyzer import PerformanceAnalyzer

logger = logging.getLogger(__name__)

class CritiqueEngine:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.static_analyzer = StaticAnalyzer(config.get("static_analysis", {}))
        self.llm_critic = LLMCritic(config.get("llm_critic", {}))
        self.test_runner = TestRunner(config.get("test_runner", {}))
        self.security_scanner = SecurityScanner(config.get("security", {}))
        self.performance_analyzer = PerformanceAnalyzer(config.get("performance", {}))
        
        # Quality thresholds
        self.min_score_threshold = config.get("min_score_threshold", 75.0)
        self.max_critical_issues = config.get("max_critical_issues", 0)
        self.max_high_issues = config.get("max_high_issues", 2)
        
    async def critique_code(self, request: CritiqueRequest) -> CritiqueResult:
        """Main critique function that orchestrates all analysis"""
        start_time = time.time()
        logger.info(f"Starting critique for task {request.task_id}, iteration {request.iteration}")
        
        try:
            # Run all analyses in parallel
            analyses = await asyncio.gather(
                self._run_static_analysis(request),
                self._run_llm_critique(request),
                self._run_test_analysis(request),
                self._run_security_analysis(request),
                self._run_performance_analysis(request),
                return_exceptions=True
            )
            
            static_result, llm_result, test_result, security_result, performance_result = analyses
            
            # Combine all results
            all_issues = []
            analysis_details = {}
            
            # Process static analysis
            if isinstance(static_result, dict) and not isinstance(static_result, Exception):
                all_issues.extend(static_result.get("issues", []))
                analysis_details["static_analysis"] = static_result
            
            # Process LLM critique
            if isinstance(llm_result, dict) and not isinstance(llm_result, Exception):
                all_issues.extend(llm_result.get("issues", []))
                analysis_details["llm_critique"] = llm_result
            
            # Process test results
            if isinstance(test_result, dict) and not isinstance(test_result, Exception):
                all_issues.extend(test_result.get("issues", []))
                analysis_details["test_results"] = test_result
            
            # Process security analysis
            if isinstance(security_result, dict) and not isinstance(security_result, Exception):
                all_issues.extend(security_result.get("issues", []))
                analysis_details["security_analysis"] = security_result
            
            # Process performance analysis
            if isinstance(performance_result, dict) and not isinstance(performance_result, Exception):
                all_issues.extend(performance_result.get("issues", []))
                analysis_details["performance_analysis"] = performance_result
            
            # Calculate overall score
            overall_score = self._calculate_overall_score(all_issues, analysis_details)
            
            # Determine if code passes
            passed = self._determine_pass_status(all_issues, overall_score)
            
            # Generate feedback
            feedback = self._generate_feedback(all_issues, analysis_details, request)
            
            # Generate suggestions
            suggestions = self._generate_suggestions(all_issues, analysis_details)
            
            analysis_time = time.time() - start_time
            
            result = CritiqueResult(
                task_id=request.task_id,
                passed=passed,
                overall_score=overall_score,
                issues=all_issues,
                feedback=feedback,
                suggestions=suggestions,
                analysis_time=analysis_time,
                analysis_details=analysis_details,
                test_results=test_result if isinstance(test_result, dict) else None,
                security_report=security_result if isinstance(security_result, dict) else None,
                performance_metrics=performance_result if isinstance(performance_result, dict) else None
            )
            
            logger.info(f"Critique completed for task {request.task_id}: "
                       f"Score={overall_score:.1f}, Passed={passed}, Issues={len(all_issues)}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error during critique of task {request.task_id}: {str(e)}")
            return CritiqueResult(
                task_id=request.task_id,
                passed=False,
                overall_score=0.0,
                issues=[],
                feedback=f"Critique failed due to error: {str(e)}",
                suggestions=["Please check the code for syntax errors and try again."],
                analysis_time=time.time() - start_time
            )
    
    async def _run_static_analysis(self, request: CritiqueRequest) -> Dict[str, Any]:
        """Run static analysis on the code"""
        try:
            return await self.static_analyzer.analyze(request.code, request.language)
        except Exception as e:
            logger.error(f"Static analysis failed: {str(e)}")
            return {"issues": [], "error": str(e)}
    
    async def _run_llm_critique(self, request: CritiqueRequest) -> Dict[str, Any]:
        """Run LLM-based critique"""
        try:
            return await self.llm_critic.critique(request)
        except Exception as e:
            logger.error(f"LLM critique failed: {str(e)}")
            return {"issues": [], "error": str(e)}
    
    async def _run_test_analysis(self, request: CritiqueRequest) -> Dict[str, Any]:
        """Run automated testing"""
        try:
            return await self.test_runner.run_tests(request.code, request.language)
        except Exception as e:
            logger.error(f"Test analysis failed: {str(e)}")
            return {"issues": [], "error": str(e)}
    
    async def _run_security_analysis(self, request: CritiqueRequest) -> Dict[str, Any]:
        """Run security analysis"""
        try:
            return await self.security_scanner.scan(request.code, request.language)
        except Exception as e:
            logger.error(f"Security analysis failed: {str(e)}")
            return {"issues": [], "error": str(e)}
    
    async def _run_performance_analysis(self, request: CritiqueRequest) -> Dict[str, Any]:
        """Run performance analysis"""
        try:
            return await self.performance_analyzer.analyze(request.code, request.language)
        except Exception as e:
            logger.error(f"Performance analysis failed: {str(e)}")
            return {"issues": [], "error": str(e)}
    
    def _calculate_overall_score(self, issues: List[Issue], analysis_details: Dict[str, Any]) -> float:
        """Calculate overall quality score"""
        base_score = 100.0
        
        # Deduct points based on issue severity
        severity_penalties = {
            CritiqueSeverity.CRITICAL: 25.0,
            CritiqueSeverity.HIGH: 10.0,
            CritiqueSeverity.MEDIUM: 5.0,
            CritiqueSeverity.LOW: 1.0
        }
        
        for issue in issues:
            penalty = severity_penalties.get(issue.severity, 1.0)
            base_score -= penalty
        
        # Bonus points for good practices
        if analysis_details.get("static_analysis", {}).get("has_docstrings"):
            base_score += 5.0
        
        if analysis_details.get("static_analysis", {}).get("has_type_hints"):
            base_score += 5.0
        
        if analysis_details.get("test_results", {}).get("coverage", 0) > 80:
            base_score += 10.0
        
        return max(0.0, min(100.0, base_score))
    
    def _determine_pass_status(self, issues: List[Issue], overall_score: float) -> bool:
        """Determine if the code passes quality gates"""
        # Check score threshold
        if overall_score < self.min_score_threshold:
            return False
        
        # Check critical issues
        critical_issues = [i for i in issues if i.severity == CritiqueSeverity.CRITICAL]
        if len(critical_issues) > self.max_critical_issues:
            return False
        
        # Check high severity issues
        high_issues = [i for i in issues if i.severity == CritiqueSeverity.HIGH]
        if len(high_issues) > self.max_high_issues:
            return False
        
        return True
    
    def _generate_feedback(self, issues: List[Issue], analysis_details: Dict[str, Any], 
                          request: CritiqueRequest) -> str:
        """Generate comprehensive feedback"""
        feedback_parts = []
        
        if not issues:
            feedback_parts.append("✅ Excellent work! The code meets all quality standards.")
            return " ".join(feedback_parts)
        
        # Group issues by category
        issues_by_category = {}
        for issue in issues:
            category = issue.category.value
            if category not in issues_by_category:
                issues_by_category[category] = []
            issues_by_category[category].append(issue)
        
        feedback_parts.append("🔍 Code Analysis Results:")
        
        # Critical and high issues first
        critical_issues = [i for i in issues if i.severity == CritiqueSeverity.CRITICAL]
        high_issues = [i for i in issues if i.severity == CritiqueSeverity.HIGH]
        
        if critical_issues:
            feedback_parts.append(f"\n🚨 CRITICAL Issues ({len(critical_issues)}):")
            for issue in critical_issues[:3]:  # Limit to top 3
                feedback_parts.append(f"- {issue.message}")
                if issue.suggestion:
                    feedback_parts.append(f"  💡 {issue.suggestion}")
        
        if high_issues:
            feedback_parts.append(f"\n⚠️  HIGH Priority Issues ({len(high_issues)}):")
            for issue in high_issues[:3]:  # Limit to top 3
                feedback_parts.append(f"- {issue.message}")
                if issue.suggestion:
                    feedback_parts.append(f"  💡 {issue.suggestion}")
        
        # Summary by category
        if len(issues_by_category) > 1:
            feedback_parts.append(f"\n📊 Issues Summary:")
            for category, category_issues in issues_by_category.items():
                feedback_parts.append(f"- {category.title()}: {len(category_issues)} issues")
        
        # Requirements check
        if request.requirements:
            feedback_parts.append(f"\n📋 Requirements Verification:")
            # This would be enhanced with actual requirement checking
            feedback_parts.append("- Please ensure all functional requirements are implemented")
        
        return "\n".join(feedback_parts)
    
    def _generate_suggestions(self, issues: List[Issue], analysis_details: Dict[str, Any]) -> List[str]:
        """Generate improvement suggestions"""
        suggestions = []
        
        # Extract suggestions from issues
        for issue in issues:
            if issue.suggestion and issue.suggestion not in suggestions:
                suggestions.append(issue.suggestion)
        
        # Add general suggestions based on analysis
        if analysis_details.get("static_analysis", {}).get("complexity_score", 0) > 10:
            suggestions.append("Consider breaking down complex functions into smaller, more manageable pieces")
        
        if not analysis_details.get("static_analysis", {}).get("has_docstrings"):
            suggestions.append("Add docstrings to functions and classes for better documentation")
        
        test_coverage = analysis_details.get("test_results", {}).get("coverage", 0)
        if test_coverage < 80:
            suggestions.append(f"Increase test coverage (currently {test_coverage}%) to at least 80%")
        
        return suggestions[:5]  # Limit to top 5 suggestions

### LLM Critic Service
python# services/llm_critic.py
import asyncio
import logging
from typing import Dict, Any, List
import httpx
from ..models.critique_request import CritiqueRequest, Issue, CritiqueSeverity, CritiqueCategory

logger = logging.getLogger(__name__)

class LLMCritic:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_endpoint = config.get("model_endpoint", "http://localhost:8001")
        self.model_name = config.get("model_name", "deepseek-coder-33b-instruct")
        self.temperature = config.get("temperature", 0.1)
        self.max_tokens = config.get("max_tokens", 2048)
        self.timeout = config.get("timeout", 180)
        
        self.client = httpx.AsyncClient(timeout=self.timeout)
    
    async def critique(self, request: CritiqueRequest) -> Dict[str, Any]:
        """Perform LLM-based code critique"""
        logger.info(f"Starting LLM critique for task {request.task_id}")
        
        try:
            # Build critique prompt
            prompt = self._build_critique_prompt(request)
            
            # Call LLM
            response = await self._call_llm(prompt)
            
            if not response.get("success", False):
                return {
                    "issues": [],
                    "error": response.get("error", "LLM critique failed"),
                    "raw_response": ""
                }
            
            # Parse LLM response
            critique_text = response.get("text", "")
            issues = self._parse_critique_response(critique_text)
            
            return {
                "issues": issues,
                "raw_response": critique_text,
                "confidence": self._calculate_confidence(critique_text),
                "analysis_type": "llm_critique"
            }
            
        except Exception as e:
            logger.error(f"LLM critique error: {str(e)}")
            return {
                "issues": [],
                "error": str(e),
                "raw_response": ""
            }
    
    def _build_critique_prompt(self, request: CritiqueRequest) -> str:
        """Build comprehensive critique prompt"""
        
        prompt_sections = [
            "# Code Review and Critique Task",
            "",
            "You are an expert code reviewer. Analyze the following code and provide detailed feedback.",
            "",
            f"## Code to Review ({request.language}):",
            "```" + request.language,
            request.code,
            "```",
            ""
        ]
        
        # Add requirements context
        if request.requirements:
            prompt_sections.extend([
                "## Requirements:",
                ""
            ])
            for i, req in enumerate(request.requirements, 1):
                prompt_sections.append(f"{i}. {req}")
            prompt_sections.append("")
        
        # Add acceptance criteria
        if request.acceptance_criteria:
            prompt_sections.extend([
                "## Acceptance Criteria:",
                ""
            ])
            for i, criteria in enumerate(request.acceptance_criteria, 1):
                prompt_sections.append(f"{i}. {criteria}")
            prompt_sections.append("")
        
        # Add previous issues context
        if request.previous_issues:
            prompt_sections.extend([
                "## Previous Issues to Check:",
                ""
            ])
            for issue in request.previous_issues:
                prompt_sections.append(RetryClaude does not have the ability to run the code it generates yet.IContinueEditpython               prompt_sections.append(f"- {issue.category.value}: {issue.message}")
           prompt_sections.append("")
       
       # Add critique instructions
       prompt_sections.extend([
           "## Review Criteria:",
           "",
           "Analyze the code for:",
           "1. **Correctness**: Does it meet the requirements? Are there logical errors?",
           "2. **Security**: Are there security vulnerabilities or unsafe practices?",
           "3. **Performance**: Are there performance issues or inefficiencies?",
           "4. **Maintainability**: Is the code readable, well-structured, and maintainable?",
           "5. **Best Practices**: Does it follow language/framework best practices?",
           "6. **Error Handling**: Are errors properly handled and edge cases considered?",
           "7. **Testing**: Is the code testable? Are there obvious test cases missing?",
           "",
           "## Response Format:",
           "",
           "For each issue found, use this exact format:",
           "ISSUE: [category] | [severity] | [message]",
           "LINE: [line_number] (if applicable)",
           "SUGGESTION: [specific_suggestion]",
           "",
           "Categories: correctness, security, performance, maintainability, style, testing, documentation",
           "Severities: critical, high, medium, low",
           "",
           "Example:",
           "ISSUE: security | high | SQL injection vulnerability in database query",
           "LINE: 45",
           "SUGGESTION: Use parameterized queries or ORM methods instead of string concatenation",
           "",
           "## Additional Analysis:",
           "",
           "After listing issues, provide:",
           "1. Overall assessment of code quality",
           "2. Most critical issues that must be fixed",
           "3. Suggestions for improvement",
           "",
           "Begin your analysis:"
       ])
       
       return "\n".join(prompt_sections)
   
   async def _call_llm(self, prompt: str) -> Dict[str, Any]:
       """Call the LLM API for critique"""
       payload = {
           "prompt": prompt,
           "max_tokens": self.max_tokens,
           "temperature": self.temperature,
           "stop": ["# END REVIEW", "---"],
           "model": self.model_name
       }
       
       try:
           response = await self.client.post(
               f"{self.model_endpoint}/v1/completions",
               json=payload
           )
           response.raise_for_status()
           
           data = response.json()
           
           if "choices" in data and len(data["choices"]) > 0:
               return {
                   "success": True,
                   "text": data["choices"][0]["text"],
                   "usage": data.get("usage", {})
               }
           else:
               return {"success": False, "error": "No response generated"}
               
       except httpx.HTTPError as e:
           logger.error(f"HTTP error calling LLM critic: {str(e)}")
           return {"success": False, "error": f"HTTP error: {str(e)}"}
       except Exception as e:
           logger.error(f"Unexpected error calling LLM critic: {str(e)}")
           return {"success": False, "error": f"Unexpected error: {str(e)}"}
   
   def _parse_critique_response(self, response_text: str) -> List[Issue]:
       """Parse LLM response into structured issues"""
       import re
       
       issues = []
       lines = response_text.split('\n')
       current_issue = None
       
       for line in lines:
           line = line.strip()
           
           # Match issue pattern
           issue_match = re.match(r'ISSUE:\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*(.+)', line, re.IGNORECASE)
           if issue_match:
               category_str, severity_str, message = issue_match.groups()
               
               # Parse category
               category = self._parse_category(category_str.strip())
               
               # Parse severity
               severity = self._parse_severity(severity_str.strip())
               
               current_issue = Issue(
                   category=category,
                   severity=severity,
                   message=message.strip(),
                   rule_id="llm_critique"
               )
               continue
           
           # Match line number
           line_match = re.match(r'LINE:\s*(\d+)', line, re.IGNORECASE)
           if line_match and current_issue:
               current_issue.line_number = int(line_match.group(1))
               continue
           
           # Match suggestion
           suggestion_match = re.match(r'SUGGESTION:\s*(.+)', line, re.IGNORECASE)
           if suggestion_match and current_issue:
               current_issue.suggestion = suggestion_match.group(1).strip()
               issues.append(current_issue)
               current_issue = None
               continue
       
       # Add current issue if it exists (in case no suggestion was provided)
       if current_issue:
           issues.append(current_issue)
       
       return issues
   
   def _parse_category(self, category_str: str) -> CritiqueCategory:
       """Parse category string to enum"""
       category_map = {
           "correctness": CritiqueCategory.CORRECTNESS,
           "security": CritiqueCategory.SECURITY,
           "performance": CritiqueCategory.PERFORMANCE,
           "maintainability": CritiqueCategory.MAINTAINABILITY,
           "style": CritiqueCategory.STYLE,
           "testing": CritiqueCategory.TESTING,
           "documentation": CritiqueCategory.DOCUMENTATION
       }
       
       return category_map.get(category_str.lower(), CritiqueCategory.MAINTAINABILITY)
   
   def _parse_severity(self, severity_str: str) -> CritiqueSeverity:
       """Parse severity string to enum"""
       severity_map = {
           "critical": CritiqueSeverity.CRITICAL,
           "high": CritiqueSeverity.HIGH,
           "medium": CritiqueSeverity.MEDIUM,
           "low": CritiqueSeverity.LOW
       }
       
       return severity_map.get(severity_str.lower(), CritiqueSeverity.MEDIUM)
   
   def _calculate_confidence(self, response_text: str) -> float:
       """Calculate confidence in the critique"""
       confidence = 0.5  # Base confidence
       
       # Check for structured response
       if "ISSUE:" in response_text:
           confidence += 0.2
       
       # Check for specific suggestions
       if "SUGGESTION:" in response_text:
           confidence += 0.2
       
       # Check for detailed analysis
       if len(response_text.split('\n')) > 10:
           confidence += 0.1
       
       return min(confidence, 1.0)
### Static Analyzer Service
python# services/static_analyzer.py
import ast
import logging
import subprocess
import tempfile
import os
from typing import Dict, Any, List, Optional
from ..models.critique_request import Issue, CritiqueSeverity, CritiqueCategory

logger = logging.getLogger(__name__)

class StaticAnalyzer:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled_tools = config.get("enabled_tools", ["pylint", "mypy", "bandit"])
        self.max_line_length = config.get("max_line_length", 88)
        
    async def analyze(self, code: str, language: str) -> Dict[str, Any]:
        """Perform static analysis on code"""
        logger.info(f"Starting static analysis for {language}")
        
        try:
            if language.lower() == "python":
                return await self._analyze_python(code)
            elif language.lower() in ["javascript", "typescript"]:
                return await self._analyze_javascript(code, language)
            else:
                return await self._analyze_generic(code, language)
                
        except Exception as e:
            logger.error(f"Static analysis failed: {str(e)}")
            return {
                "issues": [],
                "error": str(e),
                "analysis_type": "static_analysis"
            }
    
    async def _analyze_python(self, code: str) -> Dict[str, Any]:
        """Analyze Python code"""
        issues = []
        analysis_details = {}
        
        # AST-based analysis
        try:
            tree = ast.parse(code)
            ast_issues, ast_details = self._analyze_python_ast(tree, code)
            issues.extend(ast_issues)
            analysis_details.update(ast_details)
        except SyntaxError as e:
            issues.append(Issue(
                category=CritiqueCategory.CORRECTNESS,
                severity=CritiqueSeverity.CRITICAL,
                message=f"Syntax error: {str(e)}",
                line_number=e.lineno,
                rule_id="syntax_error"
            ))
        
        # External tools analysis
        if "pylint" in self.enabled_tools:
            pylint_issues = await self._run_pylint(code)
            issues.extend(pylint_issues)
        
        if "mypy" in self.enabled_tools:
            mypy_issues = await self._run_mypy(code)
            issues.extend(mypy_issues)
        
        if "bandit" in self.enabled_tools:
            bandit_issues = await self._run_bandit(code)
            issues.extend(bandit_issues)
        
        # Basic code metrics
        metrics = self._calculate_python_metrics(code)
        analysis_details.update(metrics)
        
        return {
            "issues": issues,
            "analysis_type": "static_analysis",
            "language": "python",
            "metrics": metrics,
            **analysis_details
        }
    
    def _analyze_python_ast(self, tree: ast.AST, code: str) -> tuple:
        """Analyze Python AST for issues"""
        issues = []
        details = {}
        
        # Collect various metrics and issues
        class CodeAnalyzer(ast.NodeVisitor):
            def __init__(self):
                self.functions = []
                self.classes = []
                self.complexity = 0
                self.imports = []
                self.has_docstrings = False
                self.has_type_hints = False
                self.long_functions = []
                
            def visit_FunctionDef(self, node):
                self.functions.append(node.name)
                
                # Check for docstring
                if (node.body and isinstance(node.body[0], ast.Expr) and
                    isinstance(node.body[0].value, ast.Str)):
                    self.has_docstrings = True
                
                # Check for type hints
                if node.returns or any(arg.annotation for arg in node.args.args):
                    self.has_type_hints = True
                
                # Check function length
                if len(node.body) > 20:
                    self.long_functions.append((node.name, len(node.body), node.lineno))
                
                # Calculate complexity
                complexity = self._calculate_complexity(node)
                if complexity > 10:
                    issues.append(Issue(
                        category=CritiqueCategory.MAINTAINABILITY,
                        severity=CritiqueSeverity.MEDIUM,
                        message=f"Function '{node.name}' has high complexity ({complexity})",
                        line_number=node.lineno,
                        suggestion="Consider breaking this function into smaller functions",
                        rule_id="high_complexity"
                    ))
                
                self.generic_visit(node)
                
            def visit_ClassDef(self, node):
                self.classes.append(node.name)
                
                # Check for docstring
                if (node.body and isinstance(node.body[0], ast.Expr) and
                    isinstance(node.body[0].value, ast.Str)):
                    self.has_docstrings = True
                
                self.generic_visit(node)
                
            def visit_Import(self, node):
                for alias in node.names:
                    self.imports.append(alias.name)
                self.generic_visit(node)
                
            def visit_ImportFrom(self, node):
                if node.module:
                    for alias in node.names:
                        self.imports.append(f"{node.module}.{alias.name}")
                self.generic_visit(node)
            
            def _calculate_complexity(self, node):
                """Calculate cyclomatic complexity"""
                complexity = 1  # Base complexity
                
                for child in ast.walk(node):
                    if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                        complexity += 1
                    elif isinstance(child, ast.Try):
                        complexity += len(child.handlers)
                    elif isinstance(child, (ast.And, ast.Or)):
                        complexity += 1
                
                return complexity
        
        analyzer = CodeAnalyzer()
        analyzer.visit(tree)
        
        # Add issues for long functions
        for func_name, length, line_no in analyzer.long_functions:
            issues.append(Issue(
                category=CritiqueCategory.MAINTAINABILITY,
                severity=CritiqueSeverity.LOW,
                message=f"Function '{func_name}' is too long ({length} lines)",
                line_number=line_no,
                suggestion="Consider breaking this function into smaller functions",
                rule_id="long_function"
            ))
        
        # Check for missing docstrings
        if not analyzer.has_docstrings and (analyzer.functions or analyzer.classes):
            issues.append(Issue(
                category=CritiqueCategory.DOCUMENTATION,
                severity=CritiqueSeverity.LOW,
                message="Missing docstrings for functions or classes",
                suggestion="Add docstrings to document your code",
                rule_id="missing_docstrings"
            ))
        
        details = {
            "function_count": len(analyzer.functions),
            "class_count": len(analyzer.classes),
            "import_count": len(analyzer.imports),
            "has_docstrings": analyzer.has_docstrings,
            "has_type_hints": analyzer.has_type_hints,
            "functions": analyzer.functions,
            "classes": analyzer.classes,
            "imports": analyzer.imports
        }
        
        return issues, details
    
    async def _run_pylint(self, code: str) -> List[Issue]:
        """Run pylint analysis"""
        issues = []
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            result = subprocess.run(
                ['pylint', '--output-format=json', temp_file],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.stdout:
                import json
                pylint_results = json.loads(result.stdout)
                
                for item in pylint_results:
                    severity = self._map_pylint_severity(item.get('type', 'info'))
                    category = self._map_pylint_category(item.get('message-id', ''))
                    
                    issues.append(Issue(
                        category=category,
                        severity=severity,
                        message=item.get('message', ''),
                        line_number=item.get('line'),
                        column=item.get('column'),
                        rule_id=item.get('message-id', 'pylint'),
                        suggestion=self._get_pylint_suggestion(item.get('message-id', ''))
                    ))
            
        except Exception as e:
            logger.warning(f"Pylint analysis failed: {str(e)}")
        finally:
            if 'temp_file' in locals():
                os.unlink(temp_file)
        
        return issues
    
    async def _run_mypy(self, code: str) -> List[Issue]:
        """Run mypy type checking"""
        issues = []
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            result = subprocess.run(
                ['mypy', '--show-error-codes', temp_file],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.stdout:
                for line in result.stdout.split('\n'):
                    if ':' in line and 'error:' in line:
                        parts = line.split(':')
                        if len(parts) >= 4:
                            line_no = int(parts[1]) if parts[1].isdigit() else None
                            message = ':'.join(parts[3:]).strip()
                            
                            issues.append(Issue(
                                category=CritiqueCategory.CORRECTNESS,
                                severity=CritiqueSeverity.MEDIUM,
                                message=f"Type error: {message}",
                                line_number=line_no,
                                rule_id="mypy_type_error"
                            ))
            
        except Exception as e:
            logger.warning(f"Mypy analysis failed: {str(e)}")
        finally:
            if 'temp_file' in locals():
                os.unlink(temp_file)
        
        return issues
    
    async def _run_bandit(self, code: str) -> List[Issue]:
        """Run bandit security analysis"""
        issues = []
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            result = subprocess.run(
                ['bandit', '-f', 'json', temp_file],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.stdout:
                import json
                bandit_results = json.loads(result.stdout)
                
                for item in bandit_results.get('results', []):
                    severity = self._map_bandit_severity(item.get('issue_severity', 'LOW'))
                    
                    issues.append(Issue(
                        category=CritiqueCategory.SECURITY,
                        severity=severity,
                        message=item.get('issue_text', ''),
                        line_number=item.get('line_number'),
                        rule_id=item.get('test_id', 'bandit'),
                        suggestion="Review this security issue and apply appropriate fixes"
                    ))
            
        except Exception as e:
            logger.warning(f"Bandit analysis failed: {str(e)}")
        finally:
            if 'temp_file' in locals():
                os.unlink(temp_file)
        
        return issues
    
    def _calculate_python_metrics(self, code: str) -> Dict[str, Any]:
        """Calculate basic Python code metrics"""
        lines = code.split('\n')
        
        return {
            "total_lines": len(lines),
            "non_empty_lines": len([line for line in lines if line.strip()]),
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "avg_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0,
            "max_line_length": max(len(line) for line in lines) if lines else 0,
            "long_lines": len([line for line in lines if len(line) > self.max_line_length])
        }
    
    def _map_pylint_severity(self, pylint_type: str) -> CritiqueSeverity:
        """Map pylint message type to severity"""
        mapping = {
            'error': CritiqueSeverity.HIGH,
            'warning': CritiqueSeverity.MEDIUM,
            'refactor': CritiqueSeverity.LOW,
            'convention': CritiqueSeverity.LOW,
            'info': CritiqueSeverity.LOW
        }
        return mapping.get(pylint_type.lower(), CritiqueSeverity.LOW)
    
    def _map_pylint_category(self, message_id: str) -> CritiqueCategory:
        """Map pylint message ID to category"""
        if message_id.startswith('E'):
            return CritiqueCategory.CORRECTNESS
        elif message_id.startswith('W'):
            return CritiqueCategory.MAINTAINABILITY
        elif message_id.startswith('R'):
            return CritiqueCategory.MAINTAINABILITY
        elif message_id.startswith('C'):
            return CritiqueCategory.STYLE
        else:
            return CritiqueCategory.MAINTAINABILITY
    
    def _map_bandit_severity(self, bandit_severity: str) -> CritiqueSeverity:
        """Map bandit severity to our severity"""
        mapping = {
            'HIGH': CritiqueSeverity.HIGH,
            'MEDIUM': CritiqueSeverity.MEDIUM,
            'LOW': CritiqueSeverity.LOW
        }
        return mapping.get(bandit_severity.upper(), CritiqueSeverity.LOW)
    
    def _get_pylint_suggestion(self, message_id: str) -> Optional[str]:
        """Get suggestion for pylint message ID"""
        suggestions = {
            'C0103': 'Use snake_case for variable names',
            'C0111': 'Add docstring to function/class',
            'W0613': 'Remove unused argument or prefix with underscore',
            'R0903': 'Consider adding more methods or merge with another class',
            'R0913': 'Reduce number of arguments or use a data structure'
        }
        return suggestions.get(message_id)
    
    async def _analyze_javascript(self, code: str, language: str) -> Dict[str, Any]:
        """Analyze JavaScript/TypeScript code"""
        # This would integrate with ESLint, TSC, etc.
        # For now, return basic analysis
        return {
            "issues": [],
            "analysis_type": "static_analysis",
            "language": language,
            "note": "JavaScript analysis not fully implemented"
        }
    
    async def _analyze_generic(self, code: str, language: str) -> Dict[str, Any]:
        """Generic code analysis"""
        issues = []
        
        # Basic checks that apply to most languages
        lines = code.split('\n')
        
        for i, line in enumerate(lines, 1):
            # Check line length
            if len(line) > 120:
                issues.append(Issue(
                    category=CritiqueCategory.STYLE,
                    severity=CritiqueSeverity.LOW,
                    message=f"Line too long ({len(line)} characters)",
                    line_number=i,
                    suggestion="Consider breaking long lines for better readability",
                    rule_id="line_too_long"
                ))
            
            # Check for TODO/FIXME comments
            if 'TODO' in line.upper() or 'FIXME' in line.upper():
                issues.append(Issue(
                    category=CritiqueCategory.MAINTAINABILITY,
                    severity=CritiqueSeverity.LOW,
                    message="TODO/FIXME comment found",
                    line_number=i,
                    suggestion="Complete the TODO item or create a proper issue",
                    rule_id="todo_comment"
                ))
        
        return {
            "issues": issues,
            "analysis_type": "static_analysis",
            "language": language,
            "total_lines": len(lines)
        }

### Integration Plan
#### Message Bus Implementation
python# shared/message_bus.py
import asyncio
import json
import logging
from typing import Dict, Any, Callable, Optional
import redis.asyncio as redis
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class Message:
    id: str
    type: str
    payload: Dict[str, Any]
    timestamp: datetime
    sender: str
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None

class MessageBus:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis = None
        self.subscribers: Dict[str, Callable] = {}
        self.running = False
        
    async def connect(self):
        """Connect to Redis"""
        self.redis = await redis.from_url(self.redis_url)
        logger.info("Connected to Redis message bus")
        
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis:
            await self.redis.close()
            logger.info("Disconnected from Redis message bus")
    
    async def publish(self, channel: str, message: Message) -> bool:
        """Publish message to channel"""
        try:
            message_data = {
                "id": message.id,
                "type": message.type,
                "payload": message.payload,
                "timestamp": message.timestamp.isoformat(),
                "sender": message.sender,
                "correlation_id": message.correlation_id,
                "reply_to": message.reply_to
            }
            
            await self.redis.xadd(channel, message_data)
            logger.debug(f"Published message {message.id} to {channel}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish message: {str(e)}")
            return False
    
    async def subscribe(self, channel: str, handler: Callable[[Message], None]):
        """Subscribe to channel with handler"""
        self.subscribers[channel] = handler
        logger.info(f"Subscribed to channel {channel}")
    
    async def start_consuming(self):
        """Start consuming messages from subscribed channels"""
        self.running = True
        
        while self.running:
            try:
                # Read from all subscribed channels
                for channel in self.subscribers.keys():
                    messages = await self.redis.xread({channel: "$"}, block=1000, count=10)
                    
                    for stream, msgs in messages:
                        for msg_id, fields in msgs:
                            try:
                                message = self._parse_message(fields)
                                handler = self.subscribers[channel]
                                await handler(message)
                                
                                # Acknowledge message
                                await self.redis.xack(channel, "consumer_group", msg_id)
                                
                            except Exception as e:
                                logger.error(f"Error processing message {msg_id}: {str(e)}")
                                
            except Exception as e:
                logger.error(f"Error in message consumption: {str(e)}")
                await asyncio.sleep(1)
    
    def _parse_message(self, fields: Dict[bytes, bytes]) -> Message:
        """Parse Redis message fields to Message object"""
        decoded_fields = {k.decode(): v.decode() for k, v in fields.items()}
        
        return Message(
            id=decoded_fields["id"],
            type=decoded_fields["type"],
            payload=json.loads(decoded_fields["payload"]),
            timestamp=datetime.fromisoformat(decoded_fields["timestamp"]),
            sender=decoded_fields["sender"],
            correlation_id=decoded_fields.get("correlation_id"),
            reply_to=decoded_fields.get("reply_to")
        )
    
    async def stop_consuming(self):
        """Stop consuming messages"""
        self.running = False
        logger.info("Stopped message consumption")
API Gateway
python# api/main.py
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime

from task_manager.services.orchestrator import TaskOrchestrator
from task_manager.services.parser import PlanParser
from task_manager.models.task import Project, Task
from shared.message_bus import MessageBus, Message

app = FastAPI(title="Multi-Agent Code Development System", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class ProjectRequest(BaseModel):
    name: str
    description: str
    user_input: str
    language: Optional[str] = "python"
    framework: Optional[str] = None

class ProjectResponse(BaseModel):
    id: str
    name: str
    status: str
    tasks: List[Dict[str, Any]]
    created_at: datetime

class TaskStatusResponse(BaseModel):
    task_id: str
    status: str
    progress: float
    current_iteration: int
    issues: List[Dict[str, Any]]
    generated_code: Optional[str] = None

# Global instances
message_bus = MessageBus()
plan_parser = PlanParser()
orchestrator = TaskOrchestrator(None)  # Will be initialized with state manager

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    await message_bus.connect()
    # Initialize other services
    
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    await message_bus.disconnect()

@app.post("/projects", response_model=ProjectResponse)
async def create_project(project_request: ProjectRequest, background_tasks: BackgroundTasks):
    """Create a new project from user input"""
    try:
        # Parse user input
        parsed_data = await plan_parser.parse_user_input(project_request.user_input)
        
        # Create project
        project = Project(
            id=str(uuid.uuid4()),
            name=project_request.name,
            description=project_request.description,
            language=project_request.language,
            framework=project_request.framework,
            tasks=[
                Task(
                    id=str(uuid.uuid4()),
                    project_id="",  # Will be set after project creation
                    **task_data
                ) for task_data in parsed_data["tasks"]
            ]
        )
        
        # Set project_id for tasks
        for task in project.tasks:
            task.project_id = project.id
        
        # Start project processing in background
        background_tasks.add_task(orchestrator.start_project, project)
        
        return ProjectResponse(
            id=project.id,
            name=project.name,
            status="active",
            tasks=[task.dict() for task in project.tasks],
            created_at=project.created_at
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/projects/{project_id}")
async def get_project(project_id: str):
    """Get project details and status"""
    # This would fetch from state manager
    pass

@app.get("/projects/{project_id}/tasks")
async def get_project_tasks(project_id: str):
    """Get all tasks for a project"""
    # This would fetch from state manager
    pass

@app.get("/tasks/{task_id}/status", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """Get detailed task status"""
    # This would fetch from state manager
    pass

@app.get("/tasks/{task_id}/code")
async def get_task_code(task_id: str):
    """Get generated code for a task"""
    # This would fetch from state manager
    pass

@app.post("/tasks/{task_id}/feedback")
async def provide_feedback(task_id: str, feedback: Dict[str, Any]):
    """Provide user feedback on a task"""
    # This would update the task and potentially restart processing
    pass

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

if __Retry


    

