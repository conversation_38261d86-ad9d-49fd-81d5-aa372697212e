name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_USER: agent
          POSTGRES_PASSWORD: agentpass
          POSTGRES_DB: agent_db
        ports: ["5432:5432"]
        options: >-
          --health-cmd "pg_isready -U agent" --health-interval 10s --health-timeout 5s --health-retries 5

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    - name: Install deps
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Lint & Tests
      run: |
        black --check .
        isort --check .
        pylint $(git ls-files '*.py')
        pytest -q
