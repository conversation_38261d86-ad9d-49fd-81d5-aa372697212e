from fastapi import APIRouter, Depends, HTTPException
from services.orchestrator import TaskOrchestrator
from models.project import ProjectRequest, ProjectResponse
from shared.dependencies import get_orchestrator

router = APIRouter(prefix="/projects", tags=["projects"])

@router.post("/", response_model=ProjectResponse)
async def create_project(req: ProjectRequest, orchestrator: TaskOrchestrator = Depends(get_orchestrator)):
    project_id = await orchestrator.create_project(req)
    return ProjectResponse(project_id=project_id, message="Project created")
