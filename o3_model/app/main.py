from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from shared.logger import init_logging
from app.routers import router

init_logging()

app = FastAPI(title="Local Agentic Code Development System", version="0.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(router)

@app.get("/health", tags=["system"])
async def health_check():
    return {"status": "ok"}
