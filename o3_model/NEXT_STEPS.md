# Future Improvements

1. Persistence Layer
   * Replace in-memory `StateManager` with Postgres (SQLAlchemy async ORM).
   * Migrations via Alembic.
2. Message Bus
   * Implement `shared/message_bus.py` using Redis Streams.
   * Back-pressure & consumer groups.
3. Critique Ensemble Enhancements
   * MoE gating between multiple local models.
   * Automatic prompt tuning (PEFT/LoRA).
4. Auto-Retry Logic
   * Task fails → tweak prompt via reinforcement heuristic.
5. UI Dashboard
   * React/Next.js front-end with WebSocket progress updates.
6. CI/CD
   * GitHub Actions → lint, test, Docker build & push.
7. Observability
   * Prometheus metrics, Grafana dashboards.
