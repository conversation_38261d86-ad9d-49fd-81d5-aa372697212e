from typing import List, Optional
from uuid import UUID, uuid4
from pydantic import BaseModel, Field

class ProjectRequest(BaseModel):
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field("", description="Short description")
    user_input: str = Field(..., description="User provided plan / todo list")
    language: Optional[str] = Field("python", description="Primary programming language")

class ProjectResponse(BaseModel):
    project_id: UUID
    message: str

class Task(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    project_id: UUID
    description: str
    status: str = "pending"  # pending, in_progress, done, failed
    iteration: int = 0
    feedback: Optional[str] = None

class Project(BaseModel):
    id: UUID = Field(default_factory=uuid4)
    name: str
    description: str
    language: str = "python"
    tasks: List[Task] = []
