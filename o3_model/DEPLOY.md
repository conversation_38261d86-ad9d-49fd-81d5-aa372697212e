# Deploying the Local Agentic System

## Prerequisites
* Ubuntu 22.04 (or similar)
* NVIDIA GPU (24 GB VRAM+) with CUDA 12
* Docker & docker-compose
* git-lfs

```bash
sudo apt update && sudo apt install -y docker.io docker-compose git-lfs
sudo systemctl enable --now docker
```

## Clone & Build
```bash
git clone <repo> && cd local_agent/o3_model
# Pull/quantise models (default GPTQ 34B)
./scripts/download_models.sh 34B
# Launch stack
docker-compose up -d --build
```

Endpoints:
* API -> http://localhost:8000
* vLLM -> http://localhost:8001
* Swagger -> http://localhost:8000/docs

## Memory Optimisation
* Use `--gguf` conversion plus `llama.cpp` (`llama.cpp -ngl 35 -c 4096`).
* For <16 GB VRAM cards, switch `MODEL_NAME=codellama/CodeLlama-13B-GPTQ` in `docker-compose.yml`.
* Enable KV-cache on disk via vLLM `--gpu-memory-utilization 0.8`.

## Quantisation Options
| Method | VRAM | Throughput | Quality |
|--------|------|------------|---------|
| GPTQ 4-bit | 12 GB | ★★★ | 97 % |
| GGUF q5_K_M | 10 GB | ★★ | 95 % |
| AWQ 3-bit | 18 GB | ★★★★ | 98 % |

Scripts to convert located in `scripts/`.

## Scaling Out
* Deploy Redis & Postgres to managed services.
* Run multiple `api` replicas behind Nginx.
* For high-QPS, use `text-generation-inference` with tensor-parallel GPUs.

---

For a step-by-step beginner tutorial, see `README.md` Quick-Start section.
