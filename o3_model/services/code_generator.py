import logging
from http import HTTPStatus

import httpx

logger = logging.getLogger(__name__)

class CodeGenerator:
    def __init__(self, endpoint: str = "http://localhost:8001/v1/chat/completions"):
        self.endpoint = endpoint

    async def generate_code(self, task_desc: str) -> str:
        prompt = f"""You are a senior software engineer. Write code that fulfils the following requirement:\n{task_desc}\nReturn only code blocks."""
        async with httpx.AsyncClient(timeout=120) as client:
            resp = await client.post(self.endpoint, json={
                "model": "codellama",
                "messages": [{"role": "user", "content": prompt}],
            })
        if resp.status_code != HTTPStatus.OK:
            logger.error("LLM error %s", resp.text)
            return ""
        return resp.json()["choices"][0]["message"]["content"]
