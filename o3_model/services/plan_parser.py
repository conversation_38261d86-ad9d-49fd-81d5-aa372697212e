"""PlanPars<PERSON> converts a natural-language TODO/plan into atomic Task objects.
Currently uses spaCy sentence segmentation; can be swapped for LLM-based chunking later."""
from typing import List
import spacy
from models.project import Task, Project

nlp = spacy.load("en_core_web_sm")

class PlanParser:
    def parse(self, text: str, project_id) -> List[Task]:
        doc = nlp(text)
        tasks: List[Task] = []
        for sent in doc.sents:
            content = sent.text.strip()
            if content:
                tasks.append(Task(project_id=project_id, description=content))
        return tasks
