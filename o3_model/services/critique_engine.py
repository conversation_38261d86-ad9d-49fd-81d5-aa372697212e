import logging
import re
from typing import Dict

from services.static_analyzer import StaticAnalyzer
from services.llm_critic import LLMCritic

logger = logging.getLogger(__name__)

class CritiqueEngine:
    def __init__(self):
        self.static_analyzer = StaticAnalyzer()
        self.llm_critic = LLMCritic()

    async def run_critique(self, code: str, task_desc: str) -> Dict[str, str]:
        issues = self.static_analyzer.analyze(code)
        llm_feedback = await self.llm_critic.critique(code, task_desc)
        passed = not issues and llm_feedback.get("pass", False)
        feedback_text = "\n".join(i["message"] for i in issues) + "\n" + llm_feedback.get("feedback", "")
        return {"pass": passed, "feedback": feedback_text.strip()}
