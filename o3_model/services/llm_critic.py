import logging
from http import HTTPStatus

import httpx

logger = logging.getLogger(__name__)

class LLMCritic:
    def __init__(self, endpoint: str = "http://localhost:8001/v1/chat/completions"):
        self.endpoint = endpoint

    async def critique(self, code: str, task_desc: str):
        prompt = f"""You are a code reviewer. Evaluate the code below against the requirement: {task_desc}.\nCode:\n```python\n{code}\n```\nRespond with JSON {{'pass': <bool>, 'feedback': <text>}}"""
        async with httpx.AsyncClient(timeout=120) as client:
            resp = await client.post(self.endpoint, json={
                "model": "codellama",
                "messages": [{"role": "user", "content": prompt}],
            })
        if resp.status_code != HTTPStatus.OK:
            logger.error("LLM critic error %s", resp.text)
            return {"pass": False, "feedback": "LLM error"}
        import json
        try:
            return json.loads(resp.json()["choices"][0]["message"]["content"])
        except Exception as e:
            logger.error("Parsing critic response failed: %s", e)
            return {"pass": False, "feedback": "Bad critic response"}
