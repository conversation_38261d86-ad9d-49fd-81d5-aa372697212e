import asyncio
import logging
from uuid import uuid4
from typing import List, Dict, Any

from models.project import ProjectRequest, Project, Task
from shared.state_manager import StateManager
from services.code_generator import CodeGenerator
from services.critique_engine import CritiqueEngine
from services.plan_parser import PlanParser
from shared.message_bus import MessageBus

logger = logging.getLogger(__name__)

class TaskOrchestrator:
    def __init__(self, state_manager: StateManager):
        self.state_manager = state_manager
        self.code_gen = CodeGenerator()
        self.critic = CritiqueEngine()
        self.plan_parser = PlanParser()
        self.bus = MessageBus()
        self.max_concurrent_tasks = 3
        self.active: Dict[str, asyncio.Task] = {}

    async def create_project(self, req: ProjectRequest):
        project = Project(name=req.name, description=req.description, language=req.language)
        await self.state_manager.add_project(project)
        # Very naive split: each line of user_input becomes a task
        tasks = self.plan_parser.parse(req.user_input, project.id)
        await self.state_manager.add_tasks(project.id, tasks)
        asyncio.create_task(self._process_project(project.id))
        return project.id

    async def _process_project(self, project_id):
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        while True:
            tasks = await self.state_manager.get_project_tasks(project_id)
            pending = [t for t in tasks if t.status == "pending"]
            if not pending:
                break
            await asyncio.gather(*(self._process_task(t, semaphore) for t in pending))
            await asyncio.sleep(1)
        logger.info("Project %s completed", project_id)

    async def _process_task(self, task: Task, sem: asyncio.Semaphore):
        async with sem:
            task.status = "in_progress"
            await self.state_manager.update_task(task.project_id, task)
            code = await self.code_gen.generate_code(task.description)
            critique = await self.critic.run_critique(code, task.description)
            if critique["pass"]:
                task.status = "done"
            else:
                task.status = "failed"
                task.feedback = critique["feedback"]
            await self.state_manager.update_task(task.project_id, task)
