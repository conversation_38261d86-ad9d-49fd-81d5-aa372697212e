from typing import List
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from models.project import Project, Task
from shared.db import get_session

class StateManager:
    """Async Postgres-backed state manager using SQLAlchemy."""

    async def add_project(self, project: Project):
        async with get_session() as session:
            session.add(project)
            await session.commit()

    async def get_project(self, project_id: UUID) -> Project:
        async with get_session() as session:
            res = await session.get(Project, project_id)
            return res

    async def add_tasks(self, project_id: UUID, tasks: List[Task]):
        async with get_session() as session:
            for t in tasks:
                session.add(t)
            await session.commit()

    async def update_task(self, project_id: UUID, task: Task):
        async with get_session() as session:
            await session.merge(task)
            await session.commit()

    async def get_project_tasks(self, project_id: UUID) -> List[Task]:
        async with get_session() as session:
            result = await session.execute(select(Task).where(Task.project_id == project_id))
            return result.scalars().all()
