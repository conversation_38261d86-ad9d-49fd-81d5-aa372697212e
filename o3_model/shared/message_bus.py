import json
import asyncio
from typing import Any, Callable, Dict

import aioredis

class MessageBus:
    def __init__(self, url: str = "redis://localhost:6379", stream: str = "agent_stream"):
        self.url = url
        self.stream = stream
        self.redis: aioredis.Redis | None = None

    async def connect(self):
        if self.redis is None:
            self.redis = await aioredis.from_url(self.url, decode_responses=True)

    async def publish(self, message: Dict[str, Any]):
        await self.connect()
        await self.redis.xadd(self.stream, {"data": json.dumps(message)})

    async def subscribe(self, cb: Callable[[Dict[str, Any]], None]):
        await self.connect()
        last_id = "0-0"
        while True:
            resp = await self.redis.xread({self.stream: last_id}, block=0)
            for _, messages in resp:
                for msg_id, data in messages:
                    last_id = msg_id
                    payload = json.loads(data["data"])
                    await cb(payload)
