version: "3.9"

services:
  api:
    build: .
    container_name: local_agent_api
    command: ["bash", "-c", "uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"]
    env_file:
      - .env
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - postgres
      - vllm
    volumes:
      - ./:/usr/src/app

  redis:
    image: redis:7-alpine
    container_name: local_agent_redis
    ports:
      - "6379:6379"

  postgres:
    image: postgres:16-alpine
    container_name: local_agent_postgres
    environment:
      POSTGRES_USER: agent
      POSTGRES_PASSWORD: agentpass
      POSTGRES_DB: agent_db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  vllm:
    image: vllm/vllm-openai:latest
    container_name: local_agent_vllm
    environment:
      - MODEL_NAME=TheBloke/CodeLlama-34B-Instruct-GPTQ
      - GPU_MEMORY_UTILIZATION=0.9
    ports:
      - "8001:8001"
    volumes:
      - ./models:/models
    command: ["--model", "${MODEL_NAME}", "--port", "8001", "--host", "0.0.0.0"]

volumes:
  pgdata:
