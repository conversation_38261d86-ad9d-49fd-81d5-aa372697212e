# Architecture & Data Flow

```mermaid
flowchart LR
    subgraph Core Agents
        TM(Task Manager/Orchestrator) --task--> <PERSON><PERSON>(Code Generator)
        CG --code--> CE(Critique Engine)
        CE --feedback--> TM
    end

    subgraph Infra
        REDIS(Redis Streams)--- msg --- TM & CG & CE
        DB[(PostgreSQL)] --- state --- TM
        VLLM[vLLM / TextGen-Inference] --> CG & CE
    end

    subgraph External
        User[[User / CLI / UI]] --> TM
    end
```

## Component Responsibilities

| Component | Key Classes | Primary Functions |
|-----------|-------------|-------------------|
| Task Manager | `TaskOrchestrator`, `PlanParser` | Parse plan, queue tasks, track progress |
| Code Generator | `CodeGenerator`, `PromptBuilder` | Build prompt, call LLM, post-process code |
| Critique Engine | `StaticAnalyzer`, `LLMCritic`, `CritiqueEngine` | Static checks, LLM review, scoring |
| Message Bus | `MessageBus` | Pub/Sub for distributed scaling |
| Storage | `StateManager` | Persist projects, tasks, artefacts |

### Function Dependency Graph (Python)
```mermaid
graph TD;
TaskOrchestrator-->CodeGenerator;
TaskOrchestrator-->CritiqueEngine;
CodeGenerator-->PromptBuilder;
CritiqueEngine-->StaticAnalyzer;
CritiqueEngine-->LLMCritic;
StaticAnalyzer-->pylint;
StaticAnalyzer-->mypy;
LLMCritic-->vLLM;
```
