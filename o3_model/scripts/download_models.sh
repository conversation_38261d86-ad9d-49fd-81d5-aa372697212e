#!/usr/bin/env bash
# Download and (optionally) quantise LLMs for the Local Agentic System.
# Usage: ./scripts/download_models.sh 34B          # downloads CodeLlama-34B-Instruct-GPTQ
#        ./scripts/download_models.sh 34B --gguf   # converts to GGUF via llama.cpp
#        ./scripts/download_models.sh 34B --gptq   # (default) just pull GPTQ weights

set -euo pipefail

MODEL_SIZE=${1:-34B}
FORMAT=${2:---gptq}

HF_BASE="https://huggingface.co/TheBloke"
DEST="$(pwd)/models"
mkdir -p "$DEST"

case $MODEL_SIZE in
  34B)
    HF_REPO="CodeLlama-${MODEL_SIZE}-Instruct-GPTQ";;
  33B)
    HF_REPO="deepseek-coder-${MODEL_SIZE}-Instruct-GPTQ";;
  15B)
    HF_REPO="StarCoder-${MODEL_SIZE}-GPTQ";;
  *)
    echo "Unsupported size $MODEL_SIZE" && exit 1;;
esac

echo "Downloading $HF_REPO ..."

# Requires git-lfs
if ! command -v git-lfs &>/dev/null; then
  echo "git-lfs not found. Please install git-lfs." && exit 1
fi

pushd "$DEST" >/dev/null
  if [ ! -d "$HF_REPO" ]; then
    GIT_LFS_SKIP_SMUDGE=1 git clone --depth 1 "$HF_BASE/$HF_REPO"
    cd "$HF_REPO"
    git lfs pull --include "*.safetensors,*.bin,*.gptq"
  fi
popd >/dev/null

echo "Model downloaded to $DEST/$HF_REPO"

if [[ "$FORMAT" == "--gguf" ]]; then
  echo "Converting to GGUF via llama.cpp..."
  if ! command -v python &>/dev/null; then
    echo "python not found" && exit 1
  fi
  python - <<'PY'
import sys, subprocess, json, os, pathlib
from pathlib import Path
repo = Path(os.environ['DEST']) / os.environ['HF_REPO']
output = repo.with_suffix('.gguf')
print('Running llama.cpp converter ...')
# assumes llm-format-converter is installed
subprocess.check_call(['python', '-m', 'llm_format_converter', str(repo), str(output)])
print('GGUF saved to', output)
PY
fi

echo "Done."
