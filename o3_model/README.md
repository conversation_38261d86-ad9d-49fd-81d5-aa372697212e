# Local Agentic Code Development System

## Quick Start

```bash
# 1. Clone repository and enter the project directory
$ git clone <repo-url> && cd local_agent

# 2. Create Python virtual environment
$ python3 -m venv .venv && source .venv/bin/activate

# 3. Install dependencies
$ pip install -r requirements.txt

# 4. Pull & quantise the default critique LLM (CodeLlama-34B-Instruct)
$ bash scripts/download_models.sh 34B

# 5. Start the entire stack (API + vLLM + Redis + Postgres)
$ docker-compose up --build

# 6. Open http://localhost:8000/docs for interactive Swagger UI.
```

---

## Repository Layout

```
local_agent/
├── app/               # FastAPI entry-point and routers
├── services/          # TaskManager, CodeGenerator, CritiqueEngine
├── models/            # Pydantic data models
├── shared/            # Common utilities (message-bus, logging, etc.)
├── config/            # Settings & secrets management
├── scripts/           # Helper scripts (model download/quantisation)
├── tests/             # Pytest suite
├── requirements.txt   # Python deps
├── docker-compose.yml # Full local stack
└── Dockerfile         # Production image
```

---

## System Overview

The platform implements an **iterative generation–critique loop** consisting of three asynchronous agents:

1. **Task Manager / Orchestrator** – Parses the user’s TODO list, breaks it into atomic tasks, tracks state and orchestrates the pipeline.
2. **Code Generator** – Uses a local LLM (served via [vLLM](https://github.com/vllm-project/vllm)) to generate code that fulfils the current task.
3. **Critique Engine** – Combines static-analysis tools (pylint, mypy, bandit) with an MoE ensemble of local LLMs (CodeLlama-34B, DeepSeek-Coder-33B) to provide rigorous feedback.

The loop continues until the Critique Engine reports `status == "pass"` for every task.

For a deep-dive see `ARCHITECTURE.md`.
