# requirements.txt
# Python dependencies for a high-quality FastAPI application

# Core Dependencies
fastapi==0.68.0
uvicorn[standard]==0.15.0
sqlalchemy==1.4.22
alembic==1.7.3

# Authentication and Security
passlib[bcrypt]==1.7.4  # For password hashing
josepy==1.8.0  # For JOSE (JSON Object Signing and Encryption)
oauth2-passwordless==0.5.0  # For OAuth without passwords

# Database Utilities
psycopg2-binary==2.9.3  # PostgreSQL adapter for SQLAlchemy

# Testing and Development Tools
pytest==6.2.4
pytest-asyncio==0.17.0
mypy==0.910  # For static type checking
black==21.10b0  # For code formatting
isort==5.9.3  # For sorting imports
flake8==4.0.1  # For linting

# Development and Production Dependencies
# Note: These are typically installed in a dev or production environment, not directly in the main requirements file
dev-requirements.txt:
    pre-commit==2.16.0  # For managing and running hooks
    bandit==1.7.0  # For static analysis of security vulnerabilities
    coverage==5.5  # For code coverage