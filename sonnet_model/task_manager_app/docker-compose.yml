version: '3.8'

services:
  db:
    image: postgres:latest
    container_name: postgres_dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mydb}
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    volumes:
      - db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - app-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: backend_dev
    environment:
      PYTHONUNBUFFERED: '1'
      DB_HOST: db
      DB_NAME: ${POSTGRES_DB:-mydb}
      DB_USER: ${POSTGRES_USER:-user}
      DB_PASSWORD: ${POSTGRES_PASSWORD:-password}
    volumes:
      - ./backend:/app
    depends_on:
      - db
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: frontend_dev
    environment:
      NODE_ENV: development
    volumes:
      - ./frontend:/app
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  db-data:

# Comments and documentation can be added here to explain the purpose of each service, environment variables, and other configurations.