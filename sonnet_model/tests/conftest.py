"""
Pytest configuration and shared fixtures
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_python_code():
    """Sample Python code for testing"""
    return """
def calculate_fibonacci(n):
    '''Calculate fibonacci number'''
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

class Calculator:
    def add(self, a, b):
        return a + b
    
    def multiply(self, a, b):
        return a * b
"""


@pytest.fixture
def sample_javascript_code():
    """Sample JavaScript code for testing"""
    return """
function calculateSum(arr) {
    var total = 0;
    for (var i = 0; i < arr.length; i++) {
        total += arr[i];
    }
    return total;
}

const processData = async (data) => {
    const result = await fetch('/api/data', {
        method: 'POST',
        body: JSON.stringify(data)
    });
    return result.json();
};
"""


@pytest.fixture
def mock_config():
    """Mock configuration for testing"""
    return {
        "task_manager": {
            "storage": {"type": "memory"},
            "max_concurrent_tasks": 3
        },
        "code_generator": {
            "llm": {
                "model_endpoint": "http://localhost:8000",
                "model_name": "test-model"
            },
            "templates": {"path": "templates/"}
        },
        "critique_engine": {
            "static_analysis": {
                "enabled_tools": ["pylint", "mypy"],
                "max_line_length": 88
            },
            "llm_critic": {
                "model_endpoint": "http://localhost:8001"
            }
        }
    }
