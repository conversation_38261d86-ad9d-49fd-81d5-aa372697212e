"""
Test suite for Code Generator
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from code_generator.services.code_generator import CodeGenerator
from code_generator.models.generation_request import GenerationRequest, ProgrammingLanguage, Framework


class TestCodeGenerator:
    """Test cases for CodeGenerator"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.config = {
            "llm": {"model_endpoint": "http://localhost:8000"},
            "templates": {"path": "templates/"}
        }
        self.generator = CodeGenerator(self.config)
    
    @pytest.mark.asyncio
    async def test_generate_code(self):
        """Test basic code generation functionality"""
        request = GenerationRequest(
            task_id="test_task_001",
            description="Create a function that adds two numbers",
            language=ProgrammingLanguage.PYTHON,
            framework=Framework.NONE
        )
        
        # Mock the LLM interface
        self.generator.llm_interface.generate = AsyncMock(return_value={
            "success": True,
            "text": "```python\ndef add_numbers(a, b):\n    return a + b\n```",
            "model": "test-model",
            "tokens_used": 10
        })
        
        result = await self.generator.generate_code(request)
        
        assert result.task_id == "test_task_001"
        assert "def add_numbers" in result.code
        assert result.success is True
    
    @pytest.mark.asyncio
    async def test_prompt_builder(self):
        """Test prompt building functionality"""
        from code_generator.services.prompt_builder import PromptBuilder
        
        builder = PromptBuilder()
        request = GenerationRequest(
            task_id="test_task_002",
            description="Create a REST API endpoint",
            language=ProgrammingLanguage.PYTHON,
            framework=Framework.API,
            context="Using FastAPI framework"
        )
        
        prompt = builder.build_generation_prompt(request)
        
        assert isinstance(prompt, str)
        assert "REST API" in prompt
        assert "FastAPI" in prompt
    
    @pytest.mark.asyncio
    async def test_code_parser(self):
        """Test code parsing functionality"""
        from code_generator.utils.code_parser import CodeParser
        
        parser = CodeParser()
        code = """
def example_function(x, y):
    '''Example function'''
    return x + y

class ExampleClass:
    def method(self):
        pass
"""
        
        parsed = parser.parse_code(code, "python")
        
        assert "functions" in parsed
        assert "classes" in parsed
        assert len(parsed["functions"]) == 2  # example_function and ExampleClass.method
        assert len(parsed["classes"]) == 1
        assert "example_function" in parsed["functions"]
        assert "method" in parsed["functions"]
        assert "ExampleClass" in parsed["classes"]


class TestCodeUtils:
    """Test cases for code utilities"""
    
    def test_code_validation(self):
        """Test code validation"""
        from code_generator.utils.validation import CodeValidator
        
        validator = CodeValidator()
        
        # Valid Python code
        valid_code = "def hello():\n    print('Hello')"
        assert validator.validate_syntax(valid_code, "python") is True
        
        # Invalid Python code
        invalid_code = "def hello(\n    print('Hello')"
        assert validator.validate_syntax(invalid_code, "python") is False
    
    def test_code_formatting(self):
        """Test code formatting"""
        from code_generator.services.code_formatter import CodeFormatter
        
        formatter = CodeFormatter()
        unformatted_code = "def hello( ):print( 'Hello' )"
        
        formatted = formatter.format_code(unformatted_code, "python")
        
        assert "def hello():" in formatted
        assert "print('Hello')" in formatted


if __name__ == "__main__":
    pytest.main([__file__])
