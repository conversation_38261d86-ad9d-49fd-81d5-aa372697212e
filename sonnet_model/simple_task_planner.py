"""
SIMPLE TASK PLANNER

A simplified version that actually works and creates the task breakdown
you wanted for feeding into the code generation system.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class SimpleTaskPlanner:
    """Simple but working task planner"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
    async def create_project_tasks(self, project_description: str) -> List[Dict[str, Any]]:
        """Create project tasks from description"""
        
        print(f"🎯 CREATING PROJECT TASKS")
        print(f"📝 Project: {project_description}")
        print("-" * 60)
        
        # Get raw response first
        response = await self._get_task_breakdown(project_description)
        
        if not response:
            print("❌ No response from LLM")
            return []
        
        print(f"📄 Raw response length: {len(response)} characters")
        print(f"📝 Response preview: {response[:200]}...")
        
        # Parse the response
        tasks = self._extract_tasks_from_response(response)
        
        print(f"✅ Extracted {len(tasks)} tasks")
        return tasks
    
    async def _get_task_breakdown(self, description: str) -> str:
        """Get task breakdown from LLM"""
        
        prompt = f"""Break down this project into specific implementation tasks:

PROJECT: {description}

For each task, provide:
- File path (e.g., backend/models.py)
- Description of what the file does
- 3-5 specific requirements

Format as a simple list:

TASK 1:
File: backend/models.py
Description: Database models for the application
Requirements:
- User model with authentication fields
- Data model with relationships
- Validation and constraints

TASK 2:
File: backend/api.py
Description: REST API endpoints
Requirements:
- CRUD operations
- Authentication middleware
- Error handling

Continue for all necessary files...

TASK BREAKDOWN:"""
        
        return await self._send_llm_request(prompt)
    
    def _extract_tasks_from_response(self, response: str) -> List[Dict[str, Any]]:
        """Extract tasks from the LLM response"""
        
        tasks = []
        lines = response.split('\n')
        current_task = {}
        current_requirements = []
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('TASK ') and ':' in line:
                # Save previous task
                if current_task and current_requirements:
                    current_task['requirements'] = current_requirements.copy()
                    tasks.append(current_task.copy())
                
                # Start new task
                current_task = {}
                current_requirements = []
                
            elif line.startswith('File:'):
                current_task['path'] = line.replace('File:', '').strip()
                
            elif line.startswith('Description:'):
                current_task['description'] = line.replace('Description:', '').strip()
                
            elif line.startswith('Requirements:'):
                current_requirements = []
                
            elif line.startswith('- ') and current_task:
                requirement = line.replace('- ', '').strip()
                if requirement:
                    current_requirements.append(requirement)
        
        # Add the last task
        if current_task and current_requirements:
            current_task['requirements'] = current_requirements
            tasks.append(current_task)
        
        return tasks
    
    async def create_comprehensive_project(self, description: str) -> Dict[str, Any]:
        """Create a comprehensive project structure"""
        
        print(f"🏗️ CREATING COMPREHENSIVE PROJECT")
        print(f"📝 Description: {description}")
        print("-" * 60)
        
        # Get project overview
        overview = await self._get_project_overview(description)
        
        # Get task breakdown
        tasks = await self.create_project_tasks(description)
        
        # Combine into project structure
        project = {
            "name": self._extract_project_name(description),
            "description": description,
            "overview": overview,
            "tasks": tasks,
            "total_files": len(tasks)
        }
        
        return project
    
    async def _get_project_overview(self, description: str) -> str:
        """Get project overview"""
        
        prompt = f"""Provide a brief technical overview for this project:

PROJECT: {description}

Include:
- Technology stack
- Architecture approach
- Key components
- Database requirements

Keep it concise (2-3 paragraphs).

OVERVIEW:"""
        
        response = await self._send_llm_request(prompt)
        return response.strip()
    
    def _extract_project_name(self, description: str) -> str:
        """Extract a project name from description"""
        
        # Simple extraction - take first few words and clean them
        words = description.lower().split()[:3]
        name = '_'.join(word.strip('.,!?') for word in words if word.isalpha())
        return name or "project"
    
    async def _send_llm_request(self, prompt: str) -> str:
        """Send request to LLM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.2,
                            "top_p": 0.9,
                            "num_predict": 2048
                        }
                    },
                    timeout=90.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "")
                else:
                    self.logger.error(f"LLM request failed: {response.status_code}")
                    return ""
                    
        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return ""


async def test_simple_planner():
    """Test the simple task planner"""
    
    print("🚀 TESTING SIMPLE TASK PLANNER")
    print("=" * 60)
    
    planner = SimpleTaskPlanner()
    
    # Test project
    project_description = "Create a todo list web application with user authentication"
    
    # Create comprehensive project
    project = await planner.create_comprehensive_project(project_description)
    
    print(f"\n📊 PROJECT RESULTS:")
    print(f"📁 Name: {project['name']}")
    print(f"📄 Total Files: {project['total_files']}")
    
    print(f"\n📋 TASKS:")
    for i, task in enumerate(project['tasks'], 1):
        print(f"   {i}. {task.get('path', 'N/A')}")
        print(f"      {task.get('description', 'N/A')}")
        print(f"      Requirements: {len(task.get('requirements', []))}")
    
    # Save the project
    with open("simple_project_plan.json", 'w') as f:
        json.dump(project, f, indent=2)
    
    print(f"\n💾 Saved project plan to: simple_project_plan.json")
    
    return project


async def main():
    """Run the simple planner test"""
    
    project = await test_simple_planner()
    
    print(f"\n🎉 SIMPLE TASK PLANNER COMPLETED!")
    print(f"✅ Created {len(project['tasks'])} tasks")
    
    # Show how this would be used with the code generator
    print(f"\n🔗 INTEGRATION WITH CODE GENERATOR:")
    print("This task list can now be fed to the iterative improvement system:")
    
    for i, task in enumerate(project['tasks'][:3], 1):
        print(f"   Task {i}: Generate {task.get('path', 'N/A')}")
        print(f"   → Code Generator creates file")
        print(f"   → Critique Engine reviews file")
        print(f"   → Iterate until quality threshold reached")
        print(f"   → Move to next task")


if __name__ == "__main__":
    asyncio.run(main())
