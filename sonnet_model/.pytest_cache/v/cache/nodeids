["tests/test_code_generator.py::TestCodeGenerator::test_code_parser", "tests/test_code_generator.py::TestCodeGenerator::test_generate_code", "tests/test_code_generator.py::TestCodeGenerator::test_prompt_builder", "tests/test_code_generator.py::TestCodeUtils::test_code_formatting", "tests/test_code_generator.py::TestCodeUtils::test_code_validation", "tests/test_task_manager.py::TestPlanParser::test_parse_complex_plan", "tests/test_task_manager.py::TestPlanParser::test_parse_simple_plan", "tests/test_task_manager.py::TestTaskManager::test_orchestrator_process_task", "tests/test_task_manager.py::TestTaskManager::test_state_manager_operations", "tests/test_task_manager.py::TestTaskManager::test_task_creation"]