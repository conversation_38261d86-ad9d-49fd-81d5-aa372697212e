"""
Message bus implementation for Sonnet Model
Supports Redis, RabbitMQ, and in-memory message passing
"""
import asyncio
import json
import logging
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union, Tuple

import aioredis
import pydantic
from pydantic import BaseModel, Field


class MessageType(str, Enum):
    """Message types for the message bus"""
    TASK_CREATED = "task_created"
    TASK_UPDATED = "task_updated"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    CODE_GENERATION_REQUEST = "code_generation_request"
    CODE_GENERATION_RESPONSE = "code_generation_response"
    CRITIQUE_REQUEST = "critique_request"
    CRITIQUE_RESPONSE = "critique_response"
    SYSTEM_EVENT = "system_event"


class Message(BaseModel):
    """Base message model for the message bus"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: MessageType
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    payload: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def to_json(self) -> str:
        """Convert message to JSON string"""
        return self.model_dump_json()
    
    @classmethod
    def from_json(cls, json_str: str) -> "Message":
        """Create message from JSON string"""
        data = json.loads(json_str)
        return cls(**data)


class MessageBus(ABC):
    """Abstract message bus interface"""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the message bus"""
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the message bus"""
        pass
    
    @abstractmethod
    async def publish(self, channel: str, message: Message) -> None:
        """
        Publish a message to a channel
        
        Args:
            channel: Channel name
            message: Message to publish
        """
        pass
    
    @abstractmethod
    async def subscribe(
        self, 
        channel: str, 
        callback: Callable[[Message], Awaitable[None]]
    ) -> None:
        """
        Subscribe to a channel
        
        Args:
            channel: Channel name
            callback: Async callback function to handle messages
        """
        pass
    
    @abstractmethod
    async def unsubscribe(self, channel: str) -> None:
        """
        Unsubscribe from a channel
        
        Args:
            channel: Channel name
        """
        pass


class RedisMessageBus(MessageBus):
    """Redis implementation of the message bus"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Redis message bus
        
        Args:
            config: Redis configuration
        """
        self.config = config
        self.redis = None
        self.pubsub = None
        self.subscriptions: Dict[str, List[Callable[[Message], Awaitable[None]]]] = {}
        self.tasks: List[asyncio.Task] = []
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> None:
        """Initialize Redis connection"""
        self.logger.info("Initializing Redis message bus")
        
        # Connect to Redis
        self.redis = await aioredis.create_redis_pool(
            f"redis://{self.config.get('host', 'localhost')}:{self.config.get('port', 6379)}",
            password=self.config.get("password"),
            db=self.config.get("db", 0),
            encoding="utf-8",
        )
        
        # Create PubSub instance
        self.pubsub = self.redis.pubsub()
        
        # Resubscribe to channels if any
        for channel, callbacks in self.subscriptions.items():
            for callback in callbacks:
                await self._subscribe_internal(channel, callback)
        
        self.logger.info("Redis message bus initialized")
    
    async def shutdown(self) -> None:
        """Shutdown Redis connection"""
        self.logger.info("Shutting down Redis message bus")
        
        # Cancel all subscription tasks
        for task in self.tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Close Redis connection
        if self.redis:
            self.redis.close()
            await self.redis.wait_closed()
        
        self.logger.info("Redis message bus shut down")
    
    async def publish(self, channel: str, message: Message) -> None:
        """
        Publish a message to a Redis channel
        
        Args:
            channel: Channel name
            message: Message to publish
        """
        if not self.redis:
            raise RuntimeError("Redis message bus not initialized")
        
        await self.redis.publish(channel, message.to_json())
    
    async def subscribe(
        self, 
        channel: str, 
        callback: Callable[[Message], Awaitable[None]]
    ) -> None:
        """
        Subscribe to a Redis channel
        
        Args:
            channel: Channel name
            callback: Async callback function to handle messages
        """
        if channel not in self.subscriptions:
            self.subscriptions[channel] = []
        
        self.subscriptions[channel].append(callback)
        
        if self.redis:
            await self._subscribe_internal(channel, callback)
    
    async def _subscribe_internal(
        self, 
        channel: str, 
        callback: Callable[[Message], Awaitable[None]]
    ) -> None:
        """
        Internal method to subscribe to a Redis channel
        
        Args:
            channel: Channel name
            callback: Async callback function to handle messages
        """
        # Use shared pubsub connection instead of creating new ones
        if self.pubsub is None:
            self.pubsub = self.redis.pubsub()
            # Create a single task to process all messages
            task = asyncio.create_task(self._process_all_messages())
            self.tasks.append(task)
        
        # Subscribe to the channel using shared connection
        await self.pubsub.subscribe(channel)
    
    async def _process_all_messages(self) -> None:
        """
        Process all messages from Redis pubsub channels
        """
        try:
            while True:
                message = await self.pubsub.get_message(timeout=1)
                if message and message["type"] == "message":
                    try:
                        # Parse the message
                        msg = Message.from_json(message["data"])
                        
                        # Call the callbacks for the channel
                        channel = message["channel"].decode("utf-8")
                        if channel in self.subscriptions:
                            for callback in self.subscriptions[channel]:
                                await callback(msg)
                    except Exception as e:
                        self.logger.error(f"Error processing message: {e}")
                
                # Yield control to other tasks
                await asyncio.sleep(0.01)
        except asyncio.CancelledError:
            # Task was cancelled, clean up
            await self.pubsub.unsubscribe()
        except Exception as e:
            self.logger.error(f"Error in message processing loop: {e}")
    
    async def unsubscribe(self, channel: str) -> None:
        """
        Unsubscribe from a Redis channel
        
        Args:
            channel: Channel name
        """
        if channel in self.subscriptions:
            del self.subscriptions[channel]
        
        # Note: The tasks will continue running until they're cancelled
        # during shutdown


class InMemoryMessageBus(MessageBus):
    """In-memory implementation of the message bus for testing and development"""
    
    def __init__(self):
        """Initialize in-memory message bus"""
        self.subscriptions: Dict[str, List[Callable[[Message], Awaitable[None]]]] = {}
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> None:
        """Initialize in-memory message bus"""
        self.logger.info("Initializing in-memory message bus")
    
    async def shutdown(self) -> None:
        """Shutdown in-memory message bus"""
        self.logger.info("Shutting down in-memory message bus")
        self.subscriptions.clear()
    
    async def publish(self, channel: str, message: Message) -> None:
        """
        Publish a message to a channel
        
        Args:
            channel: Channel name
            message: Message to publish
        """
        if channel in self.subscriptions:
            for callback in self.subscriptions[channel]:
                try:
                    await callback(message)
                except Exception as e:
                    self.logger.error(f"Error calling callback for channel {channel}: {e}")
    
    async def subscribe(
        self, 
        channel: str, 
        callback: Callable[[Message], Awaitable[None]]
    ) -> None:
        """
        Subscribe to a channel
        
        Args:
            channel: Channel name
            callback: Async callback function to handle messages
        """
        if channel not in self.subscriptions:
            self.subscriptions[channel] = []
        
        self.subscriptions[channel].append(callback)
    
    async def unsubscribe(self, channel: str) -> None:
        """
        Unsubscribe from a channel
        
        Args:
            channel: Channel name
        """
        if channel in self.subscriptions:
            del self.subscriptions[channel]


def create_message_bus(config: Dict[str, Any]) -> MessageBus:
    """
    Create a message bus instance based on configuration
    
    Args:
        config: Message bus configuration
        
    Returns:
        MessageBus instance
    """
    bus_type = config.get("type", "memory").lower()
    
    if bus_type == "redis":
        return RedisMessageBus(config)
    elif bus_type == "memory":
        return InMemoryMessageBus()
    else:
        raise ValueError(f"Unsupported message bus type: {bus_type}")
