"""
Application state management for Sonnet Model
"""
from dataclasses import dataclass, field
from typing import Dict, Any, Optional

from task_manager.orchestrator import TaskOrchestrator


@dataclass
class AppState:
    """
    Application state container
    
    Holds references to shared resources and services
    """
    orchestrator: Optional[TaskOrchestrator] = None
    components: Dict[str, Any] = field(default_factory=dict)
    
    def register_component(self, name: str, component: Any) -> None:
        """
        Register a component in the application state
        
        Args:
            name: Component name
            component: Component instance
        """
        self.components[name] = component
    
    def get_component(self, name: str) -> Any:
        """
        Get a component from the application state
        
        Args:
            name: Component name
            
        Returns:
            Component instance
            
        Raises:
            KeyError: If the component is not found
        """
        if name not in self.components:
            raise KeyError(f"Component not found: {name}")
        return self.components[name]
    
    def has_component(self, name: str) -> bool:
        """
        Check if a component exists in the application state
        
        Args:
            name: Component name
            
        Returns:
            True if the component exists, False otherwise
        """
        return name in self.components
