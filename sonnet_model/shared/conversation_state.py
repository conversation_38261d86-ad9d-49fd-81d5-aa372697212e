"""
Conversation State Management

Tracks conversation length and manages context resets to prevent
the main LLM from getting lost in long conversations.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

from .models import Task, TaskStatus


@dataclass
class ConversationState:
    """Tracks the state of a conversation"""
    conversation_id: str
    start_time: datetime
    message_count: int
    total_tokens: int
    active_tasks: List[str]
    completed_tasks: List[str]
    current_context: str
    project_summary: str
    key_decisions: List[str]
    last_checkpoint: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat()
        data['last_checkpoint'] = self.last_checkpoint.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationState':
        """Create from dictionary"""
        data['start_time'] = datetime.fromisoformat(data['start_time'])
        data['last_checkpoint'] = datetime.fromisoformat(data['last_checkpoint'])
        return cls(**data)


class ConversationStateManager:
    """
    Manages conversation state and triggers resets when needed
    
    This is crucial for preventing the main LLM from getting lost
    in long conversations and ensuring project completion.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.max_messages = config.get("max_messages", 50)
        self.max_tokens = config.get("max_tokens", 100000)
        self.checkpoint_interval = config.get("checkpoint_interval", 10)  # messages
        self.state_file = Path(config.get("state_file", "conversation_state.json"))
        
        # Current state
        self.current_state: Optional[ConversationState] = None
        self.reset_callbacks: List[callable] = []
        
    def initialize_conversation(self, conversation_id: str, project_summary: str) -> ConversationState:
        """Initialize a new conversation state"""
        self.current_state = ConversationState(
            conversation_id=conversation_id,
            start_time=datetime.now(),
            message_count=0,
            total_tokens=0,
            active_tasks=[],
            completed_tasks=[],
            current_context=project_summary,
            project_summary=project_summary,
            key_decisions=[],
            last_checkpoint=datetime.now()
        )
        
        self.logger.info(f"Initialized conversation {conversation_id}")
        return self.current_state
    
    def update_message_count(self, tokens_used: int = 0):
        """Update message count and token usage"""
        if not self.current_state:
            return
            
        self.current_state.message_count += 1
        self.current_state.total_tokens += tokens_used
        
        # Check if checkpoint is needed
        if self.current_state.message_count % self.checkpoint_interval == 0:
            self._create_checkpoint()
        
        # Check if reset is needed
        if self._should_reset_conversation():
            self._trigger_conversation_reset()
    
    def add_task(self, task_id: str):
        """Add a task to active tasks"""
        if self.current_state and task_id not in self.current_state.active_tasks:
            self.current_state.active_tasks.append(task_id)
    
    def complete_task(self, task_id: str):
        """Mark a task as completed"""
        if not self.current_state:
            return
            
        if task_id in self.current_state.active_tasks:
            self.current_state.active_tasks.remove(task_id)
        
        if task_id not in self.current_state.completed_tasks:
            self.current_state.completed_tasks.append(task_id)
    
    def add_key_decision(self, decision: str):
        """Add a key decision to track"""
        if self.current_state:
            self.current_state.key_decisions.append(f"{datetime.now().isoformat()}: {decision}")
    
    def update_context(self, new_context: str):
        """Update the current context"""
        if self.current_state:
            self.current_state.current_context = new_context
    
    def _should_reset_conversation(self) -> bool:
        """Check if conversation should be reset"""
        if not self.current_state:
            return False
            
        # Check message count
        if self.current_state.message_count >= self.max_messages:
            self.logger.warning(f"Message count ({self.current_state.message_count}) exceeded limit ({self.max_messages})")
            return True
        
        # Check token count
        if self.current_state.total_tokens >= self.max_tokens:
            self.logger.warning(f"Token count ({self.current_state.total_tokens}) exceeded limit ({self.max_tokens})")
            return True
        
        return False
    
    def _create_checkpoint(self):
        """Create a checkpoint of current state"""
        if not self.current_state:
            return
            
        self.current_state.last_checkpoint = datetime.now()
        self._save_state()
        self.logger.info(f"Created checkpoint at message {self.current_state.message_count}")
    
    def _trigger_conversation_reset(self):
        """Trigger a conversation reset"""
        if not self.current_state:
            return
            
        self.logger.critical("🔄 CONVERSATION RESET TRIGGERED!")
        
        # Create reset context with all essential information
        reset_context = self._create_reset_context()
        
        # Notify all callbacks
        for callback in self.reset_callbacks:
            try:
                callback(reset_context)
            except Exception as e:
                self.logger.error(f"Reset callback failed: {e}")
        
        # Reset the conversation state but preserve essential info
        old_state = self.current_state
        self.current_state = ConversationState(
            conversation_id=f"{old_state.conversation_id}_reset_{datetime.now().strftime('%H%M%S')}",
            start_time=datetime.now(),
            message_count=0,
            total_tokens=0,
            active_tasks=old_state.active_tasks.copy(),
            completed_tasks=old_state.completed_tasks.copy(),
            current_context=reset_context["fresh_context"],
            project_summary=old_state.project_summary,
            key_decisions=old_state.key_decisions.copy(),
            last_checkpoint=datetime.now()
        )
        
        self.logger.info("Conversation reset completed with preserved context")
    
    def _create_reset_context(self) -> Dict[str, Any]:
        """Create context for conversation reset"""
        if not self.current_state:
            return {}
        
        # Create a comprehensive but concise context
        fresh_context = f"""
🔄 CONVERSATION RESET - CONTINUE FROM HERE

📋 PROJECT: {self.current_state.project_summary}

✅ COMPLETED TASKS ({len(self.current_state.completed_tasks)}):
{chr(10).join(f"- {task}" for task in self.current_state.completed_tasks[-10:])}  # Last 10

🔄 ACTIVE TASKS ({len(self.current_state.active_tasks)}):
{chr(10).join(f"- {task}" for task in self.current_state.active_tasks)}

🎯 KEY DECISIONS:
{chr(10).join(self.current_state.key_decisions[-5:])}  # Last 5

📍 CURRENT CONTEXT:
{self.current_state.current_context[-1000:]}  # Last 1000 chars

🚀 INSTRUCTIONS:
- Continue working on active tasks
- Maintain the same quality and approach
- DO NOT ask for permission to continue
- DO NOT stop until ALL tasks are complete
- Reference this context for project state
"""
        
        return {
            "fresh_context": fresh_context,
            "completed_count": len(self.current_state.completed_tasks),
            "active_count": len(self.current_state.active_tasks),
            "key_decisions": self.current_state.key_decisions,
            "reset_reason": "conversation_length_exceeded"
        }
    
    def register_reset_callback(self, callback: callable):
        """Register a callback for conversation resets"""
        self.reset_callbacks.append(callback)
    
    def _save_state(self):
        """Save current state to file"""
        if not self.current_state:
            return
            
        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.current_state.to_dict(), f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
    
    def load_state(self) -> Optional[ConversationState]:
        """Load state from file"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    data = json.load(f)
                self.current_state = ConversationState.from_dict(data)
                self.logger.info(f"Loaded conversation state: {self.current_state.conversation_id}")
                return self.current_state
        except Exception as e:
            self.logger.error(f"Failed to load state: {e}")
        
        return None
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get a summary of current progress"""
        if not self.current_state:
            return {}
        
        return {
            "conversation_id": self.current_state.conversation_id,
            "message_count": self.current_state.message_count,
            "total_tokens": self.current_state.total_tokens,
            "completed_tasks": len(self.current_state.completed_tasks),
            "active_tasks": len(self.current_state.active_tasks),
            "duration_minutes": (datetime.now() - self.current_state.start_time).total_seconds() / 60,
            "needs_reset": self._should_reset_conversation()
        }
