"""
Enhanced Conversation Manager with Dynamic Length Management

Handles conversation length dynamically based on LLM signals, context preservation,
and intelligent conversation reset strategies.
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum


class ConversationLengthMode(Enum):
    """Conversation length management modes"""
    FIXED = "fixed"  # Fixed number of exchanges
    DYNAMIC = "dynamic"  # Dynamic based on content and context
    SIGNAL_BASED = "signal_based"  # Based on LLM signals


class ContextPreservationMethod(Enum):
    """Methods for preserving context during conversation reset"""
    INTELLIGENT_SUMMARY = "intelligent_summary"
    KEY_POINTS = "key_points"
    FULL_CONTEXT = "full_context"
    RECENT_ONLY = "recent_only"


@dataclass
class ConversationExchange:
    """Single conversation exchange"""
    timestamp: datetime
    role: str  # "user", "assistant", "system"
    content: str
    tokens_used: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversationState:
    """Current conversation state"""
    session_id: str
    exchanges: List[ConversationExchange] = field(default_factory=list)
    total_tokens: int = 0
    reset_count: int = 0
    last_reset: Optional[datetime] = None
    context_summary: str = ""
    critical_info: Dict[str, Any] = field(default_factory=dict)


class EnhancedConversationManager:
    """
    Enhanced conversation manager with dynamic length management
    
    Features:
    - Dynamic conversation length based on LLM signals
    - Intelligent context preservation during resets
    - Signal detection for conversation management
    - Token-aware conversation tracking
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Conversation length management
        self.length_mode = ConversationLengthMode(
            config.get("max_conversation_length_mode", "dynamic")
        )
        self.fixed_length = config.get("max_conversation_length_fixed", 50)
        self.dynamic_min = config.get("max_conversation_length_dynamic_min", 20)
        self.dynamic_max = config.get("max_conversation_length_dynamic_max", 80)
        
        # Signal-based management
        self.reset_on_signals = config.get("reset_on_signals", True)
        self.reset_signals = config.get("conversation_reset_signals", [
            "conversation is getting long",
            "context is full",
            "running out of space",
            "message limit",
            "too many messages",
            "context window",
            "token limit",
            "memory full"
        ])
        
        # Context preservation
        self.context_preservation = config.get("context_preservation", {})
        self.preservation_enabled = self.context_preservation.get("enabled", True)
        self.preservation_method = ContextPreservationMethod(
            self.context_preservation.get("method", "intelligent_summary")
        )
        self.summary_max_tokens = self.context_preservation.get("summary_max_tokens", 1500)
        self.preserve_recent_exchanges = self.context_preservation.get("preserve_recent_exchanges", 5)
        self.preserve_critical_info = self.context_preservation.get("preserve_critical_info", True)
        
        # Active conversations
        self.conversations: Dict[str, ConversationState] = {}
        
        self.logger.info(f"Enhanced conversation manager initialized with {self.length_mode.value} mode")
    
    def get_conversation(self, session_id: str) -> ConversationState:
        """Get or create conversation state for session"""
        if session_id not in self.conversations:
            self.conversations[session_id] = ConversationState(session_id=session_id)
        return self.conversations[session_id]
    
    def add_exchange(
        self, 
        session_id: str, 
        role: str, 
        content: str, 
        tokens_used: int = 0,
        metadata: Dict[str, Any] = None
    ) -> None:
        """Add a new exchange to the conversation"""
        conversation = self.get_conversation(session_id)
        
        exchange = ConversationExchange(
            timestamp=datetime.now(),
            role=role,
            content=content,
            tokens_used=tokens_used,
            metadata=metadata or {}
        )
        
        conversation.exchanges.append(exchange)
        conversation.total_tokens += tokens_used
        
        self.logger.debug(f"Added {role} exchange to session {session_id} ({tokens_used} tokens)")
    
    def should_reset_conversation(self, session_id: str, latest_content: str = "") -> Tuple[bool, str]:
        """
        Determine if conversation should be reset
        
        Returns:
            Tuple of (should_reset, reason)
        """
        conversation = self.get_conversation(session_id)
        
        # Check based on length mode
        if self.length_mode == ConversationLengthMode.FIXED:
            if len(conversation.exchanges) >= self.fixed_length:
                return True, f"Fixed length limit reached ({self.fixed_length} exchanges)"
        
        elif self.length_mode == ConversationLengthMode.DYNAMIC:
            # Dynamic assessment based on content complexity and length
            should_reset, reason = self._assess_dynamic_reset(conversation, latest_content)
            if should_reset:
                return True, reason
        
        elif self.length_mode == ConversationLengthMode.SIGNAL_BASED:
            # Check for LLM signals indicating conversation length issues
            if self.reset_on_signals and latest_content:
                signal_detected = self._detect_reset_signals(latest_content)
                if signal_detected:
                    return True, f"LLM signal detected: {signal_detected}"
        
        # Check absolute maximum regardless of mode
        if len(conversation.exchanges) >= self.dynamic_max:
            return True, f"Absolute maximum length reached ({self.dynamic_max} exchanges)"
        
        return False, ""
    
    def _assess_dynamic_reset(self, conversation: ConversationState, latest_content: str) -> Tuple[bool, str]:
        """Assess if dynamic reset is needed based on conversation characteristics"""
        exchange_count = len(conversation.exchanges)
        
        # Don't reset if below minimum
        if exchange_count < self.dynamic_min:
            return False, ""
        
        # Calculate conversation complexity metrics
        avg_exchange_length = sum(len(ex.content) for ex in conversation.exchanges) / max(exchange_count, 1)
        recent_exchanges = conversation.exchanges[-5:] if len(conversation.exchanges) >= 5 else conversation.exchanges
        recent_avg_length = sum(len(ex.content) for ex in recent_exchanges) / max(len(recent_exchanges), 1)
        
        # Check for increasing complexity
        if recent_avg_length > avg_exchange_length * 1.5:
            return True, "Conversation complexity increasing significantly"
        
        # Check for repetitive patterns
        if self._detect_repetitive_patterns(conversation):
            return True, "Repetitive conversation patterns detected"
        
        # Check token usage if available
        if conversation.total_tokens > 0:
            # Estimate context window usage (assuming 4K context window)
            estimated_context_usage = conversation.total_tokens / 4000
            if estimated_context_usage > 0.8:
                return True, "Approaching context window limit"
        
        return False, ""
    
    def _detect_reset_signals(self, content: str) -> Optional[str]:
        """Detect LLM signals indicating conversation should be reset"""
        content_lower = content.lower()
        
        for signal in self.reset_signals:
            if signal.lower() in content_lower:
                return signal
        
        # Additional pattern-based detection
        patterns = [
            r"context.*(?:full|limit|window)",
            r"conversation.*(?:long|lengthy|extended)",
            r"(?:running out|out of).*(?:space|memory|tokens)",
            r"(?:message|token).*limit",
            r"too many.*(?:messages|exchanges|turns)"
        ]
        
        for pattern in patterns:
            if re.search(pattern, content_lower):
                return f"Pattern detected: {pattern}"
        
        return None
    
    def _detect_repetitive_patterns(self, conversation: ConversationState) -> bool:
        """Detect if conversation has repetitive patterns"""
        if len(conversation.exchanges) < 6:
            return False
        
        # Check last 6 exchanges for repetitive content
        recent_contents = [ex.content.lower() for ex in conversation.exchanges[-6:]]
        
        # Simple repetition detection
        for i in range(len(recent_contents) - 2):
            for j in range(i + 2, len(recent_contents)):
                similarity = self._calculate_content_similarity(recent_contents[i], recent_contents[j])
                if similarity > 0.7:  # 70% similarity threshold
                    return True
        
        return False
    
    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """Calculate similarity between two content strings"""
        words1 = set(content1.split())
        words2 = set(content2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def reset_conversation(self, session_id: str, reason: str = "") -> Dict[str, Any]:
        """
        Reset conversation with context preservation
        
        Returns:
            Dictionary with reset information and preserved context
        """
        conversation = self.get_conversation(session_id)
        
        self.logger.info(f"Resetting conversation for session {session_id}: {reason}")
        
        # Preserve context if enabled
        preserved_context = {}
        if self.preservation_enabled:
            preserved_context = self._preserve_context(conversation)
        
        # Reset conversation state
        old_exchange_count = len(conversation.exchanges)
        conversation.exchanges = []
        conversation.total_tokens = 0
        conversation.reset_count += 1
        conversation.last_reset = datetime.now()
        
        # Update context summary
        if preserved_context.get("summary"):
            conversation.context_summary = preserved_context["summary"]
        
        # Preserve critical information
        if preserved_context.get("critical_info"):
            conversation.critical_info.update(preserved_context["critical_info"])
        
        reset_info = {
            "session_id": session_id,
            "reason": reason,
            "old_exchange_count": old_exchange_count,
            "reset_count": conversation.reset_count,
            "preserved_context": preserved_context,
            "timestamp": conversation.last_reset.isoformat()
        }
        
        self.logger.info(f"Conversation reset complete for session {session_id}")
        return reset_info
    
    def _preserve_context(self, conversation: ConversationState) -> Dict[str, Any]:
        """Preserve important context during conversation reset"""
        preserved = {
            "summary": "",
            "critical_info": {},
            "recent_exchanges": [],
            "key_points": []
        }
        
        if not conversation.exchanges:
            return preserved
        
        # Preserve recent exchanges
        if self.preserve_recent_exchanges > 0:
            recent_count = min(self.preserve_recent_exchanges, len(conversation.exchanges))
            preserved["recent_exchanges"] = [
                {
                    "role": ex.role,
                    "content": ex.content[:500],  # Truncate long content
                    "timestamp": ex.timestamp.isoformat()
                }
                for ex in conversation.exchanges[-recent_count:]
            ]
        
        # Generate summary based on preservation method
        if self.preservation_method == ContextPreservationMethod.INTELLIGENT_SUMMARY:
            preserved["summary"] = self._generate_intelligent_summary(conversation)
        elif self.preservation_method == ContextPreservationMethod.KEY_POINTS:
            preserved["key_points"] = self._extract_key_points(conversation)
        
        # Preserve critical information
        if self.preserve_critical_info:
            preserved["critical_info"] = self._extract_critical_info(conversation)
        
        return preserved
    
    def _generate_intelligent_summary(self, conversation: ConversationState) -> str:
        """Generate intelligent summary of conversation"""
        if not conversation.exchanges:
            return ""
        
        # Extract key themes and decisions
        key_content = []
        for exchange in conversation.exchanges:
            # Look for important markers
            content = exchange.content
            if any(marker in content.lower() for marker in [
                "implement", "create", "build", "fix", "error", "issue", 
                "requirement", "feature", "task", "goal", "objective"
            ]):
                key_content.append(content[:200])  # First 200 chars
        
        # Create summary
        summary_parts = [
            f"Conversation with {len(conversation.exchanges)} exchanges",
            f"Total tokens: {conversation.total_tokens}",
            f"Key activities: {', '.join(key_content[:3])}" if key_content else "General discussion"
        ]
        
        return " | ".join(summary_parts)
    
    def _extract_key_points(self, conversation: ConversationState) -> List[str]:
        """Extract key points from conversation"""
        key_points = []
        
        for exchange in conversation.exchanges:
            # Look for bullet points, numbered lists, or key statements
            lines = exchange.content.split('\n')
            for line in lines:
                line = line.strip()
                if (line.startswith(('•', '-', '*', '1.', '2.', '3.')) or
                    'important' in line.lower() or
                    'key' in line.lower() or
                    'critical' in line.lower()):
                    key_points.append(line[:100])
        
        return key_points[:10]  # Limit to 10 key points
    
    def _extract_critical_info(self, conversation: ConversationState) -> Dict[str, Any]:
        """Extract critical information that should be preserved"""
        critical_info = {
            "project_state": {},
            "requirements": [],
            "errors_encountered": [],
            "decisions_made": []
        }
        
        for exchange in conversation.exchanges:
            content = exchange.content.lower()
            
            # Extract requirements
            if 'requirement' in content or 'must' in content or 'should' in content:
                critical_info["requirements"].append(exchange.content[:200])
            
            # Extract errors
            if 'error' in content or 'exception' in content or 'failed' in content:
                critical_info["errors_encountered"].append(exchange.content[:200])
            
            # Extract decisions
            if 'decided' in content or 'chosen' in content or 'selected' in content:
                critical_info["decisions_made"].append(exchange.content[:200])
        
        return critical_info
    
    def get_conversation_stats(self, session_id: str) -> Dict[str, Any]:
        """Get conversation statistics"""
        conversation = self.get_conversation(session_id)
        
        return {
            "session_id": session_id,
            "exchange_count": len(conversation.exchanges),
            "total_tokens": conversation.total_tokens,
            "reset_count": conversation.reset_count,
            "last_reset": conversation.last_reset.isoformat() if conversation.last_reset else None,
            "length_mode": self.length_mode.value,
            "should_reset": self.should_reset_conversation(session_id)[0],
            "context_summary": conversation.context_summary,
            "critical_info_keys": list(conversation.critical_info.keys())
        }
