"""
Shared Models - Single Source of Truth for All Data Models
Consolidates all model definitions to eliminate duplication
"""
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Set, Union
from pydantic import BaseModel, Field
import uuid


# Core Enums - Single Source of Truth
class ProgrammingLanguage(str, Enum):
    """Programming language enum"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CSHARP = "csharp"
    CPP = "cpp"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    RUBY = "ruby"
    SWIFT = "swift"
    KOTLIN = "kotlin"
    SHELL = "shell"
    SQL = "sql"
    HTML = "html"
    CSS = "css"
    OTHER = "other"


class Framework(str, Enum):
    """Framework enum"""
    NONE = "none"
    API = "api"
    # Python frameworks
    DJANGO = "django"
    FLASK = "flask"
    FASTAPI = "fastapi"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    PANDAS = "pandas"
    NUMPY = "numpy"
    # JavaScript/TypeScript frameworks
    REACT = "react"
    ANGULAR = "angular"
    VUE = "vue"
    NEXT = "next"
    NODE = "node"
    EXPRESS = "express"
    # Other
    SPRING = "spring"
    DOTNET = "dotnet"
    LARAVEL = "laravel"
    RAILS = "rails"


class IssueSeverity(str, Enum):
    """Issue severity levels"""
    CRITICAL = "critical"
    ERROR = "error"
    HIGH = "high"
    WARNING = "warning"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class IssueCategory(str, Enum):
    """Issue categories - Consolidated from multiple definitions"""
    SYNTAX = "syntax"
    STYLE = "style"
    LOGIC = "logic"
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"
    CORRECTNESS = "correctness"
    COMPLEXITY = "complexity"
    COMPATIBILITY = "compatibility"
    BEST_PRACTICES = "best_practices"
    DOCUMENTATION = "documentation"
    TESTS = "tests"
    QUALITY = "quality"
    OPENCL = "opencl"
    OTHER = "other"


class CritiqueLevel(str, Enum):
    """Critique level enum"""
    BASIC = "basic"
    STANDARD = "standard"
    COMPREHENSIVE = "comprehensive"


class TaskStatus(str, Enum):
    """Task status enum"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    WAITING_FOR_DEPENDENCY = "waiting_for_dependency"
    WAITING_FOR_REVIEW = "waiting_for_review"
    BLOCKED = "blocked"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(int, Enum):
    """Task priority enum"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class TaskType(str, Enum):
    """Task type enum"""
    PLAN = "plan"
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    TESTING = "testing"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    DEPLOYMENT = "deployment"
    OTHER = "other"


# Core Models
class CodeFile(BaseModel):
    """Code file model - Single definition"""
    filename: str = Field(..., description="Filename")
    content: str = Field(..., description="File content")
    language: ProgrammingLanguage = Field(default=ProgrammingLanguage.PYTHON, description="Programming language")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class CodeIssue(BaseModel):
    """Code issue model - Single definition"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique issue identifier")
    title: str = Field(..., description="Short issue title")
    description: str = Field(..., description="Detailed issue description")
    severity: IssueSeverity = Field(default=IssueSeverity.MEDIUM, description="Issue severity")
    category: IssueCategory = Field(default=IssueCategory.OTHER, description="Issue category")
    file_path: Optional[str] = Field(default=None, description="File path")
    line_start: Optional[int] = Field(default=None, description="Starting line number")
    line_end: Optional[int] = Field(default=None, description="Ending line number")
    column_start: Optional[int] = Field(default=None, description="Starting column number")
    column_end: Optional[int] = Field(default=None, description="Ending column number")
    code_snippet: Optional[str] = Field(default=None, description="Relevant code snippet")
    fix_suggestions: List[str] = Field(default_factory=list, description="Suggested fixes")
    auto_fixable: bool = Field(default=False, description="Whether issue can be auto-fixed")
    rule_id: Optional[str] = Field(default=None, description="ID of the rule that detected the issue")
    tool_source: Optional[str] = Field(default=None, description="Source tool")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

    def get_location_str(self) -> str:
        """Get string representation of issue location"""
        if not self.file_path:
            return "Unknown location"
        location = self.file_path
        if self.line_start:
            location += f":{self.line_start}"
            if self.column_start:
                location += f":{self.column_start}"
        return location

    def get_severity_value(self) -> int:
        """Get numeric value for severity"""
        severity_values = {
            IssueSeverity.CRITICAL: 100, IssueSeverity.ERROR: 90, IssueSeverity.HIGH: 75,
            IssueSeverity.WARNING: 60, IssueSeverity.MEDIUM: 50, IssueSeverity.LOW: 25, IssueSeverity.INFO: 10
        }
        return severity_values.get(self.severity, 0)


class GenerationRequest(BaseModel):
    """Code generation request model - Single definition"""
    task_id: str = Field(..., description="Task ID")
    language: ProgrammingLanguage = Field(default=ProgrammingLanguage.PYTHON, description="Programming language")
    framework: Optional[Framework] = Field(default=None, description="Framework to use")
    description: str = Field(..., description="Description of the code to generate")
    requirements: List[str] = Field(default_factory=list, description="List of requirements")
    context: Optional[str] = Field(default=None, description="Additional context")
    dependencies: List[str] = Field(default_factory=list, description="List of dependencies")
    examples: List[str] = Field(default_factory=list, description="Example code snippets")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="Constraints")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    max_tokens: int = Field(default=4096, description="Maximum tokens to generate")
    temperature: float = Field(default=0.7, description="Temperature for generation")
    iteration: int = Field(default=1, description="Iteration number")
    feedback: Optional[str] = Field(default=None, description="Feedback from previous iteration")
    previous_code: Optional[str] = Field(default=None, description="Previous code for reference")

    def get_constraint(self, key: str, default: Any = None) -> Any:
        return self.constraints.get(key, default)

    def has_framework(self) -> bool:
        return self.framework is not None and self.framework != Framework.NONE


class GenerationResult(BaseModel):
    """Code generation result model - Single definition"""
    task_id: str = Field(..., description="Task ID")
    code: str = Field(..., description="Generated code")
    language: str = Field(..., description="Programming language")
    success: bool = Field(..., description="Whether generation was successful")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    generation_time: float = Field(default=0.0, description="Generation time in seconds")
    iteration: int = Field(default=1, description="Iteration number")
    model_used: Optional[str] = Field(default=None, description="Model used for generation")
    tokens_used: int = Field(default=0, description="Tokens used")


class CritiqueRequest(BaseModel):
    """Code critique request model - Single definition"""
    task_id: str = Field(..., description="Task ID")
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Request ID")
    files: List[CodeFile] = Field(default_factory=list, description="Code files to critique")
    code: Optional[str] = Field(default=None, description="Single code string (alternative to files)")
    language: ProgrammingLanguage = Field(default=ProgrammingLanguage.PYTHON, description="Programming language")
    framework: Optional[Framework] = Field(default=None, description="Framework used")
    level: CritiqueLevel = Field(default=CritiqueLevel.STANDARD, description="Critique level")
    categories: Set[IssueCategory] = Field(default_factory=set, description="Categories to check")
    requirements: List[str] = Field(default_factory=list, description="Requirements to check against")
    context: Optional[str] = Field(default=None, description="Additional context")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    iteration: int = Field(default=1, description="Iteration number")

    def should_check_category(self, category: IssueCategory) -> bool:
        return not self.categories or category in self.categories


class CritiqueResult(BaseModel):
    """Code critique result model - Single definition"""
    task_id: str = Field(..., description="Task ID")
    request_id: str = Field(..., description="Request ID")
    issues: List[CodeIssue] = Field(default_factory=list, description="Issues found")
    quality_score: float = Field(..., description="Quality score 0-100")
    meets_threshold: bool = Field(..., description="Whether meets quality threshold")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    analysis_time: float = Field(default=0.0, description="Analysis time in seconds")
    iteration: int = Field(default=1, description="Iteration number")
    success: bool = Field(default=True, description="Whether analysis was successful")

    @property
    def has_critical_issues(self) -> bool:
        return any(issue.severity == IssueSeverity.CRITICAL for issue in self.issues)


class Task(BaseModel):
    """Task model - Single definition"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Task ID")
    project_id: str = Field(..., description="Project ID")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Task description")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Task status")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    task_type: TaskType = Field(default=TaskType.OTHER, description="Task type")
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="Creation time")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(), description="Last update time")
    started_at: Optional[datetime] = Field(default=None, description="Start time")
    completed_at: Optional[datetime] = Field(default=None, description="Completion time")
    assigned_to: Optional[str] = Field(default=None, description="Assigned user")

    # Dependencies and relationships
    dependencies: List[str] = Field(default_factory=list, description="Task dependencies")
    blocks: List[str] = Field(default_factory=list, description="Tasks this blocks")

    # Requirements and specifications
    requirements: List[str] = Field(default_factory=list, description="Task requirements")
    acceptance_criteria: List[str] = Field(default_factory=list, description="Acceptance criteria")

    # Code generation specifics
    language: str = Field(default="python", description="Programming language")
    framework: Optional[str] = Field(default=None, description="Framework to use")
    file_path: Optional[str] = Field(default=None, description="Target file path")

    # Progress tracking
    iteration: int = Field(default=0, description="Current iteration")
    iteration_count: int = Field(default=0, description="Number of iterations attempted")
    max_iterations: int = Field(default=5, description="Maximum iterations")
    progress: float = Field(default=0.0, description="Progress percentage")

    # Results and feedback
    generated_code: Optional[str] = Field(default=None, description="Generated code")
    feedback_history: List[str] = Field(default_factory=list, description="Feedback history")
    quality_score: float = Field(default=0.0, description="Quality score")

    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    tags: List[str] = Field(default_factory=list, description="Task tags")

    def update_status(self, status: TaskStatus) -> None:
        """Update task status and timestamp"""
        self.status = status
        self.updated_at = datetime.now()
        if status == TaskStatus.COMPLETED:
            self.completed_at = datetime.now()

    def is_ready(self) -> bool:
        return self.status == TaskStatus.PENDING and len(self.dependencies) == 0

    def is_blocked(self) -> bool:
        return len(self.dependencies) > 0

    def can_start(self, completed_task_ids: List[str] = None) -> bool:
        if completed_task_ids is None:
            return self.status == TaskStatus.PENDING and not self.is_blocked()
        return all(dep_id in completed_task_ids for dep_id in self.dependencies)

    def mark_started(self) -> None:
        """Mark task as started"""
        self.status = TaskStatus.IN_PROGRESS
        self.started_at = datetime.now()
        self.updated_at = datetime.now()

    def mark_completed(self, code: str = None, quality_score: float = 0.0) -> None:
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
        if code:
            self.generated_code = code
        self.quality_score = quality_score

    def mark_failed(self, reason: str = None) -> None:
        """Mark task as failed"""
        self.status = TaskStatus.FAILED
        self.updated_at = datetime.now()
        if reason:
            self.metadata["failure_reason"] = reason

    def add_dependency(self, task_id: str) -> None:
        if task_id not in self.dependencies:
            self.dependencies.append(task_id)

    def remove_dependency(self, task_id: str) -> None:
        if task_id in self.dependencies:
            self.dependencies.remove(task_id)

    def add_feedback(self, feedback: str) -> None:
        """Add feedback to history"""
        self.feedback_history.append(feedback)
        self.updated_at = datetime.now()

    def increment_iteration(self) -> None:
        """Increment iteration count"""
        self.iteration += 1
        self.iteration_count += 1
        self.updated_at = datetime.now()

    def get_duration(self) -> Optional[float]:
        """Get task duration in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    def to_dict(self) -> Dict[str, Any]:
        return self.dict()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """Create from dictionary"""
        return cls(**data)
