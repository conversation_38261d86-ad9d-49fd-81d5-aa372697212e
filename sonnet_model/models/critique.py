"""
Critique models for Sonnet Model
"""
from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field

from models.code_generation import ProgrammingLanguage, Framework


class IssueSeverity(str, Enum):
    """Issue severity enum"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class IssueType(str, Enum):
    """Issue type enum"""
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"
    RELIABILITY = "reliability"
    FUNCTIONALITY = "functionality"
    STYLE = "style"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    OTHER = "other"


class IssueLocation(BaseModel):
    """Issue location model"""
    filename: str
    line_start: int
    line_end: int
    column_start: Optional[int] = None
    column_end: Optional[int] = None
    code_snippet: Optional[str] = None


class Issue(BaseModel):
    """Issue model"""
    id: str = Field(default_factory=lambda: f"issue_{datetime.utcnow().timestamp()}")
    title: str
    description: str
    severity: IssueSeverity
    issue_type: IssueType
    location: Optional[IssueLocation] = None
    suggested_fix: Optional[str] = None
    references: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class CritiqueRequest(BaseModel):
    """Critique request model"""
    task_id: str
    code_files: List[Dict[str, str]]  # List of {filename: content} pairs
    language: ProgrammingLanguage
    framework: Optional[Framework] = None
    requirements: List[str] = Field(default_factory=list)
    context: Optional[str] = None
    iteration: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)


class CritiqueMetrics(BaseModel):
    """Metrics for code quality assessment"""
    # General metrics
    lines_of_code: int = 0
    comment_lines: int = 0
    comment_ratio: float = 0.0
    cyclomatic_complexity: Optional[float] = None
    
    # Language-specific metrics
    cognitive_complexity: Optional[float] = None
    maintainability_index: Optional[float] = None
    halstead_volume: Optional[float] = None
    
    # Test metrics
    test_coverage: Optional[float] = None
    tests_passed: Optional[int] = None
    tests_failed: Optional[int] = None
    
    # Performance metrics
    execution_time_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    
    # Custom metrics
    custom_metrics: Dict[str, Any] = Field(default_factory=dict)


class CritiqueResult(BaseModel):
    """Critique result model"""
    request_id: str
    task_id: str
    issues: List[Issue] = Field(default_factory=list)
    metrics: CritiqueMetrics = Field(default_factory=CritiqueMetrics)
    feedback: str
    suggestions: List[str] = Field(default_factory=list)
    quality_score: float  # 0-100 score
    passed: bool  # Whether the code passed the critique
    analysis_details: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    execution_time_ms: int = 0
    
    @property
    def has_critical_issues(self) -> bool:
        """Check if there are any critical issues"""
        return any(issue.severity == IssueSeverity.CRITICAL for issue in self.issues)
    
    @property
    def has_high_issues(self) -> bool:
        """Check if there are any high severity issues"""
        return any(issue.severity == IssueSeverity.HIGH for issue in self.issues)
    
    def get_issues_by_severity(self, severity: IssueSeverity) -> List[Issue]:
        """
        Get issues by severity
        
        Args:
            severity: Issue severity
            
        Returns:
            List of issues with the specified severity
        """
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_issues_by_type(self, issue_type: IssueType) -> List[Issue]:
        """
        Get issues by type
        
        Args:
            issue_type: Issue type
            
        Returns:
            List of issues with the specified type
        """
        return [issue for issue in self.issues if issue.issue_type == issue_type]
    
    def get_issues_by_file(self, filename: str) -> List[Issue]:
        """
        Get issues by filename
        
        Args:
            filename: Filename
            
        Returns:
            List of issues for the specified file
        """
        return [
            issue for issue in self.issues 
            if issue.location and issue.location.filename == filename
        ]
    
    def summarize(self) -> Dict[str, Any]:
        """
        Generate a summary of the critique result
        
        Returns:
            Dictionary with critique summary
        """
        severity_counts = {
            severity.value: len(self.get_issues_by_severity(severity))
            for severity in IssueSeverity
        }
        
        type_counts = {
            issue_type.value: len(self.get_issues_by_type(issue_type))
            for issue_type in IssueType
        }
        
        return {
            "task_id": self.task_id,
            "total_issues": len(self.issues),
            "severity_counts": severity_counts,
            "type_counts": type_counts,
            "quality_score": self.quality_score,
            "passed": self.passed,
            "metrics": self.metrics.dict(),
            "execution_time_ms": self.execution_time_ms,
        }
