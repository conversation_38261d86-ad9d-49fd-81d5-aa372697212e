"""
Task models for Sonnet Model
"""
import enum
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field


class TaskStatus(str, enum.Enum):
    """Task status enum"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    WAITING_FOR_DEPENDENCY = "waiting_for_dependency"
    WAITING_FOR_REVIEW = "waiting_for_review"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(int, enum.Enum):
    """Task priority enum"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class TaskType(str, enum.Enum):
    """Task type enum"""
    PLAN = "plan"
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    TESTING = "testing"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    DEPLOYMENT = "deployment"
    OTHER = "other"


class Task(BaseModel):
    """Task model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str
    title: str
    description: str
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.MEDIUM
    task_type: TaskType
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    assigned_to: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    iteration: int = 0
    max_iterations: int = 5
    
    def update_status(self, status: TaskStatus) -> None:
        """
        Update task status and updated_at timestamp
        
        Args:
            status: New task status
        """
        self.status = status
        self.updated_at = datetime.utcnow()
        
        if status == TaskStatus.COMPLETED and not self.completed_at:
            self.completed_at = datetime.utcnow()
    
    def increment_iteration(self) -> None:
        """Increment the iteration counter"""
        self.iteration += 1
        self.updated_at = datetime.utcnow()
    
    def is_blocked(self) -> bool:
        """Check if the task is blocked by dependencies"""
        return self.status == TaskStatus.WAITING_FOR_DEPENDENCY
    
    def is_completed(self) -> bool:
        """Check if the task is completed"""
        return self.status == TaskStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """Check if the task is failed"""
        return self.status == TaskStatus.FAILED
    
    def is_active(self) -> bool:
        """Check if the task is active"""
        return self.status in (TaskStatus.PENDING, TaskStatus.IN_PROGRESS)
    
    def has_reached_max_iterations(self) -> bool:
        """Check if the task has reached the maximum number of iterations"""
        return self.iteration >= self.max_iterations


class Project(BaseModel):
    """Project model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    status: str = "active"
    metadata: Dict[str, Any] = Field(default_factory=dict)
    tasks: List[Task] = Field(default_factory=list)
    
    def add_task(self, task: Task) -> None:
        """
        Add a task to the project
        
        Args:
            task: Task to add
        """
        self.tasks.append(task)
        self.updated_at = datetime.utcnow()
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        Get a task by ID
        
        Args:
            task_id: Task ID
            
        Returns:
            Task if found, None otherwise
        """
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None
    
    def update_task(self, task_id: str, **kwargs) -> bool:
        """
        Update a task by ID
        
        Args:
            task_id: Task ID
            **kwargs: Task attributes to update
            
        Returns:
            True if the task was updated, False otherwise
        """
        task = self.get_task(task_id)
        if not task:
            return False
        
        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        task.updated_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        return True
    
    def remove_task(self, task_id: str) -> bool:
        """
        Remove a task by ID
        
        Args:
            task_id: Task ID
            
        Returns:
            True if the task was removed, False otherwise
        """
        for i, task in enumerate(self.tasks):
            if task.id == task_id:
                self.tasks.pop(i)
                self.updated_at = datetime.utcnow()
                return True
        return False
    
    def get_pending_tasks(self) -> List[Task]:
        """
        Get pending tasks
        
        Returns:
            List of pending tasks
        """
        return [task for task in self.tasks if task.status == TaskStatus.PENDING]
    
    def get_active_tasks(self) -> List[Task]:
        """
        Get active tasks
        
        Returns:
            List of active tasks
        """
        return [
            task for task in self.tasks 
            if task.status in (TaskStatus.PENDING, TaskStatus.IN_PROGRESS)
        ]
    
    def get_completed_tasks(self) -> List[Task]:
        """
        Get completed tasks
        
        Returns:
            List of completed tasks
        """
        return [task for task in self.tasks if task.status == TaskStatus.COMPLETED]
    
    def get_failed_tasks(self) -> List[Task]:
        """
        Get failed tasks
        
        Returns:
            List of failed tasks
        """
        return [task for task in self.tasks if task.status == TaskStatus.FAILED]
    
    def is_completed(self) -> bool:
        """
        Check if the project is completed
        
        Returns:
            True if all tasks are completed, False otherwise
        """
        if not self.tasks:
            return False
        
        return all(
            task.status in (TaskStatus.COMPLETED, TaskStatus.CANCELLED)
            for task in self.tasks
        )
