"""
Code generation models for Sonnet Model
"""
from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional

from pydantic import BaseModel, Field


class ProgrammingLanguage(str, Enum):
    """Programming language enum"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CSHARP = "csharp"
    CPP = "cpp"
    GO = "go"
    RUST = "rust"
    PHP = "php"
    RUBY = "ruby"
    SWIFT = "swift"
    KOTLIN = "kotlin"
    SCALA = "scala"
    HTML = "html"
    CSS = "css"
    SQL = "sql"
    SHELL = "shell"
    OTHER = "other"


class Framework(str, Enum):
    """Framework enum"""
    # Python frameworks
    DJANGO = "django"
    FLASK = "flask"
    FASTAPI = "fastapi"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    PANDAS = "pandas"
    NUMPY = "numpy"
    
    # JavaScript/TypeScript frameworks
    REACT = "react"
    ANGULAR = "angular"
    VUE = "vue"
    NEXT = "next"
    NODE = "node"
    EXPRESS = "express"
    
    # Other frameworks
    SPRING = "spring"
    DOTNET = "dotnet"
    LARAVEL = "laravel"
    RAILS = "rails"
    
    # None
    NONE = "none"


class GenerationRequest(BaseModel):
    """Code generation request model"""
    task_id: str
    language: ProgrammingLanguage
    framework: Optional[Framework] = None
    description: str
    requirements: List[str]
    context: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    examples: List[str] = Field(default_factory=list)
    constraints: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    iteration: int = 0
    max_tokens: int = 4096
    temperature: float = 0.2
    created_at: datetime = Field(default_factory=datetime.utcnow)


class CodeFile(BaseModel):
    """Code file model"""
    filename: str
    content: str
    language: ProgrammingLanguage
    metadata: Dict[str, Any] = Field(default_factory=dict)


class GenerationResponse(BaseModel):
    """Code generation response model"""
    request_id: str
    task_id: str
    files: List[CodeFile]
    explanation: str
    suggestions: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    execution_time_ms: int = 0
