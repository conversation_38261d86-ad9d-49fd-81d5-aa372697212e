"""
API Routes for the Agentic Code Development System
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, FastAPI
from pydantic import BaseModel, Field

from system_integration import AgenticSystem

app = FastAPI()

router = APIRouter(prefix="/api/v1")
logger = logging.getLogger(__name__)

# Global system instance - will be set by main.py
system_instance: Optional[AgenticSystem] = None


class ProjectRequest(BaseModel):
    name: str = Field(..., description="Project name")
    description: str = Field(..., description="Project description/plan")
    user_input: str = Field(..., description="User's detailed requirements")
    language: str = Field(default="python", description="Programming language")


class ProjectResponse(BaseModel):
    project_id: str
    status: str
    message: str


class StatusResponse(BaseModel):
    system_state: str
    current_project: Optional[str]
    config: Dict[str, Any]


def set_system_instance(system: AgenticSystem) -> None:
    """Set the system instance (called from main.py)"""
    global system_instance
    system_instance = system


def get_system_instance() -> AgenticSystem:
    """Get the system instance"""
    global system_instance
    if system_instance is None:
        raise HTTPException(
            status_code=500,
            detail="System not initialized. Please ensure the application is properly started."
        )
    return system_instance


@router.post("/projects", response_model=ProjectResponse)
async def create_project(request: ProjectRequest, background_tasks: BackgroundTasks):
    """Create a new project and start processing"""
    system = get_system_instance()
    
    if system.state != "IDLE":
        raise HTTPException(
            status_code=409, 
            detail="System is currently processing another project"
        )
    
    # Start processing in background
    background_tasks.add_task(
        process_project_background, 
        system, 
        request.user_input, 
        request.name
    )
    
    return ProjectResponse(
        project_id=request.name,
        status="processing",
        message="Project processing started"
    )


async def process_project_background(system: AgenticSystem, user_input: str, project_name: str):
    """Background task to process the project"""
    result = await system.process_user_plan(user_input, project_name)
    logger.info(f"Project {project_name} completed with result: {len(result)} characters")


@router.get("/status", response_model=StatusResponse)
async def get_system_status():
    """Get current system status"""
    system = get_system_instance()
    status = await system.get_system_status()
    
    return StatusResponse(
        system_state=status["state"],
        current_project=status["current_project"],
        config=status["config"]
    )


@router.get("/projects/{project_id}/status")
async def get_project_status(project_id: str):
    """Get status of a specific project"""
    system = get_system_instance()
    
    if system.current_project_id != project_id:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return {
        "project_id": project_id,
        "state": system.state,
        "current_project": system.current_project_id
    }


@router.post("/projects/{project_id}/stop")
async def stop_project(project_id: str):
    """Stop processing a project"""
    system = get_system_instance()
    
    if system.current_project_id != project_id:
        raise HTTPException(status_code=404, detail="Project not found")
    
    if system.state == "IDLE":
        raise HTTPException(status_code=400, detail="No active project to stop")
    
    # Reset system state
    system.state = "IDLE"
    system.current_project_id = None
    
    return {"message": "Project stopped successfully"}


@app.get("/health")
def health_check():
    return {"status": "ok"}


# Additional utility endpoints
@router.get("/config")
async def get_config():
    """Get current system configuration"""
    system = get_system_instance()
    return system.config


@router.post("/config")
async def update_config(config: Dict[str, Any]):
    """Update system configuration"""
    global system_instance
    system_instance = AgenticSystem(config)
    return {"message": "Configuration updated successfully"}
