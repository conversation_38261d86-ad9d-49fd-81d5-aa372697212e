"""
ROBUST ITERATIVE IMPROVEMENT SYSTEM

This addresses all the critical issues:
1. NO ARBITRARY ITERATION LIMITS - only quality matters
2. DETAILED, ACTIONABLE CRITIQUE with specific metrics
3. REAL CODE IMPROVEMENT tracking
4. COMPREHENSIVE QUALITY ASSESSMENT
5. HONEST FEEDBACK LOOP

This is the REAL system you wanted!
"""

import asyncio
import json
import logging
import hashlib
from typing import Dict, Any, List, Tuple
import httpx
from pathlib import Path

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class RobustIterativeSystem:
    """Robust iterative improvement system with real quality focus"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Quality-focused settings (NO ARBITRARY LIMITS!)
        self.minimum_quality_threshold = 8.5  # High standard
        self.minimum_improvement_threshold = 0.2  # Must improve by at least 0.2 points
        self.stagnation_limit = 3  # Stop if no improvement for 3 iterations
        self.absolute_max_iterations = 15  # Safety limit only
        
        # Quality metrics we track
        self.quality_metrics = [
            "readability",
            "maintainability", 
            "efficiency",
            "security",
            "error_handling",
            "documentation",
            "best_practices"
        ]
    
    async def improve_code_until_excellent(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Improve code until it reaches excellent quality - NO ARBITRARY LIMITS"""
        
        print(f"🎯 ROBUST IMPROVEMENT: {task['path']}")
        print(f"📝 {task['description']}")
        print(f"🎯 Target Quality: {self.minimum_quality_threshold}/10")
        print("-" * 60)
        
        iteration = 1
        current_code = ""
        quality_history = []
        stagnation_count = 0
        
        while iteration <= self.absolute_max_iterations:
            print(f"\n🔄 ITERATION {iteration}")
            print("-" * 30)
            
            # Generate code
            print("🤖 CODE GENERATOR: Working...")
            new_code = await self._generate_code_with_context(task, current_code, quality_history, iteration)
            
            if not new_code:
                print("❌ Code generation failed")
                break
            
            # Check for actual changes
            code_changed = self._detect_real_changes(current_code, new_code)
            print(f"📄 Code changed: {code_changed}")
            print(f"📄 Code length: {len(new_code)} characters")
            
            # Get comprehensive critique
            print("🔍 CRITIQUE ENGINE: Analyzing...")
            critique = await self._get_comprehensive_critique(new_code, task, quality_history)
            
            if not critique:
                print("❌ Critique failed")
                break
            
            # Extract quality metrics
            overall_quality = critique.get('overall_quality', 0)
            metrics = critique.get('quality_metrics', {})
            issues = critique.get('specific_issues', [])
            improvements = critique.get('improvements_made', [])
            
            print(f"📊 Overall Quality: {overall_quality:.1f}/10")
            print(f"📈 Quality Metrics:")
            for metric, score in metrics.items():
                print(f"   {metric}: {score:.1f}/10")
            
            print(f"🔍 Specific Issues: {len(issues)}")
            for i, issue in enumerate(issues[:3], 1):
                print(f"   {i}. {issue}")
            
            if improvements:
                print(f"✅ Improvements Made: {len(improvements)}")
                for i, improvement in enumerate(improvements[:2], 1):
                    print(f"   {i}. {improvement}")
            
            # Track quality history
            quality_history.append({
                'iteration': iteration,
                'overall_quality': overall_quality,
                'metrics': metrics,
                'issues': issues,
                'code_length': len(new_code),
                'code_changed': code_changed
            })
            
            # Check if we've reached excellent quality
            if overall_quality >= self.minimum_quality_threshold:
                print(f"🎉 EXCELLENT QUALITY ACHIEVED! ({overall_quality:.1f}/{self.minimum_quality_threshold})")
                return self._create_success_result(task, new_code, quality_history, iteration)
            
            # Check for improvement
            if len(quality_history) > 1:
                previous_quality = quality_history[-2]['overall_quality']
                improvement = overall_quality - previous_quality
                
                print(f"📈 Quality improvement: {improvement:+.1f}")
                
                if improvement < self.minimum_improvement_threshold:
                    stagnation_count += 1
                    print(f"⚠️ Stagnation count: {stagnation_count}/{self.stagnation_limit}")
                else:
                    stagnation_count = 0  # Reset if we see improvement
                
                # Stop if stagnating
                if stagnation_count >= self.stagnation_limit:
                    print(f"🛑 STOPPING: No significant improvement for {self.stagnation_limit} iterations")
                    return self._create_stagnation_result(task, new_code, quality_history, iteration)
            
            current_code = new_code
            iteration += 1
            
            await asyncio.sleep(1)  # Brief pause
        
        print(f"🛑 STOPPING: Reached safety limit ({self.absolute_max_iterations} iterations)")
        return self._create_limit_result(task, current_code, quality_history, iteration - 1)
    
    async def _generate_code_with_context(self, task: Dict[str, Any], current_code: str, 
                                        quality_history: List[Dict], iteration: int) -> str:
        """Generate code with full context and specific improvement instructions"""
        
        if iteration == 1:
            prompt = f"""You are an expert developer. Create EXCELLENT {task['path']} code.

TASK: {task['description']}

REQUIREMENTS:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Create code that achieves:
- Readability: 9+/10 (clear, well-structured)
- Maintainability: 9+/10 (modular, extensible)
- Efficiency: 8+/10 (optimized, no waste)
- Security: 9+/10 (secure by design)
- Error handling: 9+/10 (comprehensive)
- Documentation: 9+/10 (thorough)
- Best practices: 9+/10 (industry standard)

EXCELLENT CODE:"""
        else:
            # Build specific improvement instructions from critique history
            last_critique = quality_history[-1] if quality_history else {}
            specific_issues = last_critique.get('issues', [])
            current_metrics = last_critique.get('metrics', {})
            
            improvement_instructions = []
            for metric, score in current_metrics.items():
                if score < 8.5:
                    improvement_instructions.append(f"IMPROVE {metric.upper()}: Current {score:.1f}/10, target 9+/10")
            
            issues_text = ""
            if specific_issues:
                issues_text = f"""
CRITICAL ISSUES TO FIX:
{chr(10).join(f"- {issue}" for issue in specific_issues)}

YOU MUST FIX EVERY ISSUE ABOVE!
"""
            
            prompt = f"""You are an expert developer. IMPROVE this {task['path']} code.

TASK: {task['description']}

CURRENT CODE:
```
{current_code}
```

CURRENT QUALITY SCORES:
{chr(10).join(f"- {metric}: {score:.1f}/10" for metric, score in current_metrics.items())}

IMPROVEMENT TARGETS:
{chr(10).join(improvement_instructions)}
{issues_text}
CRITICAL: Make SIGNIFICANT improvements. Don't just make cosmetic changes!
Address every issue specifically. Aim for 9+/10 in all metrics.

SIGNIFICANTLY IMPROVED CODE:"""
        
        response = await self._send_llm_request(prompt, temperature=0.1)
        return self._extract_code_from_response(response, task['path'])
    
    async def _get_comprehensive_critique(self, code: str, task: Dict[str, Any], 
                                        quality_history: List[Dict]) -> Dict[str, Any]:
        """Get comprehensive, actionable critique with detailed metrics"""
        
        prompt = f"""You are a senior code reviewer with VERY HIGH STANDARDS. Analyze this {task['path']} code.

TASK: {task['description']}

REQUIREMENTS:
{chr(10).join(f"- {req}" for req in task['requirements'])}

CODE TO REVIEW:
```
{code}
```

Provide DETAILED analysis in JSON format:

{{
    "overall_quality": <number 1-10, be STRICT>,
    "quality_metrics": {{
        "readability": <1-10, clear structure, naming, comments>,
        "maintainability": <1-10, modular, extensible, clean>,
        "efficiency": <1-10, optimized, no waste>,
        "security": <1-10, secure by design, no vulnerabilities>,
        "error_handling": <1-10, comprehensive error management>,
        "documentation": <1-10, thorough docs and comments>,
        "best_practices": <1-10, follows industry standards>
    }},
    "specific_issues": [
        "SPECIFIC issue 1 with EXACT location and fix needed",
        "SPECIFIC issue 2 with EXACT location and fix needed"
    ],
    "improvements_made": [
        "Specific improvement 1 (if any from previous iteration)",
        "Specific improvement 2 (if any from previous iteration)"
    ],
    "actionable_fixes": [
        "EXACT fix 1: Replace line X with Y",
        "EXACT fix 2: Add Z function to handle Y"
    ],
    "reasoning": "Brief explanation of scores and main issues"
}}

CRITICAL REQUIREMENTS:
- Be SPECIFIC and ACTIONABLE in feedback
- Give EXACT line numbers and fixes
- Be STRICT with scoring (9+ only for excellent code)
- Identify REAL issues, not cosmetic ones

ANALYSIS:"""
        
        response = await self._send_llm_request(prompt, temperature=0.05)
        return self._parse_json_response(response)
    
    def _detect_real_changes(self, old_code: str, new_code: str) -> bool:
        """Detect if real changes were made (not just whitespace)"""
        
        if not old_code:
            return True
        
        # Normalize whitespace and compare
        old_normalized = ' '.join(old_code.split())
        new_normalized = ' '.join(new_code.split())
        
        # Check for meaningful changes
        if old_normalized == new_normalized:
            return False
        
        # Check if change is significant (more than 5% difference)
        change_ratio = abs(len(new_normalized) - len(old_normalized)) / len(old_normalized)
        return change_ratio > 0.05
    
    def _create_success_result(self, task: Dict[str, Any], code: str, 
                             quality_history: List[Dict], iterations: int) -> Dict[str, Any]:
        """Create success result"""
        
        final_quality = quality_history[-1] if quality_history else {}
        
        return {
            "success": True,
            "reason": "excellent_quality_achieved",
            "task": task,
            "final_code": code,
            "iterations": iterations,
            "final_quality": final_quality.get('overall_quality', 0),
            "quality_metrics": final_quality.get('metrics', {}),
            "quality_history": quality_history
        }
    
    def _create_stagnation_result(self, task: Dict[str, Any], code: str, 
                                quality_history: List[Dict], iterations: int) -> Dict[str, Any]:
        """Create stagnation result"""
        
        final_quality = quality_history[-1] if quality_history else {}
        
        return {
            "success": final_quality.get('overall_quality', 0) >= (self.minimum_quality_threshold - 1),
            "reason": "stagnation_detected",
            "task": task,
            "final_code": code,
            "iterations": iterations,
            "final_quality": final_quality.get('overall_quality', 0),
            "quality_metrics": final_quality.get('metrics', {}),
            "quality_history": quality_history
        }
    
    def _create_limit_result(self, task: Dict[str, Any], code: str, 
                           quality_history: List[Dict], iterations: int) -> Dict[str, Any]:
        """Create safety limit result"""
        
        final_quality = quality_history[-1] if quality_history else {}
        
        return {
            "success": final_quality.get('overall_quality', 0) >= (self.minimum_quality_threshold - 1),
            "reason": "safety_limit_reached",
            "task": task,
            "final_code": code,
            "iterations": iterations,
            "final_quality": final_quality.get('overall_quality', 0),
            "quality_metrics": final_quality.get('metrics', {}),
            "quality_history": quality_history
        }
    
    async def _send_llm_request(self, prompt: str, temperature: float = 0.2) -> str:
        """Send request to LLM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": temperature,
                            "top_p": 0.9,
                            "num_predict": 4096
                        }
                    },
                    timeout=180.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "")
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return ""
    
    def _extract_code_from_response(self, response: str, file_path: str) -> str:
        """Extract code from LLM response"""
        
        if file_path.endswith('.py'):
            patterns = ["```python", "```"]
        elif file_path.endswith('.js'):
            patterns = ["```javascript", "```js", "```"]
        else:
            patterns = ["```"]
        
        for pattern in patterns:
            if pattern in response:
                start = response.find(pattern) + len(pattern)
                if pattern != "```":
                    start = response.find("\n", start) + 1
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        
        return response.strip()
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response with better error handling"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except Exception as e:
            self.logger.error(f"JSON parsing failed: {e}")
        
        return {
            "overall_quality": 5,
            "quality_metrics": {metric: 5 for metric in self.quality_metrics},
            "specific_issues": ["Could not parse critique response"],
            "improvements_made": [],
            "actionable_fixes": [],
            "reasoning": "Critique parsing failed"
        }


async def test_robust_system():
    """Test the robust iterative system"""
    
    print("🎯 TESTING ROBUST ITERATIVE SYSTEM")
    print("Quality-focused, no arbitrary limits!")
    print("=" * 60)
    
    system = RobustIterativeSystem()
    
    # Test with a challenging task
    test_task = {
        "path": "secure_auth.py",
        "description": "Secure user authentication system",
        "requirements": [
            "JWT token-based authentication",
            "Password hashing with salt",
            "Rate limiting for login attempts",
            "Secure session management",
            "Input validation and sanitization",
            "Comprehensive error handling",
            "Detailed logging for security events"
        ]
    }
    
    result = await system.improve_code_until_excellent(test_task)
    
    print(f"\n📊 FINAL RESULTS:")
    print(f"✅ Success: {result['success']}")
    print(f"🎯 Reason: {result['reason']}")
    print(f"🔄 Iterations: {result['iterations']}")
    print(f"📊 Final Quality: {result['final_quality']:.1f}/10")
    
    if 'quality_metrics' in result:
        print(f"📈 Quality Breakdown:")
        for metric, score in result['quality_metrics'].items():
            print(f"   {metric}: {score:.1f}/10")
    
    # Save the result
    output_path = Path("robust_system_result.py")
    with open(output_path, 'w') as f:
        f.write(f"# Generated by Robust Iterative System\n")
        f.write(f"# Iterations: {result['iterations']}\n")
        f.write(f"# Final Quality: {result['final_quality']:.1f}/10\n\n")
        f.write(result['final_code'])
    
    print(f"💾 Saved result to: {output_path}")
    
    return result


async def main():
    """Run the robust system test"""
    
    result = await test_robust_system()
    
    print(f"\n🎉 ROBUST SYSTEM TEST COMPLETED!")
    
    if result['success'] and result['final_quality'] >= 8.5:
        print("🚀 EXCELLENT! The robust system achieved high quality!")
    elif result['success']:
        print("✅ Good quality achieved, but room for improvement")
    else:
        print("⚠️ System needs more work")


if __name__ == "__main__":
    asyncio.run(main())
