#!/usr/bin/env python3
"""
Sonnet Model: Agentic Code Development System
Main entry point for the application
"""
import asyncio
import logging
import os
import sys
from pathlib import Path

import uvicorn
import yaml
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.routes import router as api_router, set_system_instance
from shared.logging_config import configure_logging
from shared.state import AppState
from system_integration import AgenticSystem
from task_manager.orchestrator import TaskOrchestrator
from utils.config_loader import load_config


def create_app(config_path: str = None) -> FastAPI:
    """Create and configure the FastAPI application"""
    # Load configuration
    config = load_config(config_path)
    
    # Configure logging
    configure_logging(config["system"]["log_level"], config["monitoring"]["logging"])
    
    # Initialize the AgenticSystem with the loaded configuration
    agentic_system = AgenticSystem(config)

    # Create FastAPI app
    app = FastAPI(
        title=config["system"]["name"],
        description="Agentic Code Development System",
        version=config["system"]["version"],
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
    )

    # Set the system instance on the application state
    set_system_instance(app, agentic_system)
    
    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config["api"]["cors_origins"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Initialize application state
    app.state.config = config
    app.state.app_state = AppState()
    app.state.agentic_system = agentic_system
    
    # Include API routes
    app.include_router(api_router, prefix="/api")
    
    # Startup and shutdown events
    @app.on_event("startup")
    async def startup_event():
        """Initialize services on startup"""
        logging.info("Starting Sonnet Model system")
        app.state.app_state.orchestrator = TaskOrchestrator(config["task_manager"])
        await app.state.app_state.orchestrator.initialize()
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Clean up resources on shutdown"""
        logging.info("Shutting down Sonnet Model system")
        if app.state.app_state.orchestrator:
            await app.state.app_state.orchestrator.shutdown()
    
    return app


def main():
    """Main entry point for the application"""
    config_path = os.environ.get("SONNET_CONFIG", "config/config.yaml")
    config = load_config(config_path)
    
    # Run the FastAPI application
    uvicorn.run(
        "main:create_app()",
        host=config["api"]["host"],
        port=config["api"]["port"],
        reload=config["api"]["debug"],
        log_level=config["system"]["log_level"].lower(),
    )


if __name__ == "__main__":
    main()
