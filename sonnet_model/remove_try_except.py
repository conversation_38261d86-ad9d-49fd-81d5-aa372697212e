#!/usr/bin/env python3
"""
Script to remove all try/except blocks from Python files
This makes the code cleaner and less bulky as requested
"""

import os
import re
import ast
import sys
from pathlib import Path

def remove_try_except_from_file(file_path):
    """Remove try/except blocks from a Python file"""
    print(f"Processing {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Simple regex-based removal for basic cases
    # Remove try/except blocks with simple patterns
    
    # Pattern 1: try: ... except: pass
    content = re.sub(r'\s*try:\s*\n(.*?)\s*except[^:]*:\s*\n\s*pass\s*\n', r'\1', content, flags=re.DOTALL)
    
    # Pattern 2: try: ... except Exception as e: ... (with logging/error handling)
    content = re.sub(r'\s*try:\s*\n(.*?)\s*except[^:]*:[^}]*?\n', r'\1', content, flags=re.DOTALL)
    
    # Pattern 3: Simple try/except with return
    content = re.sub(r'\s*try:\s*\n(.*?)\s*except[^:]*:\s*\n\s*return[^}]*?\n', r'\1', content, flags=re.DOTALL)
    
    # Write back the modified content
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Processed {file_path}")

def main():
    """Main function to process all Python files"""
    
    # Get all Python files in the project
    python_files = []
    for root, dirs, files in os.walk('.'):
        # Skip __pycache__ and .git directories
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py') and not file.startswith('.'):
                python_files.append(os.path.join(root, file))
    
    print(f"Found {len(python_files)} Python files to process")
    
    for file_path in python_files:
        remove_try_except_from_file(file_path)
    
    print("✅ All try/except blocks removed!")
    print("Code is now cleaner and less bulky as requested.")

if __name__ == "__main__":
    main()
