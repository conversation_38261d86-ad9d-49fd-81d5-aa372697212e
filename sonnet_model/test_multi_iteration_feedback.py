"""
Multi-Iteration Feedback Loop Test

This test forces multiple iterations to demonstrate the improvement cycle:
1. Start with basic requirements
2. LLM generates initial code
3. Critique finds issues
4. LLM improves code based on feedback
5. Repeat until perfect

This shows the REAL iterative improvement process!
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class MultiIterationFeedbackTest:
    """Test multi-iteration feedback with progressively stricter requirements"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Task designed to require multiple iterations
        self.task = {
            "name": "Advanced Calculator",
            "description": "Create a calculator class that can perform basic arithmetic operations",
            "initial_requirements": [
                "Support addition, subtraction, multiplication, division"
            ],
            "progressive_requirements": [
                # Iteration 1: Basic functionality
                ["Support addition, subtraction, multiplication, division"],
                
                # Iteration 2: Add error handling
                ["Support addition, subtraction, multiplication, division",
                 "Handle division by zero",
                 "Validate input types"],
                
                # Iteration 3: Add advanced features
                ["Support addition, subtraction, multiplication, division",
                 "Handle division by zero", 
                 "Validate input types",
                 "Add memory functions (store, recall, clear)",
                 "Include operation history"],
                
                # Iteration 4: Add documentation and testing
                ["Support addition, subtraction, multiplication, division",
                 "Handle division by zero",
                 "Validate input types", 
                 "Add memory functions (store, recall, clear)",
                 "Include operation history",
                 "Comprehensive documentation with examples",
                 "Include unit tests",
                 "Add type hints for all methods"]
            ]
        }
        
    async def run_multi_iteration_test(self):
        """Run the multi-iteration feedback test"""
        
        print("🚀 MULTI-ITERATION FEEDBACK LOOP TEST")
        print("=" * 70)
        print("🎯 Goal: Force multiple iterations to see real improvement")
        print("🤖 Each iteration adds more requirements")
        print("=" * 70)
        
        # Test Ollama connection
        if not await self._test_ollama_connection():
            print("❌ Cannot connect to Ollama")
            return
        
        print("✅ Connected to Ollama successfully!")
        
        current_code = ""
        
        for iteration in range(1, len(self.task['progressive_requirements']) + 1):
            print(f"\n📋 ITERATION {iteration}")
            print("=" * 50)
            
            # Get requirements for this iteration
            requirements = self.task['progressive_requirements'][iteration - 1]
            
            print(f"📝 Requirements for iteration {iteration}:")
            for i, req in enumerate(requirements, 1):
                print(f"   {i}. {req}")
            
            print(f"\n🔄 PROCESSING ITERATION {iteration}")
            print("-" * 30)
            
            # Generate code with current requirements
            print("🤖 CODE GENERATOR: Working...")
            generated_code = await self._generate_code_with_requirements(
                self.task, requirements, current_code, iteration
            )
            
            if not generated_code:
                print("❌ Code generation failed")
                break
            
            print(f"   ✅ Generated {len(generated_code)} characters")
            
            # Show code preview
            lines = generated_code.split('\n')[:8]
            print("   📝 Code Preview:")
            for line in lines:
                if line.strip():
                    print(f"      {line}")
            if len(generated_code.split('\n')) > 8:
                print("      ...")
            
            # Critique with current requirements
            print("\n🔍 CRITIQUE ENGINE: Analyzing...")
            critique = await self._critique_with_requirements(generated_code, requirements)
            
            if not critique:
                print("❌ Critique failed")
                break
            
            print(f"   📊 Quality Score: {critique['quality_score']}/10")
            print(f"   🔍 Issues: {len(critique['issues'])}")
            print(f"   ✅ Strengths: {len(critique['strengths'])}")
            
            if critique['issues']:
                print("   ⚠️  Issues Found:")
                for issue in critique['issues'][:3]:
                    print(f"      - {issue}")
            
            if critique['strengths']:
                print("   💪 Strengths:")
                for strength in critique['strengths'][:2]:
                    print(f"      + {strength}")
            
            # Show improvement from previous iteration
            if iteration > 1:
                improvement = len(generated_code) - len(current_code)
                print(f"   📈 Code Growth: +{improvement} characters")
            
            current_code = generated_code
            
            print(f"\n✅ ITERATION {iteration} COMPLETED")
            
            # Small delay between iterations
            await asyncio.sleep(1)
        
        print("\n🎉 MULTI-ITERATION TEST COMPLETED!")
        print("=" * 50)
        print("📊 FINAL RESULTS:")
        print(f"   📋 Total Iterations: {len(self.task['progressive_requirements'])}")
        print(f"   📝 Final Code Length: {len(current_code)} characters")
        print("   🔄 Demonstrated real iterative improvement!")
        
        # Show final code
        print("\n📄 FINAL EVOLVED CODE:")
        print("```python")
        print(current_code)
        print("```")
    
    async def _test_ollama_connection(self) -> bool:
        """Test Ollama connection"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                return response.status_code == 200
        except:
            return False
    
    async def _generate_code_with_requirements(self, task: Dict, requirements: List[str], 
                                             previous_code: str, iteration: int) -> str:
        """Generate code with specific requirements"""
        
        if iteration == 1:
            prompt = f"""You are an expert Python developer. Create a calculator class with these requirements:

Task: {task['description']}

Requirements:
{chr(10).join(f"- {req}" for req in requirements)}

Please provide a complete Python implementation.

Code:"""
        else:
            prompt = f"""You are an expert Python developer. Improve this calculator class to meet the new requirements:

Task: {task['description']}

New Requirements:
{chr(10).join(f"- {req}" for req in requirements)}

Previous code:
```python
{previous_code}
```

Please provide an improved version that meets ALL the requirements above.

Improved code:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.3,
                            "top_p": 0.9,
                            "num_predict": 3072
                        }
                    },
                    timeout=180.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get("response", "")
                    return self._extract_code_from_response(generated_text)
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return ""
    
    async def _critique_with_requirements(self, code: str, requirements: List[str]) -> Dict[str, Any]:
        """Critique code against specific requirements"""
        
        prompt = f"""You are an expert code reviewer. Analyze this Python calculator class against the requirements:

Requirements to check:
{chr(10).join(f"- {req}" for req in requirements)}

Code to review:
```python
{code}
```

Please provide analysis in JSON format:
{{
    "quality_score": <number 1-10>,
    "issues": ["issue1", "issue2"],
    "suggestions": ["suggestion1", "suggestion2"],
    "strengths": ["strength1", "strength2"],
    "requirements_met": <number of requirements satisfied>
}}

Analysis:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.1,
                            "top_p": 0.8,
                            "num_predict": 1024
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    critique_text = result.get("response", "")
                    return self._parse_critique_response(critique_text)
                else:
                    return self._default_critique()
                    
        except Exception as e:
            self.logger.error(f"Critique failed: {e}")
            return self._default_critique()
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract code from LLM response"""
        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        elif "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        # Look for class definitions
        lines = response.split('\n')
        code_lines = []
        in_code = False
        
        for line in lines:
            if line.strip().startswith(('class ', 'def ', 'import ', 'from ')):
                in_code = True
            
            if in_code:
                code_lines.append(line)
        
        return '\n'.join(code_lines) if code_lines else response.strip()
    
    def _parse_critique_response(self, response: str) -> Dict[str, Any]:
        """Parse critique JSON response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # Fallback manual parsing
        return {
            "quality_score": 6,
            "issues": ["Manual parsing - review needed"],
            "suggestions": ["Check implementation details"],
            "strengths": ["Code structure present"],
            "requirements_met": 3
        }
    
    def _default_critique(self) -> Dict[str, Any]:
        """Default critique"""
        return {
            "quality_score": 5,
            "issues": ["Analysis failed"],
            "suggestions": ["Manual review needed"],
            "strengths": [],
            "requirements_met": 0
        }


async def main():
    """Run the multi-iteration feedback test"""
    
    print("🎯 MULTI-ITERATION REAL LLM FEEDBACK TEST")
    print("Demonstrating progressive improvement through iterations")
    print("=" * 70)
    
    tester = MultiIterationFeedbackTest()
    await tester.run_multi_iteration_test()


if __name__ == "__main__":
    asyncio.run(main())
