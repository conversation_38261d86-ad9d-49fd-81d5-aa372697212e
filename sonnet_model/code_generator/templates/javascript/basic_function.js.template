/**
 * {{function_name}} - {{description}}
 * {% for param in params %}
 * @param {{{param.type}}} {{param.name}} - {{param.description}}{% endfor %}{% if return_type %}
 * @returns {{{return_type}}} {{return_description}}{% endif %}
 */
function {{function_name}}({% for param in params %}{{param.name}}{% if param.default %} = {{param.default}}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}) {
  // TODO: Implement {{function_name}}
  {% if return_type %}return {% if return_type == "boolean" %}false{% elif return_type == "number" %}0{% elif return_type == "string" %}""{% elif return_type == "Array" %}[]{% elif return_type == "Object" %}{}{% else %}null{% endif %};{% endif %}
}
