/**
 * {{class_name}} - {{description}}
 */
class {{class_name}} {
  /**
   * Create a new {{class_name}}
   * {% for param in params %}
   * @param {{{param.type}}} {{param.name}} - {{param.description}}{% endfor %}
   */
  constructor({% for param in params %}{{param.name}}{% if param.default %} = {{param.default}}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}) {
    {% for param in params %}
    this.{{param.name}} = {{param.name}};{% endfor %}
  }
  {% for method in methods %}

  /**
   * {{method.description}}
   * {% for param in method.params %}
   * @param {{{param.type}}} {{param.name}} - {{param.description}}{% endfor %}{% if method.return_type %}
   * @returns {{{method.return_type}}} {{method.return_description}}{% endif %}
   */
  {{method.name}}({% for param in method.params %}{{param.name}}{% if param.default %} = {{param.default}}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}) {
    // TODO: Implement {{method.name}}
  }{% endfor %}
}
