"""
{{function_name}} - {{description}}
"""
from typing import Dict, List, Any, Optional


def {{function_name}}({% for param in params %}{{param.name}}: {{param.type}}{% if param.default %} = {{param.default}}{% endif %}{% if not loop.last %}, {% endif %}{% endfor %}){% if return_type %} -> {{return_type}}{% endif %}:
    """
    {{description}}
    {% for param in params %}
    Args:
        {{param.name}}: {{param.description}}{% endfor %}{% if return_type %}
    
    Returns:
        {{return_description}}{% endif %}
    """
    # TODO: Implement {{function_name}}
    {% if return_type %}return {% if return_type == "bool" %}False{% elif return_type == "int" %}0{% elif return_type == "float" %}0.0{% elif return_type == "str" %}""{% elif return_type == "List" %}[]{% elif return_type == "Dict" %}{}{% else %}None{% endif %}{% endif %}
