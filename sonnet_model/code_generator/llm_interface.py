"""
LLM Interface for Code Generator
Provides an abstraction layer for different LLM backends
"""
import abc
import asyncio
import logging
import os
import json
from typing import Dict, Any, List, Optional, Union

import httpx


class LLMInterface(abc.ABC):
    """
    Abstract base class for LLM interfaces
    """
    
    @abc.abstractmethod
    async def initialize(self) -> None:
        """Initialize the LLM interface"""
        pass
    
    @abc.abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the LLM interface"""
        pass
    
    @abc.abstractmethod
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 4096,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        Generate text from prompt

        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation

        Returns:
            Dictionary with generation result:
            {
                "success": bool,
                "text": str,
                "error": str (if success=False),
                "model": str,
                "tokens_used": int
            }
        """
        pass


class LlamaCppInterface(LLMInterface):
    """
    Interface for llama.cpp models
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize llama.cpp interface
        
        Args:
            config: LLM configuration
        """
        self.config = config
        self.model_path = config.get("model_path")
        self.n_ctx = config.get("n_ctx", 4096)
        self.n_gpu_layers = config.get("n_gpu_layers", -1)
        self.n_batch = config.get("n_batch", 512)
        self.n_threads = config.get("n_threads", 4)
        self.use_mlock = config.get("use_mlock", True)
        self.use_mmap = config.get("use_mmap", True)
        self.seed = config.get("seed", -1)
        self.logger = logging.getLogger(__name__)
        self.llm = None
    
    async def initialize(self) -> None:
        """Initialize the LLM interface"""
        self.logger.info(f"Initializing llama.cpp interface with model {self.model_path}")
        
        from llama_cpp import Llama

        # Check if model exists
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model not found: {self.model_path}")

        # Initialize model
        self.llm = Llama(
            model_path=self.model_path,
            n_ctx=self.n_ctx,
            n_gpu_layers=self.n_gpu_layers,
            n_batch=self.n_batch,
            n_threads=self.n_threads,
            use_mlock=self.use_mlock,
            use_mmap=self.use_mmap,
            seed=self.seed
        )

        self.logger.info("llama.cpp interface initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the LLM interface"""
        self.logger.info("Shutting down llama.cpp interface")
        
        # No explicit cleanup needed for llama.cpp
        self.llm = None
    
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 4096,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        Generate text from prompt

        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation

        Returns:
            Dictionary with generation result
        """
        if not self.llm:
            return {
                "success": False,
                "error": "LLM not initialized",
                "text": "",
                "model": "llama.cpp",
                "tokens_used": 0
            }

        self.logger.info(f"Generating text with llama.cpp (max_tokens={max_tokens}, temp={temperature})")

        # Run in a thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: self.llm.create_completion(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                stop=["</s>", "</parameter>"]
            )
        )

        # Extract generated text
        generated_text = response.get("choices", [{}])[0].get("text", "")
        usage = response.get("usage", {})

        return {
            "success": True,
            "text": generated_text,
            "model": "llama.cpp",
            "tokens_used": usage.get("completion_tokens", 0)
        }


class VLLMInterface(LLMInterface):
    """
    Interface for vLLM models
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize vLLM interface
        
        Args:
            config: LLM configuration
        """
        self.config = config
        self.model_name = config.get("model_name")
        self.tensor_parallel_size = config.get("tensor_parallel_size", 1)
        self.gpu_memory_utilization = config.get("gpu_memory_utilization", 0.9)
        self.max_model_len = config.get("max_model_len", 4096)
        self.logger = logging.getLogger(__name__)
        self.llm = None
    
    async def initialize(self) -> None:
        """Initialize the LLM interface"""
        self.logger.info(f"Initializing vLLM interface with model {self.model_name}")
        
        from vllm import LLM

        # Initialize model
        self.llm = LLM(
            model=self.model_name,
            tensor_parallel_size=self.tensor_parallel_size,
            gpu_memory_utilization=self.gpu_memory_utilization,
            max_model_len=self.max_model_len
        )

        self.logger.info("vLLM interface initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the LLM interface"""
        self.logger.info("Shutting down vLLM interface")
        
        # No explicit cleanup needed for vLLM
        self.llm = None
    
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 4096,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        Generate text from prompt

        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation

        Returns:
            Dictionary with generation result
        """
        if not self.llm:
            return {
                "success": False,
                "error": "LLM not initialized",
                "text": "",
                "model": "vllm",
                "tokens_used": 0
            }

        self.logger.info(f"Generating text with vLLM (max_tokens={max_tokens}, temp={temperature})")

        # Run in a thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()

        sampling_params = {
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stop": ["</s>", "</parameter>"]
        }

        response = await loop.run_in_executor(
            None,
            lambda: self.llm.generate(prompt, sampling_params)
        )

        # Extract generated text
        generated_text = response[0].outputs[0].text
        tokens_used = len(response[0].outputs[0].token_ids) if hasattr(response[0].outputs[0], 'token_ids') else 0

        return {
            "success": True,
            "text": generated_text,
            "model": "vllm",
            "tokens_used": tokens_used
        }


class HttpApiInterface(LLMInterface):
    """
    Interface for HTTP API-based LLM services
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize HTTP API interface
        
        Args:
            config: LLM configuration
        """
        self.config = config
        self.api_url = config.get("api_url")
        self.api_key = config.get("api_key")
        self.model = config.get("model", "codellama-34b-instruct")
        self.timeout = config.get("timeout", 120)
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}" if self.api_key else ""
        }
        self.logger = logging.getLogger(__name__)
        self.client = None
    
    async def initialize(self) -> None:
        """Initialize the LLM interface"""
        self.logger.info(f"Initializing HTTP API interface with URL {self.api_url}")
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            headers=self.headers
        )

        self.logger.info("HTTP API interface initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the LLM interface"""
        self.logger.info("Shutting down HTTP API interface")
        
        if self.client:
            await self.client.aclose()
            self.client = None
    
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 4096,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        Generate text from prompt

        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate
            temperature: Temperature for generation

        Returns:
            Dictionary with generation result
        """
        if not self.client:
            return {
                "success": False,
                "error": "HTTP client not initialized",
                "text": "",
                "model": self.model,
                "tokens_used": 0
            }

        self.logger.info(f"Generating text with HTTP API (max_tokens={max_tokens}, temp={temperature})")

        # Prepare request payload for Ollama API
        payload = {
            "model": self.model,
            "prompt": prompt,
            "options": {
                "num_predict": max_tokens,
                "temperature": temperature,
                "stop": ["</s>", "</parameter>"]
            },
            "stream": False
        }

        # Send request to Ollama API
        response = await self.client.post(
            self.api_url,
            json=payload
        )

        # Check response
        response.raise_for_status()

        # Parse response
        response_data = response.json()

        # Extract generated text (Ollama format)
        if "response" in response_data:
            generated_text = response_data["response"]
            tokens_used = response_data.get("eval_count", 0)
        elif "choices" in response_data and len(response_data["choices"]) > 0:
            # OpenAI-compatible format
            generated_text = response_data["choices"][0].get("text", "")
            usage = response_data.get("usage", {})
            tokens_used = usage.get("completion_tokens", 0)
        else:
            generated_text = response_data.get("generated_text", "")
            tokens_used = 0

        return {
            "success": True,
            "text": generated_text,
            "model": self.model,
            "tokens_used": tokens_used
        }


def create_llm_interface(config: Dict[str, Any]) -> LLMInterface:
    """
    Create LLM interface based on configuration
    
    Args:
        config: LLM configuration
        
    Returns:
        LLM interface
    """
    llm_type = config.get("type", "llama.cpp")
    
    if llm_type == "llama.cpp":
        return LlamaCppInterface(config)
    elif llm_type == "vllm":
        return VLLMInterface(config)
    elif llm_type == "http_api":
        return HttpApiInterface(config)
    else:
        raise ValueError(f"Unsupported LLM type: {llm_type}")