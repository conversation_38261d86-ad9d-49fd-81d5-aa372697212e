"""
Cloud LLM Interface for Major Providers

Supports OpenAI, Anthropic, Azure OpenAI, Google PaLM, and other cloud LLM providers
with unified interface and automatic conversation management.
"""

import asyncio
import logging
import os
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

import httpx


class CloudLLMProvider(ABC):
    """Abstract base class for cloud LLM providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.api_key = config.get("api_key") or os.getenv(self._get_api_key_env_var())
        self.model = config.get("model")
        self.temperature = config.get("temperature", 0.2)
        self.max_tokens = config.get("max_tokens", 4096)
        self.timeout = config.get("timeout", 300)
        
        if not self.api_key:
            raise ValueError(f"API key not found. Set {self._get_api_key_env_var()} environment variable.")
    
    @abstractmethod
    def _get_api_key_env_var(self) -> str:
        """Get the environment variable name for API key"""
        pass
    
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate text using the provider's API"""
        pass
    
    @abstractmethod
    def _format_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Format request for the provider's API"""
        pass
    
    @abstractmethod
    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse response from the provider's API"""
        pass


class OpenAIProvider(CloudLLMProvider):
    """OpenAI GPT provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_base = config.get("api_base", "https://api.openai.com/v1")
        self.organization = config.get("organization")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        if self.organization:
            self.headers["OpenAI-Organization"] = self.organization
    
    def _get_api_key_env_var(self) -> str:
        return "OPENAI_API_KEY"
    
    async def generate_text(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate text using OpenAI API"""
        request_data = self._format_request(prompt, **kwargs)
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.api_base}/chat/completions",
                headers=self.headers,
                json=request_data
            )
            response.raise_for_status()
            
        return self._parse_response(response.json())
    
    def _format_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Format request for OpenAI API"""
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            "stream": False
        }
    
    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse OpenAI API response"""
        choice = response["choices"][0]
        usage = response.get("usage", {})
        
        return {
            "success": True,
            "text": choice["message"]["content"],
            "model": self.model,
            "tokens_used": usage.get("total_tokens", 0),
            "finish_reason": choice.get("finish_reason"),
            "usage": usage
        }


class AnthropicProvider(CloudLLMProvider):
    """Anthropic Claude provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_base = config.get("api_base", "https://api.anthropic.com")
        self.api_version = config.get("api_version", "2023-06-01")
        
        self.headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": self.api_version
        }
    
    def _get_api_key_env_var(self) -> str:
        return "ANTHROPIC_API_KEY"
    
    async def generate_text(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate text using Anthropic API"""
        request_data = self._format_request(prompt, **kwargs)
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.api_base}/v1/messages",
                headers=self.headers,
                json=request_data
            )
            response.raise_for_status()
            
        return self._parse_response(response.json())
    
    def _format_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Format request for Anthropic API"""
        return {
            "model": self.model,
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            "temperature": kwargs.get("temperature", self.temperature),
            "messages": [{"role": "user", "content": prompt}]
        }
    
    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Anthropic API response"""
        content = response["content"][0]
        usage = response.get("usage", {})
        
        return {
            "success": True,
            "text": content["text"],
            "model": self.model,
            "tokens_used": usage.get("input_tokens", 0) + usage.get("output_tokens", 0),
            "finish_reason": response.get("stop_reason"),
            "usage": usage
        }


class AzureOpenAIProvider(CloudLLMProvider):
    """Azure OpenAI provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_base = config.get("api_base") or os.getenv("AZURE_OPENAI_ENDPOINT")
        self.api_version = config.get("api_version", "2023-12-01-preview")
        self.deployment_name = config.get("deployment_name") or os.getenv("AZURE_DEPLOYMENT_NAME")
        
        if not self.api_base or not self.deployment_name:
            raise ValueError("Azure OpenAI requires api_base and deployment_name")
        
        self.headers = {
            "api-key": self.api_key,
            "Content-Type": "application/json"
        }
    
    def _get_api_key_env_var(self) -> str:
        return "AZURE_OPENAI_API_KEY"
    
    async def generate_text(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate text using Azure OpenAI API"""
        request_data = self._format_request(prompt, **kwargs)
        
        url = f"{self.api_base}/openai/deployments/{self.deployment_name}/chat/completions?api-version={self.api_version}"
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(url, headers=self.headers, json=request_data)
            response.raise_for_status()
            
        return self._parse_response(response.json())
    
    def _format_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Format request for Azure OpenAI API"""
        return {
            "messages": [{"role": "user", "content": prompt}],
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            "stream": False
        }
    
    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Azure OpenAI API response"""
        choice = response["choices"][0]
        usage = response.get("usage", {})
        
        return {
            "success": True,
            "text": choice["message"]["content"],
            "model": self.deployment_name,
            "tokens_used": usage.get("total_tokens", 0),
            "finish_reason": choice.get("finish_reason"),
            "usage": usage
        }


class GooglePaLMProvider(CloudLLMProvider):
    """Google PaLM provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_base = config.get("api_base", "https://generativelanguage.googleapis.com")
    
    def _get_api_key_env_var(self) -> str:
        return "GOOGLE_PALM_API_KEY"
    
    async def generate_text(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate text using Google PaLM API"""
        request_data = self._format_request(prompt, **kwargs)
        
        url = f"{self.api_base}/v1beta/models/{self.model}:generateText?key={self.api_key}"
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(url, json=request_data)
            response.raise_for_status()
            
        return self._parse_response(response.json())
    
    def _format_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Format request for Google PaLM API"""
        return {
            "prompt": {"text": prompt},
            "temperature": kwargs.get("temperature", self.temperature),
            "candidateCount": 1,
            "maxOutputTokens": kwargs.get("max_tokens", self.max_tokens)
        }
    
    def _parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Google PaLM API response"""
        candidates = response.get("candidates", [])
        if not candidates:
            return {
                "success": False,
                "error": "No candidates returned",
                "text": ""
            }
        
        candidate = candidates[0]
        return {
            "success": True,
            "text": candidate.get("output", ""),
            "model": self.model,
            "tokens_used": 0,  # PaLM doesn't return token usage
            "finish_reason": candidate.get("finishReason"),
            "usage": {}
        }


class CloudLLMInterface:
    """Unified interface for cloud LLM providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.provider_type = config.get("type", "openai")
        
        # Initialize the appropriate provider
        self.provider = self._create_provider()
        
        self.logger.info(f"Initialized {self.provider_type} cloud LLM interface")
    
    def _create_provider(self) -> CloudLLMProvider:
        """Create the appropriate provider based on configuration"""
        providers = {
            "openai": OpenAIProvider,
            "anthropic": AnthropicProvider,
            "azure_openai": AzureOpenAIProvider,
            "google_palm": GooglePaLMProvider
        }
        
        provider_class = providers.get(self.provider_type)
        if not provider_class:
            raise ValueError(f"Unsupported provider type: {self.provider_type}")
        
        return provider_class(self.config)
    
    async def generate_text(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate text using the configured cloud provider"""
        return await self.provider.generate_text(prompt, **kwargs)
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current provider"""
        return {
            "type": self.provider_type,
            "model": self.provider.model,
            "api_key_configured": bool(self.provider.api_key),
            "max_tokens": self.provider.max_tokens,
            "temperature": self.provider.temperature
        }
