"""
Validation Utilities for Code Generator
Provides utility functions for validating code and inputs
"""
import re
from typing import Dict, List, Any, Optional, Set, Tuple

from code_generator.models.generation_request import GenerationRequest, ProgrammingLanguage


class CodeValidator:
    """Code validation utility class"""
    
    def __init__(self):
        """Initialize code validator"""
        pass
    
    def validate_syntax(self, code: str, language: str) -> bool:
        """Validate code syntax - returns boolean for test compatibility"""
        try:
            lang_enum = ProgrammingLanguage(language.lower())
            is_valid, _ = validate_code_syntax(code, lang_enum)
            return is_valid
        except ValueError:
            return False
    
    def validate_request(self, request: GenerationRequest) -> bool:
        """Validate generation request - returns boolean for test compatibility"""
        is_valid, _ = validate_generation_request(request)
        return is_valid


def validate_generation_request(request: GenerationRequest) -> Tuple[bool, List[str]]:
    """
    Validate generation request
    
    Args:
        request: Generation request
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Validate task ID
    if not request.task_id:
        errors.append("Task ID is required")
    
    # Validate description
    if not request.description:
        errors.append("Description is required")
    elif len(request.description) < 10:
        errors.append("Description is too short (minimum 10 characters)")
    
    # Validate max_tokens
    if request.max_tokens <= 0:
        errors.append("Max tokens must be positive")
    elif request.max_tokens > 8192:
        errors.append("Max tokens exceeds limit (maximum 8192)")
    
    # Validate temperature
    if request.temperature < 0.0 or request.temperature > 2.0:
        errors.append("Temperature must be between 0.0 and 2.0")
    
    # Validate iteration
    if request.iteration <= 0:
        errors.append("Iteration must be positive")
    
    return len(errors) == 0, errors


def validate_code_syntax(code: str, language: ProgrammingLanguage) -> Tuple[bool, List[str]]:
    """
    Validate code syntax
    
    Args:
        code: Code content
        language: Programming language
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    if language == ProgrammingLanguage.PYTHON:
        valid, syntax_errors = _validate_python_syntax(code)
        if not valid:
            errors.extend(syntax_errors)
    
    elif language in [ProgrammingLanguage.JAVASCRIPT, ProgrammingLanguage.TYPESCRIPT]:
        valid, syntax_errors = _validate_js_syntax(code)
        if not valid:
            errors.extend(syntax_errors)
    
    # For other languages, we don't have built-in syntax validation
    
    return len(errors) == 0, errors


def _validate_python_syntax(code: str) -> Tuple[bool, List[str]]:
    """
    Validate Python syntax
    
    Args:
        code: Python code content
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    try:
        # Try to compile the code
        compile(code, "<string>", "exec")
    except SyntaxError as e:
        # Extract error information
        line_no = e.lineno if hasattr(e, "lineno") else "unknown"
        error_msg = str(e)
        errors.append(f"Syntax error at line {line_no}: {error_msg}")
    
    return len(errors) == 0, errors


def _validate_js_syntax(code: str) -> Tuple[bool, List[str]]:
    """
    Validate JavaScript/TypeScript syntax
    
    Args:
        code: JavaScript/TypeScript code content
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Basic syntax validation for JS/TS
    # Check for unbalanced brackets, parentheses, and braces
    brackets = {"(": ")", "[": "]", "{": "}"}
    stack = []
    
    for i, char in enumerate(code):
        if char in brackets:
            stack.append((char, i))
        elif char in brackets.values():
            if not stack:
                line_no = code[:i].count("\n") + 1
                errors.append(f"Unmatched closing bracket at line {line_no}")
                break
            
            last_open, _ = stack.pop()
            if brackets[last_open] != char:
                line_no = code[:i].count("\n") + 1
                errors.append(f"Mismatched brackets at line {line_no}")
                break
    
    # Check for remaining unclosed brackets
    if stack:
        for bracket, pos in stack:
            line_no = code[:pos].count("\n") + 1
            errors.append(f"Unclosed {bracket} at line {line_no}")
    
    return len(errors) == 0, errors


def validate_filename(filename: str) -> Tuple[bool, Optional[str]]:
    """
    Validate filename
    
    Args:
        filename: Filename to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check for empty filename
    if not filename:
        return False, "Filename cannot be empty"
    
    # Check for invalid characters
    invalid_chars = r'[<>:"/\\|?*]'
    if re.search(invalid_chars, filename):
        return False, f"Filename contains invalid characters: {re.findall(invalid_chars, filename)}"
    
    # Check for reserved filenames (Windows)
    reserved_names = [
        "CON", "PRN", "AUX", "NUL",
        "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
        "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
    ]
    
    base_name = filename.split(".")[0].upper()
    if base_name in reserved_names:
        return False, f"Filename uses reserved name: {base_name}"
    
    return True, None


def validate_project_structure(structure: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate project structure
    
    Args:
        structure: Project structure dictionary
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Validate structure is not empty
    if not structure:
        errors.append("Project structure cannot be empty")
    
    # Validate structure is not too deep
    max_depth = 10
    
    def check_depth(struct, current_depth=0):
        if current_depth > max_depth:
            return False
        
        if isinstance(struct, dict):
            return all(check_depth(value, current_depth + 1) for value in struct.values())
        
        return True
    
    if not check_depth(structure):
        errors.append(f"Project structure exceeds maximum depth of {max_depth}")
    
    return len(errors) == 0, errors
