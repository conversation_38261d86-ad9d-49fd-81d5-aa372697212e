"""
Code Utilities for Code Generator
Provides utility functions for code generation and manipulation
"""
import os
import re
from typing import Dict, List, Any, Op<PERSON>, <PERSON><PERSON>, Set


def extract_code_blocks(text: str) -> List[Tuple[str, str, str]]:
    """
    Extract code blocks from text
    
    Args:
        text: Text containing code blocks
        
    Returns:
        List of tuples (language, filename, code)
    """
    # Pattern to match code blocks with optional language and filename
    # Format: ```language filename
    # or # filename: filename.ext
    patterns = [
        r"```(\w+)\s+([^\s]+)\n(.*?)```",  # ```language filename.ext
        r"#\s*filename:\s*([^\s]+)\n```(\w+)?\n(.*?)```",  # # filename: filename.ext
        r"<!--\s*filename:\s*([^\s]+)\s*-->\n```(\w+)?\n(.*?)```",  # <!-- filename: filename.ext -->
        r"```(\w+)\n#\s*filename:\s*([^\s]+)\n(.*?)```",  # ```language \n # filename: filename.ext
    ]
    
    code_blocks = []
    
    for pattern in patterns:
        for match in re.finditer(pattern, text, re.DOTALL):
            if len(match.groups()) == 3:
                if match.group(1).lower() in ["filename:", "file:"]:
                    # Handle case where filename is in group 1
                    filename = match.group(2)
                    language = match.group(3) if match.group(3) else "text"
                    code = match.group(3)
                else:
                    # Handle normal case
                    language = match.group(1) if match.group(1) else "text"
                    filename = match.group(2)
                    code = match.group(3)
                
                code_blocks.append((language, filename, code))
    
    # If no code blocks with filenames were found, try to extract regular code blocks
    if not code_blocks:
        # Pattern to match regular code blocks
        pattern = r"```(\w+)?\n(.*?)```"
        
        for i, match in enumerate(re.finditer(pattern, text, re.DOTALL)):
            language = match.group(1) if match.group(1) else "text"
            code = match.group(2)
            filename = f"generated_file_{i+1}.{language}"
            
            code_blocks.append((language, filename, code))
    
    return code_blocks


def get_file_extension(language: str) -> str:
    """
    Get file extension for language
    
    Args:
        language: Programming language
        
    Returns:
        File extension
    """
    extensions = {
        "python": "py",
        "py": "py",
        "javascript": "js",
        "js": "js",
        "typescript": "ts",
        "ts": "ts",
        "java": "java",
        "csharp": "cs",
        "cs": "cs",
        "cpp": "cpp",
        "c++": "cpp",
        "c": "c",
        "go": "go",
        "rust": "rs",
        "php": "php",
        "ruby": "rb",
        "swift": "swift",
        "kotlin": "kt",
        "shell": "sh",
        "bash": "sh",
        "sh": "sh",
        "sql": "sql",
        "html": "html",
        "css": "css",
        "json": "json",
        "yaml": "yaml",
        "yml": "yml",
        "markdown": "md",
        "md": "md",
    }
    
    return extensions.get(language.lower(), "txt")


def ensure_directory_exists(file_path: str) -> None:
    """
    Ensure directory exists for file path
    
    Args:
        file_path: File path
    """
    directory = os.path.dirname(file_path)
    
    if directory and not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)


def detect_language_from_file(file_path: str) -> Optional[str]:
    """
    Detect language from file path
    
    Args:
        file_path: File path
        
    Returns:
        Language name if detected, None otherwise
    """
    extension = file_path.split(".")[-1] if "." in file_path else ""
    
    extension_to_language = {
        "py": "python",
        "js": "javascript",
        "ts": "typescript",
        "jsx": "javascript",
        "tsx": "typescript",
        "java": "java",
        "cs": "csharp",
        "cpp": "cpp",
        "c": "c",
        "go": "go",
        "rs": "rust",
        "php": "php",
        "rb": "ruby",
        "swift": "swift",
        "kt": "kotlin",
        "sh": "shell",
        "sql": "sql",
        "html": "html",
        "css": "css",
        "json": "json",
        "yaml": "yaml",
        "yml": "yaml",
        "md": "markdown",
    }
    
    return extension_to_language.get(extension.lower())


def extract_imports(content: str, language: str) -> Set[str]:
    """
    Extract imports from code content
    
    Args:
        content: Code content
        language: Programming language
        
    Returns:
        Set of imported modules
    """
    imports = set()
    
    if language in ["python", "py"]:
        # Python import patterns
        import_patterns = [
            r"^import\s+([\w\.]+)(?:\s+as\s+\w+)?",
            r"^from\s+([\w\.]+)\s+import\s+.+"
        ]
        
        for line in content.split("\n"):
            line = line.strip()
            
            for pattern in import_patterns:
                match = re.match(pattern, line)
                if match:
                    module = match.group(1)
                    # Add only top-level module
                    top_module = module.split(".")[0]
                    imports.add(top_module)
    
    elif language in ["javascript", "js", "typescript", "ts"]:
        # JavaScript/TypeScript import patterns
        import_patterns = [
            r"import\s+.*\s+from\s+['\"]([^'\"]+)['\"]",
            r"require\s*\(\s*['\"]([^'\"]+)['\"]"
        ]
        
        for line in content.split("\n"):
            line = line.strip()
            
            for pattern in import_patterns:
                match = re.search(pattern, line)
                if match:
                    module = match.group(1)
                    # Skip relative imports
                    if not module.startswith("."):
                        # Extract package name (before any /)
                        package = module.split("/")[0]
                        imports.add(package)
    
    return imports


def extract_functions_and_classes(content: str, language: str) -> Dict[str, Dict[str, Any]]:
    """
    Extract functions and classes from code content
    
    Args:
        content: Code content
        language: Programming language
        
    Returns:
        Dictionary of functions and classes with their details
    """
    result = {
        "functions": {},
        "classes": {}
    }
    
    if language in ["python", "py"]:
        # Extract Python functions
        function_pattern = r"def\s+(\w+)\s*\((.*?)\)(?:\s*->\s*([^:]+))?\s*:"
        
        for match in re.finditer(function_pattern, content, re.DOTALL):
            name = match.group(1)
            params = match.group(2)
            return_type = match.group(3) if match.group(3) else "None"
            
            result["functions"][name] = {
                "params": params.strip(),
                "return_type": return_type.strip()
            }
        
        # Extract Python classes
        class_pattern = r"class\s+(\w+)(?:\(([^)]+)\))?\s*:"
        
        for match in re.finditer(class_pattern, content, re.DOTALL):
            name = match.group(1)
            inheritance = match.group(2) if match.group(2) else ""
            
            result["classes"][name] = {
                "inheritance": inheritance.strip()
            }
    
    elif language in ["javascript", "js", "typescript", "ts"]:
        # Extract JavaScript/TypeScript functions
        function_patterns = [
            r"function\s+(\w+)\s*\((.*?)\)",
            r"(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s*)?\((.*?)\)\s*=>"
        ]
        
        for pattern in function_patterns:
            for match in re.finditer(pattern, content, re.DOTALL):
                name = match.group(1)
                params = match.group(2)
                
                result["functions"][name] = {
                    "params": params.strip()
                }
        
        # Extract JavaScript/TypeScript classes
        class_pattern = r"class\s+(\w+)(?:\s+extends\s+([^\s{]+))?\s*{"
        
        for match in re.finditer(class_pattern, content, re.DOTALL):
            name = match.group(1)
            inheritance = match.group(2) if match.group(2) else ""
            
            result["classes"][name] = {
                "inheritance": inheritance.strip()
            }
    
    return result
