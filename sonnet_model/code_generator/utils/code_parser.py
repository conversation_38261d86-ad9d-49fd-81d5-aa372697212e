"""
Code Parser Utility
Parses and extracts code from LLM responses
"""

import re
import ast
from typing import Dict, List, Any, Optional, Tuple


class CodeParser:
    """
    Code Parser
    
    Extracts and processes code from LLM responses
    """
    
    def __init__(self):
        """Initialize Code Parser"""
        # Regex patterns for code extraction
        self.code_block_pattern = re.compile(r'```(?:\w+)?\s*\n(.*?)\n```', re.DOTALL)
        self.inline_code_pattern = re.compile(r'`([^`]+)`')
        self.language_pattern = re.compile(r'```(\w+)')
        
        # Patterns for metadata extraction
        self.function_pattern = re.compile(r'(?:def|function)\s+(\w+)')
        self.class_pattern = re.compile(r'class\s+(\w+)')
        self.import_pattern = re.compile(r'(?:import|from|require)\s+(\S+)')
    
    def extract_code(self, text: str) -> str:
        """
        Extract code from text
        
        Args:
            text: Text containing code
            
        Returns:
            Extracted code
        """
        # Try to extract code blocks first
        code_blocks = self.code_block_pattern.findall(text)
        if code_blocks:
            return '\n\n'.join(code_blocks)
        
        # If no code blocks, try inline code
        inline_code = self.inline_code_pattern.findall(text)
        if inline_code:
            return '\n'.join(inline_code)
        
        # If no code markers, return the original text
        # This is a fallback for when the LLM doesn't use code markers
        return text
    
    def detect_language(self, text: str) -> Optional[str]:
        """
        Detect programming language from code block
        
        Args:
            text: Text containing code block
            
        Returns:
            Detected language or None
        """
        language_matches = self.language_pattern.findall(text)
        if language_matches:
            return language_matches[0].lower()
        
        # Try to infer language from code content
        if 'def ' in text and ':' in text and ('self' in text or 'import ' in text):
            return 'python'
        elif 'function ' in text and '{' in text and '}' in text:
            if 'import React' in text or 'useState' in text:
                return 'jsx'
            elif 'console.log' in text:
                return 'javascript'
        elif '<' in text and '>' in text and ('</' in text or '/>' in text):
            return 'html'
        elif '@' in text and '{' in text and '}' in text and 'class ' in text:
            return 'java'
        
        return None
    
    def extract_metadata(self, code: str) -> Dict[str, Any]:
        """
        Extract metadata from code
        
        Args:
            code: Code to extract metadata from
            
        Returns:
            Extracted metadata
        """
        metadata = {
            'functions': [],
            'classes': [],
            'imports': [],
            'line_count': 0,
            'char_count': 0
        }
        
        if not code:
            return metadata
        
        # Extract basic metrics
        lines = code.split('\n')
        metadata['line_count'] = len(lines)
        metadata['char_count'] = len(code)
        
        # Extract functions, classes, and imports
        metadata['functions'] = self.function_pattern.findall(code)
        metadata['classes'] = self.class_pattern.findall(code)
        metadata['imports'] = self.import_pattern.findall(code)
        
        return metadata
    
    def format_code(self, code: str, language: Optional[str] = None) -> str:
        """
        Format code based on language
        
        Args:
            code: Code to format
            language: Programming language
            
        Returns:
            Formatted code
        """
        if not code:
            return ""
        
        # Remove leading/trailing whitespace
        formatted_code = code.strip()
        
        # Basic formatting based on language
        if language == 'python':
            # Ensure consistent indentation (4 spaces)
            lines = formatted_code.split('\n')
            formatted_lines = []
            for line in lines:
                # Replace tabs with spaces
                line = line.replace('\t', '    ')
                formatted_lines.append(line)
            formatted_code = '\n'.join(formatted_lines)
        
        return formatted_code
    
    def extract_code_and_explanation(self, text: str) -> Tuple[str, str]:
        """
        Extract code and explanation from text
        
        Args:
            text: Text containing code and explanation
            
        Returns:
            Tuple of (code, explanation)
        """
        # Extract all code blocks
        code_blocks = self.code_block_pattern.findall(text)
        
        if not code_blocks:
            # No code blocks found
            return "", text
        
        # Join all code blocks
        code = '\n\n'.join(code_blocks)
        
        # Remove code blocks from text to get explanation
        explanation = self.code_block_pattern.sub('', text).strip()
        
        return code, explanation
    
    def is_valid_code(self, code: str, language: str) -> bool:
        """
        Check if code is valid (basic checks only)
        
        Args:
            code: Code to check
            language: Programming language
            
        Returns:
            Whether code is valid
        """
        if not code.strip():
            return False
        
        # Basic validity checks based on language
        if language == 'python':
            # Check for basic syntax issues
            if code.count('(') != code.count(')'):
                return False
            if code.count('{') != code.count('}'):
                return False
            if code.count('[') != code.count(']'):
                return False
            
            # Check for common Python syntax
            if 'def ' in code and ':' not in code:
                return False
        
        elif language in ['javascript', 'typescript', 'jsx']:
            # Check for basic syntax issues
            if code.count('(') != code.count(')'):
                return False
            if code.count('{') != code.count('}'):
                return False
            if code.count('[') != code.count(']'):
                return False
            
            # Check for missing semicolons (not always required but common)
            lines = code.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.endswith('{') and not line.endswith('}') and \
                   not line.endswith(';') and not line.endswith(',') and \
                   not line.startswith('//') and not line.startswith('/*') and \
                   not line.endswith('*/') and not line.endswith(':'):
                    # This is a heuristic and might have false positives
                    pass
        
        return True

    def parse_code(self, code: str, language: str) -> Dict[str, Any]:
        """
        Parse code and extract structural information
        
        Args:
            code: Code content to parse
            language: Programming language
            
        Returns:
            Dictionary containing parsed code information
        """
        result = {
            "functions": [],
            "classes": [],
            "imports": [],
            "variables": [],
            "language": language,
            "valid": self.validate_code(code, language)
        }
        
        if language.lower() == "python":
            # Extract functions
            function_matches = self.function_pattern.findall(code)
            result["functions"] = function_matches
            
            # Extract classes
            class_matches = self.class_pattern.findall(code)
            result["classes"] = class_matches
            
            # Extract imports
            import_matches = self.import_pattern.findall(code)
            result["imports"] = import_matches
            
            # Extract variables (simple heuristic)
            lines = code.split('\n')
            for line in lines:
                line = line.strip()
                if '=' in line and not line.startswith('#') and not line.startswith('def') and not line.startswith('class'):
                    var_name = line.split('=')[0].strip()
                    if var_name and var_name.isidentifier():
                        result["variables"].append(var_name)
        
        return result
        
    def validate_code(self, code: str, language: str) -> bool:
        """
        Validate code syntax for the given language
        
        Args:
            code: Code to validate
            language: Programming language
            
        Returns:
            bool: True if code is valid, False otherwise
        """
        if not code.strip():
            return False
            
        if language.lower() == "python":
            try:
                ast.parse(code)
                return True
            except (SyntaxError, ValueError):
                return False
                
        # For other languages, we'll do basic validation
        # This could be enhanced with language-specific validators
        return bool(code.strip())
