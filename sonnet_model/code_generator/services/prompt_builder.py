"""
Prompt Builder for Code Generator
Builds prompts for LLM code generation
"""
import os
from typing import Dict, List, Any, Optional

from code_generator.models.generation_request import GenerationRequest, ProgrammingLanguage, Framework
from code_generator.models.code_context import CodeContext


class PromptBuilder:
    """
    Prompt Builder
    
    Builds prompts for LLM code generation
    """
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        Initialize Prompt Builder
        
        Args:
            template_dir: Template directory path
        """
        self.template_dir = template_dir or os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "templates"
        )
    
    def build_generation_prompt(self, request: GenerationRequest) -> str:
        """
        Build code generation prompt
        
        Args:
            request: Generation request
            
        Returns:
            Generated prompt
        """
        # Start with system instructions
        prompt = self._get_system_prompt(request.language, request.framework)
        
        # Add task description
        prompt += self._format_task_description(request)
        
        # Add requirements
        if request.requirements:
            prompt += "\n\n## Requirements\n"
            for i, req in enumerate(request.requirements, 1):
                prompt += f"{i}. {req}\n"
        
        # Add context
        if request.context:
            prompt += f"\n\n## Context\n{request.context}\n"
        
        # Add examples
        if request.examples:
            prompt += "\n\n## Examples\n"
            for i, example in enumerate(request.examples, 1):
                prompt += f"### Example {i}\n```\n{example}\n```\n\n"
        
        # Add constraints
        if request.constraints:
            prompt += "\n\n## Constraints\n"
            for key, value in request.constraints.items():
                prompt += f"- {key}: {value}\n"
        
        # Add iteration info if not first iteration
        if request.iteration > 1:
            prompt += f"\n\n## Iteration\nThis is iteration {request.iteration}. "
            prompt += "Please refine your previous solution based on feedback.\n"
        
        # Add final instructions
        prompt += self._get_final_instructions(request.language)
        
        return prompt
    
    def build_context_prompt(self, context: CodeContext) -> str:
        """
        Build code context prompt
        
        Args:
            context: Code context
            
        Returns:
            Generated prompt
        """
        prompt = "# Code Context\n\n"
        
        # Add language and framework
        prompt += f"## Language: {context.language.value}\n"
        if context.framework:
            prompt += f"## Framework: {context.framework.value}\n"
        
        # Add dependencies
        if context.dependencies:
            prompt += "\n## Dependencies\n"
            for dep in context.dependencies:
                dev_str = " (dev)" if dep.is_dev else ""
                prompt += f"- {dep}{dev_str}\n"
        
        # Add project structure
        if context.project_structure:
            prompt += "\n## Project Structure\n"
            prompt += self._format_project_structure(context.project_structure)
        
        # Add files
        if context.files:
            prompt += "\n## Files\n"
            for file in context.files:
                if file.is_directory:
                    prompt += f"- Directory: {file.path}\n"
                else:
                    prompt += f"- File: {file.path}\n"
                    if file.content:
                        prompt += f"```\n{file.content}\n```\n\n"
        
        return prompt
    
    def _get_system_prompt(
        self, 
        language: ProgrammingLanguage, 
        framework: Optional[Framework] = None
    ) -> str:
        """
        Get system prompt
        
        Args:
            language: Programming language
            framework: Framework
            
        Returns:
            System prompt
        """
        # Base system prompt
        prompt = "# Code Generation Task\n\n"
        prompt += "You are an expert software developer specializing in "
        prompt += f"{language.value.capitalize()}"
        
        if framework:
            prompt += f" with {framework.value.capitalize()} framework"
        
        prompt += ". Your task is to generate high-quality, production-ready code "
        prompt += "based on the requirements provided below.\n\n"
        
        # Add language-specific instructions
        if language == ProgrammingLanguage.PYTHON:
            prompt += "Follow PEP 8 style guidelines and include proper docstrings. "
            prompt += "Use type hints where appropriate. "
            prompt += "Structure your code in a modular and maintainable way.\n\n"
        elif language in [ProgrammingLanguage.JAVASCRIPT, ProgrammingLanguage.TYPESCRIPT]:
            prompt += "Follow modern ES6+ practices. "
            prompt += "Use proper error handling and async/await where appropriate. "
            prompt += "Structure your code in a modular and maintainable way.\n\n"
        
        return prompt
    
    def _format_task_description(self, request: GenerationRequest) -> str:
        """
        Format task description
        
        Args:
            request: Generation request
            
        Returns:
            Formatted task description
        """
        return f"## Task Description\n{request.description}\n"
    
    def _format_project_structure(self, structure: Dict[str, Any], indent: int = 0) -> str:
        """
        Format project structure
        
        Args:
            structure: Project structure dictionary
            indent: Indentation level
            
        Returns:
            Formatted project structure
        """
        result = ""
        
        for key, value in structure.items():
            result += " " * indent + f"- {key}\n"
            
            if isinstance(value, dict):
                result += self._format_project_structure(value, indent + 2)
        
        return result
    
    def _get_final_instructions(self, language: ProgrammingLanguage) -> str:
        """
        Get final instructions
        
        Args:
            language: Programming language
            
        Returns:
            Final instructions
        """
        prompt = "\n\n## Output Instructions\n"
        prompt += "Please provide your solution in the following format:\n\n"
        prompt += "1. First, give a brief explanation of your approach and solution.\n"
        prompt += "2. Then, provide the code files with clear filenames using markdown code blocks.\n"
        prompt += "   Format: ```language\n// filename: example.js\n// Your code here\n```\n"
        prompt += "3. Finally, add any suggestions or considerations for future improvements.\n\n"
        
        return prompt
    
    def _load_template(self, template_path: str) -> str:
        """
        Load template from file
        
        Args:
            template_path: Template file path
            
        Returns:
            Template content
        """
        try:
            with open(template_path, "r") as f:
                return f.read()
        except Exception as e:
            # Return empty string if template not found
            return ""
