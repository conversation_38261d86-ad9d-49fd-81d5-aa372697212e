"""
Context Manager for Code Generator
Manages code context for generation
"""
import os
import logging
from typing import Dict, List, Any, Optional, Set

from code_generator.models.code_context import CodeContext, FileReference, CodeDependency
from code_generator.models.generation_request import ProgrammingLanguage, Framework


class ContextManager:
    """
    Context Manager
    
    Manages code context for generation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Context Manager
        
        Args:
            config: Context manager configuration
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
    
    async def build_context(
        self,
        language: ProgrammingLanguage,
        framework: Optional[Framework] = None,
        project_path: Optional[str] = None,
        file_paths: Optional[List[str]] = None,
        dependencies: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> CodeContext:
        """
        Build code context
        
        Args:
            language: Programming language
            framework: Framework
            project_path: Project path
            file_paths: List of file paths to include
            dependencies: List of dependencies
            metadata: Additional metadata
            
        Returns:
            Code context
        """
        context = CodeContext(
            language=language,
            framework=framework,
            metadata=metadata or {}
        )
        
        # Add dependencies
        if dependencies:
            for dep in dependencies:
                context.add_dependency(
                    name=dep["name"],
                    version=dep.get("version"),
                    is_dev=dep.get("is_dev", False)
                )
        
        # Add files
        if project_path and file_paths:
            await self._add_files_to_context(context, project_path, file_paths)
        
        # Build project structure
        if project_path:
            context.project_structure = await self._build_project_structure(project_path)
        
        return context
    
    async def _add_files_to_context(
        self,
        context: CodeContext,
        project_path: str,
        file_paths: List[str]
    ) -> None:
        """
        Add files to context
        
        Args:
            context: Code context
            project_path: Project path
            file_paths: List of file paths to include
        """
        for path in file_paths:
            full_path = os.path.join(project_path, path)
            
            if os.path.isdir(full_path):
                context.add_file(path=path, is_directory=True)
            elif os.path.isfile(full_path):
                try:
                    with open(full_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    
                    context.add_file(path=path, content=content)
                    
                    # Extract imports for Python files
                    if path.endswith(".py"):
                        self._extract_python_imports(context, content)
                    
                    # Extract imports for JavaScript/TypeScript files
                    elif path.endswith((".js", ".ts")):
                        self._extract_js_imports(context, content)
                
                except Exception as e:
                    self.logger.error(f"Error reading file {path}: {e}", exc_info=True)
    
    async def _build_project_structure(self, project_path: str) -> Dict[str, Any]:
        """
        Build project structure
        
        Args:
            project_path: Project path
            
        Returns:
            Project structure dictionary
        """
        structure = {}
        
        try:
            for root, dirs, files in os.walk(project_path):
                # Skip hidden directories and files
                dirs[:] = [d for d in dirs if not d.startswith(".")]
                files = [f for f in files if not f.startswith(".")]
                
                # Skip virtual environments
                if "venv" in dirs:
                    dirs.remove("venv")
                if "node_modules" in dirs:
                    dirs.remove("node_modules")
                
                # Get relative path
                rel_path = os.path.relpath(root, project_path)
                if rel_path == ".":
                    # Add top-level files
                    for file in files:
                        structure[file] = {}
                    
                    # Add directories
                    for directory in dirs:
                        structure[directory] = {}
                else:
                    # Navigate to the correct position in the structure
                    parts = rel_path.split(os.sep)
                    current = structure
                    
                    for part in parts:
                        if part not in current:
                            current[part] = {}
                        
                        current = current[part]
                    
                    # Add files
                    for file in files:
                        current[file] = {}
        
        except Exception as e:
            self.logger.error(f"Error building project structure: {e}", exc_info=True)
        
        return structure
    
    def _extract_python_imports(self, context: CodeContext, content: str) -> None:
        """
        Extract Python imports
        
        Args:
            context: Code context
            content: File content
        """
        import re
        
        # Match import statements
        import_patterns = [
            r"^import\s+([\w\.]+)(?:\s+as\s+\w+)?",
            r"^from\s+([\w\.]+)\s+import\s+.+"
        ]
        
        for line in content.split("\n"):
            line = line.strip()
            
            for pattern in import_patterns:
                match = re.match(pattern, line)
                if match:
                    module = match.group(1)
                    # Add only top-level module
                    top_module = module.split(".")[0]
                    context.add_import(top_module)
    
    def _extract_js_imports(self, context: CodeContext, content: str) -> None:
        """
        Extract JavaScript/TypeScript imports
        
        Args:
            context: Code context
            content: File content
        """
        import re
        
        # Match import statements
        import_patterns = [
            r"import\s+.*\s+from\s+['\"]([^'\"]+)['\"]",
            r"require\s*\(\s*['\"]([^'\"]+)['\"]"
        ]
        
        for line in content.split("\n"):
            line = line.strip()
            
            for pattern in import_patterns:
                match = re.search(pattern, line)
                if match:
                    module = match.group(1)
                    # Skip relative imports
                    if not module.startswith("."):
                        # Extract package name (before any /)
                        package = module.split("/")[0]
                        context.add_import(package)
