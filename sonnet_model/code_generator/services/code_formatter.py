"""
Code Formatter for Code Generator
Formats generated code according to language-specific style guidelines
"""
import asyncio
import logging
from typing import Union

from code_generator.models.generation_request import ProgrammingLanguage
from code_generator.models.generation_result import CodeFile


class CodeFormatter:
    """Code formatter for various programming languages"""
    
    def __init__(self):
        """Initialize the code formatter"""
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.black_config = {
            "line_length": 88,
            "target_versions": {"py38", "py39", "py310"},
            "skip_string_normalization": False
        }
        
        self.prettier_config = {
            "printWidth": 80,
            "tabWidth": 2,
            "useTabs": False,
            "semi": True,
            "singleQuote": True,
            "quoteProps": "as-needed",
            "trailingComma": "es5"
        }
    
    async def format_code(self, code: Union[str, CodeFile], language: str = None) -> Union[str, CodeFile]:
        """
        Format code - supports both string input (for tests) and CodeFile input

        Args:
            code: Code content (string) or CodeFile object
            language: Programming language (only used when code is string)

        Returns:
            Formatted code (string) or CodeFile object
        """
        if isinstance(code, str) and language:
            # Test compatibility mode - return formatted string
            return await self._format_code_string(code, language)
        elif isinstance(code, CodeFile):
            # Production mode - return formatted CodeFile
            return await self._format_code_file(code)
        else:
            raise ValueError("Invalid arguments: provide either (code_string, language) or (code_file)")
    
    async def _format_code_string(self, code: str, language: str) -> str:
        """Format code string for test compatibility"""
        if language.lower() == "python":
            return await self._format_python(code)
        return code  # Return unchanged for unsupported languages
    
    def _format_python_simple(self, code: str) -> str:
        """Simple Python formatting without external dependencies"""
        # Basic formatting: normalize whitespace
        lines = code.split('\n')
        formatted_lines = []
        
        for line in lines:
            # Remove trailing whitespace
            line = line.rstrip()
            # Normalize function definitions
            if 'def ' in line and '(' in line and ')' in line:
                # Fix spacing around parentheses
                line = line.replace('( ', '(').replace(' )', ')')
                # Add space after colon
                if ':' in line and not line.endswith(': '):
                    line = line.replace(':', ': ')
            formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    async def _format_code_file(self, code_file: CodeFile) -> CodeFile:
        """
        Format code

        Args:
            code_file: Code file to format

        Returns:
            Formatted code file
        """
        language = code_file.language

        if language == ProgrammingLanguage.PYTHON:
            formatted_content = await self._format_python(code_file.content)
        elif language in [ProgrammingLanguage.JAVASCRIPT, ProgrammingLanguage.TYPESCRIPT]:
            formatted_content = await self._format_js_ts(code_file.content, language)
        else:
            # For unsupported languages, return the original content
            self.logger.warning(f"No formatter available for {language.value}")
            return code_file

        # Create new code file with formatted content
        return CodeFile(
            filename=code_file.filename,
            content=formatted_content,
            language=language
        )
    
    async def _format_python(self, content: str) -> str:
        """
        Format Python code using black

        Args:
            content: Python code content

        Returns:
            Formatted Python code
        """
        import black

        mode = black.Mode(
            line_length=88,
            string_normalization=True,
            is_pyi=False
        )

        return black.format_str(content, mode=mode)
    
    async def _format_js_ts(self, content: str, language: ProgrammingLanguage) -> str:
        """
        Format JavaScript/TypeScript code using prettier

        Args:
            content: JavaScript/TypeScript code content
            language: Programming language

        Returns:
            Formatted JavaScript/TypeScript code
        """
        # Determine parser based on language
        parser = "typescript" if language == ProgrammingLanguage.TYPESCRIPT else "babel"

        # Run prettier using asyncio subprocess
        process = await asyncio.create_subprocess_exec(
            "prettier", f"--parser={parser}", "--single-quote",
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate(input=content.encode('utf-8'))

        if process.returncode != 0:
            self.logger.error(f"prettier error: {stderr.decode()}")
            return content

        return stdout.decode()
    
    def _basic_python_formatter(self, content: str) -> str:
        """
        Basic Python formatter
        
        Args:
            content: Python code content
            
        Returns:
            Formatted Python code
        """
        # This is a very basic formatter that just ensures consistent indentation
        lines = content.split("\n")
        formatted_lines = []
        
        for line in lines:
            # Remove trailing whitespace
            formatted_line = line.rstrip()
            formatted_lines.append(formatted_line)
        
        return "\n".join(formatted_lines)
    
    def _basic_js_formatter(self, content: str) -> str:
        """
        Basic JavaScript formatter
        
        Args:
            content: JavaScript code content
            
        Returns:
            Formatted JavaScript code
        """
        # This is a very basic formatter that just ensures consistent indentation
        lines = content.split("\n")
        formatted_lines = []
        
        for line in lines:
            # Remove trailing whitespace
            formatted_line = line.rstrip()
            formatted_lines.append(formatted_line)
        
        return "\n".join(formatted_lines)
