"""
Code Context Model
Defines the data structure for code context
"""
from typing import Dict, List, Optional, Any, Set
from pydantic import BaseModel, Field

from code_generator.models.generation_request import ProgrammingLanguage, Framework


class CodeDependency(BaseModel):
    """
    Code dependency model
    
    Represents a dependency for code generation
    """
    name: str = Field(..., description="Dependency name")
    version: Optional[str] = Field(
        default=None,
        description="Dependency version"
    )
    is_dev: bool = Field(
        default=False,
        description="Whether this is a development dependency"
    )
    
    def __str__(self) -> str:
        """
        String representation
        
        Returns:
            String representation
        """
        if self.version:
            return f"{self.name}=={self.version}"
        
        return self.name


class FileReference(BaseModel):
    """
    File reference model
    
    Represents a reference to a file
    """
    path: str = Field(..., description="File path")
    content: Optional[str] = Field(
        default=None,
        description="File content"
    )
    is_directory: bool = Field(
        default=False,
        description="Whether this is a directory"
    )
    
    def is_empty(self) -> bool:
        """
        Check if file content is empty
        
        Returns:
            True if file content is empty, False otherwise
        """
        return self.content is None or self.content == ""


class CodeContext(BaseModel):
    """
    Code context model
    
    Represents the context for code generation
    """
    language: ProgrammingLanguage = Field(
        default=ProgrammingLanguage.PYTHON,
        description="Programming language"
    )
    framework: Optional[Framework] = Field(
        default=None,
        description="Framework to use"
    )
    dependencies: List[CodeDependency] = Field(
        default_factory=list,
        description="List of dependencies"
    )
    files: List[FileReference] = Field(
        default_factory=list,
        description="List of file references"
    )
    project_structure: Dict[str, Any] = Field(
        default_factory=dict,
        description="Project structure"
    )
    imports: Set[str] = Field(
        default_factory=set,
        description="Set of imports"
    )
    variables: Dict[str, Any] = Field(
        default_factory=dict,
        description="Dictionary of variables"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    
    def add_dependency(self, name: str, version: Optional[str] = None, is_dev: bool = False) -> None:
        """
        Add dependency
        
        Args:
            name: Dependency name
            version: Dependency version
            is_dev: Whether this is a development dependency
        """
        dependency = CodeDependency(name=name, version=version, is_dev=is_dev)
        self.dependencies.append(dependency)
    
    def add_file(self, path: str, content: Optional[str] = None, is_directory: bool = False) -> None:
        """
        Add file reference
        
        Args:
            path: File path
            content: File content
            is_directory: Whether this is a directory
        """
        file_ref = FileReference(path=path, content=content, is_directory=is_directory)
        self.files.append(file_ref)
    
    def get_file(self, path: str) -> Optional[FileReference]:
        """
        Get file reference by path
        
        Args:
            path: File path
            
        Returns:
            File reference if found, None otherwise
        """
        for file in self.files:
            if file.path == path:
                return file
        
        return None
    
    def add_import(self, import_statement: str) -> None:
        """
        Add import statement
        
        Args:
            import_statement: Import statement
        """
        self.imports.add(import_statement)
    
    def set_variable(self, name: str, value: Any) -> None:
        """
        Set variable
        
        Args:
            name: Variable name
            value: Variable value
        """
        self.variables[name] = value
    
    def get_variable(self, name: str, default: Any = None) -> Any:
        """
        Get variable
        
        Args:
            name: Variable name
            default: Default value if variable not found
            
        Returns:
            Variable value
        """
        return self.variables.get(name, default)
