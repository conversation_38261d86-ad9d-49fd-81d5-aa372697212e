"""
Generation Result Model
Defines the data structure for code generation results
"""
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

from code_generator.models.generation_request import ProgrammingLanguage


class CodeFile(BaseModel):
    """
    Code file model
    
    Represents a generated code file
    """
    filename: str = Field(..., description="Filename")
    content: str = Field(..., description="File content")
    language: ProgrammingLanguage = Field(
        default=ProgrammingLanguage.PYTHON,
        description="Programming language"
    )
    
    def get_extension(self) -> str:
        """
        Get file extension
        
        Returns:
            File extension
        """
        if "." in self.filename:
            return self.filename.split(".")[-1]
        
        # Default extension based on language
        language_extensions = {
            ProgrammingLanguage.PYTHON: "py",
            ProgrammingLanguage.JAVASCRIPT: "js",
            ProgrammingLanguage.TYPESCRIPT: "ts",
            ProgrammingLanguage.JAVA: "java",
            ProgrammingLanguage.CSHARP: "cs",
            ProgrammingLanguage.CPP: "cpp",
            ProgrammingLanguage.GO: "go",
            ProgrammingLanguage.RUST: "rs",
            ProgrammingLanguage.PHP: "php",
            ProgrammingLanguage.RUBY: "rb",
            ProgrammingLanguage.SWIFT: "swift",
            ProgrammingLanguage.KOTLIN: "kt",
            ProgrammingLanguage.SHELL: "sh",
            ProgrammingLanguage.SQL: "sql",
            ProgrammingLanguage.HTML: "html",
            ProgrammingLanguage.CSS: "css",
        }
        
        return language_extensions.get(self.language, "txt")


class GenerationResponse(BaseModel):
    """
    Code generation response model
    
    Represents the result of a code generation request
    """
    request_id: str = Field(..., description="Request ID")
    task_id: str = Field(..., description="Task ID")
    files: List[CodeFile] = Field(
        default_factory=list,
        description="List of generated code files"
    )
    explanation: str = Field(
        default="",
        description="Explanation of the generated code"
    )
    suggestions: List[str] = Field(
        default_factory=list,
        description="List of suggestions for improvement"
    )
    warnings: List[str] = Field(
        default_factory=list,
        description="List of warnings"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    execution_time_ms: int = Field(
        default=0,
        description="Execution time in milliseconds"
    )
    
    def get_file_by_name(self, filename: str) -> Optional[CodeFile]:
        """
        Get file by name
        
        Args:
            filename: Filename to find
            
        Returns:
            Code file if found, None otherwise
        """
        for file in self.files:
            if file.filename == filename:
                return file
        
        return None
    
    def get_file_by_extension(self, extension: str) -> Optional[CodeFile]:
        """
        Get first file with the given extension
        
        Args:
            extension: File extension to find (without dot)
            
        Returns:
            Code file if found, None otherwise
        """
        for file in self.files:
            if file.filename.endswith(f".{extension}"):
                return file
        
        return None
    
    def has_warnings(self) -> bool:
        """
        Check if there are warnings
        
        Returns:
            True if there are warnings, False otherwise
        """
        return len(self.warnings) > 0
