"""
Simple Generation Result Model for System Integration
"""
from typing import Dict, Optional, Any
from pydantic import BaseModel, Field


class GenerationResult(BaseModel):
    """Simple generation result model for system integration"""
    
    task_id: str = Field(..., description="Task ID")
    code: str = Field(..., description="Generated code")
    language: str = Field(..., description="Programming language")
    success: bool = Field(default=True, description="Whether generation was successful")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    generation_time: float = Field(default=0.0, description="Generation time in seconds")
    iteration: int = Field(default=1, description="Iteration number")
    model_used: Optional[str] = Field(default=None, description="Model used for generation")
    tokens_used: int = Field(default=0, description="Number of tokens used")
