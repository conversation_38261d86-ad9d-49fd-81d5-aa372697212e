"""
Security Scanner Service
Scans code for security vulnerabilities
"""
import os
import logging
import asyncio
import tempfile
import re
from typing import Dict, List, Any, Optional, Set, Tuple

from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory
from critique_engine.models.critique_result import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CodeIssue, IssueSeverity
from critique_engine.rules.security_rules import SecurityRules


class SecurityScanner:
    """
    Security Scanner Service
    
    Scans code for security vulnerabilities
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Security Scanner
        
        Args:
            config: Security scanner configuration
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.security_rules = SecurityRules()
    
    async def scan(self, request: CritiqueRequest) -> CritiqueResult:
        """
        Scan code for security vulnerabilities
        
        Args:
            request: Critique request
            
        Returns:
            Critique result
        """
        self.logger.info(f"Starting security scan for task {request.task_id}")
        
        # Create result object
        result = CritiqueResult(
            task_id=request.task_id,
            request_id=request.request_id
        )
        
        # Skip if security category not requested
        if not request.should_check_category(CritiqueCategory.SECURITY):
            self.logger.info("Security category not requested, skipping")
            return result
        
        # Scan each file
        for file in request.files:
            # Get language-specific rules
            rules = self.security_rules.get_rules_for_language(file.language.value)
            
            # Apply each rule
            for rule in rules:
                issues = rule.apply(file.filename, file.content)
                
                # Add issues to result
                for issue in issues:
                    result.add_issue(issue)
            
            # Apply common patterns
            issues = await self._scan_common_patterns(file.filename, file.content, file.language.value)
            
            # Add issues to result
            for issue in issues:
                result.add_issue(issue)
        
        self.logger.info(f"Security scan complete for task {request.task_id}, found {len(result.issues)} issues")
        
        return result
    
    async def _scan_common_patterns(
        self, 
        filename: str, 
        content: str, 
        language: str
    ) -> List[CodeIssue]:
        """
        Scan for common security patterns
        
        Args:
            filename: Filename
            content: File content
            language: Programming language
            
        Returns:
            List of security issues
        """
        issues = []
        
        # Check for hardcoded secrets
        secret_patterns = [
            (r"password\s*=\s*['\"]([^'\"]{8,})['\"]", "Hardcoded password"),
            (r"api[_]?key\s*=\s*['\"]([^'\"]{8,})['\"]", "Hardcoded API key"),
            (r"secret\s*=\s*['\"]([^'\"]{8,})['\"]", "Hardcoded secret"),
            (r"token\s*=\s*['\"]([^'\"]{8,})['\"]", "Hardcoded token"),
            (r"-----BEGIN\s+(?:RSA\s+)?PRIVATE\s+KEY-----", "Private key in code"),
        ]
        
        for pattern, issue_type in secret_patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-{issue_type.lower().replace(' ', '-')}-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.CRITICAL,
                    message=f"{issue_type} found in code",
                    code_snippet=line_content,
                    suggestion="Store sensitive information in environment variables or a secure vault"
                ))
        
        # Language-specific checks
        if language == "python":
            await self._scan_python_security(filename, content, issues)
        elif language in ["javascript", "typescript"]:
            await self._scan_js_security(filename, content, issues)
        
        return issues
    
    async def _scan_python_security(
        self, 
        filename: str, 
        content: str, 
        issues: List[CodeIssue]
    ) -> None:
        """
        Scan Python code for security issues
        
        Args:
            filename: Filename
            content: File content
            issues: List to add issues to
        """
        # Check for SQL injection
        sql_patterns = [
            r"execute\s*\(\s*[\"']SELECT.*?\+",
            r"execute\s*\(\s*[\"']INSERT.*?\+",
            r"execute\s*\(\s*[\"']UPDATE.*?\+",
            r"execute\s*\(\s*[\"']DELETE.*?\+",
            r"execute\s*\(\s*f[\"']SELECT",
            r"execute\s*\(\s*f[\"']INSERT",
            r"execute\s*\(\s*f[\"']UPDATE",
            r"execute\s*\(\s*f[\"']DELETE",
        ]
        
        for pattern in sql_patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-sql-injection-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.CRITICAL,
                    message="Potential SQL injection vulnerability",
                    code_snippet=line_content,
                    suggestion="Use parameterized queries or an ORM"
                ))
        
        # Check for unsafe deserialization
        unsafe_deserialize_patterns = [
            r"pickle\.loads\(",
            r"yaml\.load\(",
            r"eval\(",
        ]
        
        for pattern in unsafe_deserialize_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-unsafe-deserialization-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.HIGH,
                    message="Unsafe deserialization",
                    code_snippet=line_content,
                    suggestion="Use safer alternatives like pickle.loads with restrictions, yaml.safe_load, or ast.literal_eval"
                ))
        
        # Check for command injection
        cmd_injection_patterns = [
            r"os\.system\s*\(\s*[^)]*\+",
            r"os\.popen\s*\(\s*[^)]*\+",
            r"subprocess\.call\s*\(\s*[^)]*\+",
            r"subprocess\.Popen\s*\(\s*[^)]*\+",
            r"os\.system\s*\(\s*f[\"']",
            r"os\.popen\s*\(\s*f[\"']",
            r"subprocess\.call\s*\(\s*f[\"']",
            r"subprocess\.Popen\s*\(\s*f[\"']",
        ]
        
        for pattern in cmd_injection_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-command-injection-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.CRITICAL,
                    message="Potential command injection vulnerability",
                    code_snippet=line_content,
                    suggestion="Use subprocess.run with shell=False and pass arguments as a list"
                ))
    
    async def _scan_js_security(
        self, 
        filename: str, 
        content: str, 
        issues: List[CodeIssue]
    ) -> None:
        """
        Scan JavaScript/TypeScript code for security issues
        
        Args:
            filename: Filename
            content: File content
            issues: List to add issues to
        """
        # Check for eval
        eval_patterns = [
            r"eval\s*\(",
            r"new\s+Function\s*\(",
            r"setTimeout\s*\(\s*['\"]",
            r"setInterval\s*\(\s*['\"]",
        ]
        
        for pattern in eval_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-eval-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.HIGH,
                    message="Use of eval or equivalent is a security risk",
                    code_snippet=line_content,
                    suggestion="Avoid using eval, new Function, or passing strings to setTimeout/setInterval"
                ))
        
        # Check for SQL injection
        sql_patterns = [
            r"query\s*\(\s*[\"']SELECT.*?\+",
            r"query\s*\(\s*[\"']INSERT.*?\+",
            r"query\s*\(\s*[\"']UPDATE.*?\+",
            r"query\s*\(\s*[\"']DELETE.*?\+",
            r"query\s*\(\s*`SELECT",
            r"query\s*\(\s*`INSERT",
            r"query\s*\(\s*`UPDATE",
            r"query\s*\(\s*`DELETE",
        ]
        
        for pattern in sql_patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-sql-injection-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.CRITICAL,
                    message="Potential SQL injection vulnerability",
                    code_snippet=line_content,
                    suggestion="Use parameterized queries or an ORM"
                ))
        
        # Check for XSS
        xss_patterns = [
            r"innerHTML\s*=",
            r"outerHTML\s*=",
            r"document\.write\s*\(",
            r"\.insertAdjacentHTML\s*\(",
        ]
        
        for pattern in xss_patterns:
            for match in re.finditer(pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                line_content = content.split('\n')[line_num - 1].strip()
                
                issues.append(CodeIssue(
                    id=f"security-xss-{line_num}",
                    filename=filename,
                    line_start=line_num,
                    line_end=line_num,
                    category=CritiqueCategory.SECURITY,
                    severity=IssueSeverity.HIGH,
                    message="Potential XSS vulnerability",
                    code_snippet=line_content,
                    suggestion="Use textContent instead of innerHTML, or sanitize input with a library like DOMPurify"
                ))
