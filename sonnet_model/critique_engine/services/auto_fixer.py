"""
Auto-Fixer Service for Critique Engine
Provides automatic code fixing capabilities
"""

import re
import ast
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from critique_engine.models.code_issue import CodeIssue, IssueSeverity


class FixType(Enum):
    SYNTAX = "syntax"
    STYLE = "style"
    SECURITY = "security"
    PERFORMANCE = "performance"
    DOCUMENTATION = "documentation"


@dataclass
class CodeFix:
    """Represents a code fix"""
    issue_id: str
    fix_type: FixType
    description: str
    original_code: str
    fixed_code: str
    line_number: int
    confidence: float
    auto_applicable: bool = True


class AutoFixer:
    """
    Automatic code fixing service
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize auto-fixer"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.max_confidence_threshold = config.get("max_confidence_threshold", 0.9)
        self.min_confidence_threshold = config.get("min_confidence_threshold", 0.7)
    
    async def fix_issues(self, code: str, issues: List[CodeIssue], language: str = "python") -> Dict[str, Any]:
        """
        Fix code issues automatically
        
        Args:
            code: Original code
            issues: List of code issues
            language: Programming language
            
        Returns:
            Dictionary with fixed code and applied fixes
        """
        self.logger.info(f"Starting auto-fix for {len(issues)} issues")
        
        lines = code.split('\n')
        applied_fixes = []
        skipped_fixes = []
        
        # Sort issues by line number (reverse order to avoid line number shifts)
        sorted_issues = sorted(issues, key=lambda x: x.line_start or 0, reverse=True)
        
        for issue in sorted_issues:
            fix = await self._generate_fix(issue, lines, language)
            
            if fix and fix.auto_applicable and fix.confidence >= self.min_confidence_threshold:
                lines = self._apply_fix(lines, fix)
                applied_fixes.append(fix)
                self.logger.info(f"Applied fix for issue: {issue.description}")
            else:
                skipped_fixes.append({
                    "issue": issue,
                    "reason": "Low confidence" if fix and fix.confidence < self.min_confidence_threshold else "Not auto-applicable"
                })
        
        fixed_code = '\n'.join(lines)
        
        return {
            "original_code": code,
            "fixed_code": fixed_code,
            "applied_fixes": applied_fixes,
            "skipped_fixes": skipped_fixes,
            "fix_count": len(applied_fixes),
            "skip_count": len(skipped_fixes)
        }
    
    async def _generate_fix(self, issue: CodeIssue, lines: List[str], language: str) -> Optional[CodeFix]:
        """Generate a fix for a specific issue"""
        
        if not issue.line_start or issue.line_start > len(lines):
            return None
        
        line_idx = issue.line_start - 1
        original_line = lines[line_idx]
        
        # Determine fix type based on issue category
        fix_type = self._categorize_fix_type(issue)
        
        # Generate fix based on issue type
        if language == "python":
            return await self._fix_python_issue(issue, original_line, fix_type)
        elif language == "javascript":
            return await self._fix_javascript_issue(issue, original_line, fix_type)
        else:
            return await self._fix_generic_issue(issue, original_line, fix_type)
    
    def _categorize_fix_type(self, issue: CodeIssue) -> FixType:
        """Categorize the type of fix needed"""
        description_lower = issue.description.lower()
        
        if any(word in description_lower for word in ["syntax", "indentation", "whitespace"]):
            return FixType.SYNTAX
        elif any(word in description_lower for word in ["security", "vulnerability", "injection"]):
            return FixType.SECURITY
        elif any(word in description_lower for word in ["performance", "optimization", "slow"]):
            return FixType.PERFORMANCE
        elif any(word in description_lower for word in ["docstring", "documentation", "comment"]):
            return FixType.DOCUMENTATION
        else:
            return FixType.STYLE
    
    async def _fix_python_issue(self, issue: CodeIssue, original_line: str, fix_type: FixType) -> Optional[CodeFix]:
        """Fix Python-specific issues"""
        
        # Indentation fixes
        if "indentation" in issue.description.lower():
            return self._fix_python_indentation(issue, original_line)
        
        # Import organization
        if "import" in issue.description.lower() and "unused" in issue.description.lower():
            return self._fix_unused_import(issue, original_line)
        
        # Variable naming
        if "naming" in issue.description.lower():
            return self._fix_variable_naming(issue, original_line)
        
        # Missing docstring
        if "docstring" in issue.description.lower():
            return self._fix_missing_docstring(issue, original_line)
        
        # Line length
        if "line too long" in issue.description.lower():
            return self._fix_long_line(issue, original_line)
        
        # Whitespace issues
        if "whitespace" in issue.description.lower():
            return self._fix_whitespace(issue, original_line)
        
        return None
    
    def _fix_python_indentation(self, issue: CodeIssue, original_line: str) -> CodeFix:
        """Fix Python indentation issues"""
        # Calculate proper indentation
        content = original_line.lstrip()
        
        # Determine indentation level based on context
        if content.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'with ', 'try:')):
            indent = '    '  # 4 spaces for top-level
        elif content.startswith(('elif ', 'else:', 'except ', 'finally:')):
            indent = '    '  # Same level as if/try
        else:
            indent = '        '  # 8 spaces for nested
        
        fixed_line = indent + content
        
        return CodeFix(
            issue_id=f"indent_{issue.line_start}",
            fix_type=FixType.SYNTAX,
            description="Fixed indentation",
            original_code=original_line,
            fixed_code=fixed_line,
            line_number=issue.line_start,
            confidence=0.95
        )
    
    def _fix_unused_import(self, issue: CodeIssue, original_line: str) -> CodeFix:
        """Fix unused import statements"""
        return CodeFix(
            issue_id=f"import_{issue.line_start}",
            fix_type=FixType.STYLE,
            description="Removed unused import",
            original_code=original_line,
            fixed_code="",  # Remove the line
            line_number=issue.line_start,
            confidence=0.9
        )
    
    def _fix_variable_naming(self, issue: CodeIssue, original_line: str) -> CodeFix:
        """Fix variable naming issues"""
        # Convert camelCase to snake_case
        def camel_to_snake(name):
            s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
            return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
        
        # Find variable names in the line
        var_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
        
        fixed_line = original_line
        for match in re.finditer(var_pattern, original_line):
            var_name = match.group()
            if re.match(r'^[a-z]+[A-Z]', var_name):  # camelCase
                snake_name = camel_to_snake(var_name)
                fixed_line = fixed_line.replace(var_name, snake_name)
        
        if fixed_line != original_line:
            return CodeFix(
                issue_id=f"naming_{issue.line_start}",
                fix_type=FixType.STYLE,
                description="Fixed variable naming (camelCase to snake_case)",
                original_code=original_line,
                fixed_code=fixed_line,
                line_number=issue.line_start,
                confidence=0.8
            )
        
        return None
    
    def _fix_missing_docstring(self, issue: CodeIssue, original_line: str) -> CodeFix:
        """Add missing docstring"""
        if original_line.strip().startswith('def '):
            # Extract function name
            func_match = re.search(r'def\s+(\w+)\s*\(', original_line)
            if func_match:
                func_name = func_match.group(1)
                docstring = f'    """TODO: Add description for {func_name}."""'
                
                return CodeFix(
                    issue_id=f"docstring_{issue.line_start}",
                    fix_type=FixType.DOCUMENTATION,
                    description="Added placeholder docstring",
                    original_code=original_line,
                    fixed_code=f"{original_line}\n{docstring}",
                    line_number=issue.line_start,
                    confidence=0.7
                )
        
        return None
    
    def _fix_long_line(self, issue: CodeIssue, original_line: str) -> CodeFix:
        """Fix lines that are too long"""
        if len(original_line) > 88:
            # Simple line breaking for long lines
            indent = len(original_line) - len(original_line.lstrip())
            indent_str = ' ' * indent
            
            # Break at logical points
            if ',' in original_line:
                parts = original_line.split(',')
                fixed_lines = []
                current_line = parts[0]
                
                for part in parts[1:]:
                    if len(current_line + ',' + part.strip()) > 88:
                        fixed_lines.append(current_line + ',')
                        current_line = indent_str + '    ' + part.strip()
                    else:
                        current_line += ',' + part
                
                fixed_lines.append(current_line)
                fixed_code = '\n'.join(fixed_lines)
                
                return CodeFix(
                    issue_id=f"long_line_{issue.line_start}",
                    fix_type=FixType.STYLE,
                    description="Broke long line",
                    original_code=original_line,
                    fixed_code=fixed_code,
                    line_number=issue.line_start,
                    confidence=0.6
                )
        
        return None
    
    def _fix_whitespace(self, issue: CodeIssue, original_line: str) -> CodeFix:
        """Fix whitespace issues"""
        # Remove trailing whitespace
        fixed_line = original_line.rstrip()
        
        # Fix multiple spaces
        fixed_line = re.sub(r'  +', ' ', fixed_line)
        
        # Fix spaces around operators
        fixed_line = re.sub(r'(\w)\s*=\s*(\w)', r'\1 = \2', fixed_line)
        fixed_line = re.sub(r'(\w)\s*\+\s*(\w)', r'\1 + \2', fixed_line)
        fixed_line = re.sub(r'(\w)\s*-\s*(\w)', r'\1 - \2', fixed_line)
        
        if fixed_line != original_line:
            return CodeFix(
                issue_id=f"whitespace_{issue.line_start}",
                fix_type=FixType.SYNTAX,
                description="Fixed whitespace",
                original_code=original_line,
                fixed_code=fixed_line,
                line_number=issue.line_start,
                confidence=0.95
            )
        
        return None
    
    async def _fix_javascript_issue(self, issue: CodeIssue, original_line: str, fix_type: FixType) -> Optional[CodeFix]:
        """Fix JavaScript-specific issues"""
        
        # var to let/const
        if "var" in issue.description.lower():
            fixed_line = re.sub(r'\bvar\b', 'let', original_line)
            return CodeFix(
                issue_id=f"var_to_let_{issue.line_start}",
                fix_type=FixType.STYLE,
                description="Replaced var with let",
                original_code=original_line,
                fixed_code=fixed_line,
                line_number=issue.line_start,
                confidence=0.9
            )
        
        # == to ===
        if "loose equality" in issue.description.lower():
            fixed_line = original_line.replace('==', '===').replace('!=', '!==')
            return CodeFix(
                issue_id=f"strict_equality_{issue.line_start}",
                fix_type=FixType.STYLE,
                description="Used strict equality",
                original_code=original_line,
                fixed_code=fixed_line,
                line_number=issue.line_start,
                confidence=0.95
            )
        
        return None
    
    async def _fix_generic_issue(self, issue: CodeIssue, original_line: str, fix_type: FixType) -> Optional[CodeFix]:
        """Fix generic issues applicable to any language"""
        
        # TODO comments
        if "TODO" in original_line and "todo" in issue.description.lower():
            return CodeFix(
                issue_id=f"todo_{issue.line_start}",
                fix_type=FixType.DOCUMENTATION,
                description="TODO comment found (manual review needed)",
                original_code=original_line,
                fixed_code=original_line,  # Keep as-is, just flag for review
                line_number=issue.line_start,
                confidence=0.5,
                auto_applicable=False
            )
        
        return None
    
    def _apply_fix(self, lines: List[str], fix: CodeFix) -> List[str]:
        """Apply a fix to the code lines"""
        if not fix.line_number or fix.line_number > len(lines):
            return lines
        
        line_idx = fix.line_number - 1
        
        if fix.fixed_code == "":
            # Remove the line
            lines.pop(line_idx)
        elif '\n' in fix.fixed_code:
            # Replace with multiple lines
            new_lines = fix.fixed_code.split('\n')
            lines[line_idx:line_idx+1] = new_lines
        else:
            # Replace the line
            lines[line_idx] = fix.fixed_code
        
        return lines
    
    def get_fix_suggestions(self, issues: List[CodeIssue]) -> List[Dict[str, Any]]:
        """Get fix suggestions for issues that cannot be auto-fixed"""
        suggestions = []
        
        for issue in issues:
            suggestion = {
                "issue": issue,
                "suggestions": []
            }
            
            description_lower = issue.description.lower()
            
            if "complexity" in description_lower:
                suggestion["suggestions"].append("Consider breaking this function into smaller functions")
                suggestion["suggestions"].append("Use early returns to reduce nesting")
            
            if "security" in description_lower:
                suggestion["suggestions"].append("Review security implications carefully")
                suggestion["suggestions"].append("Consider input validation and sanitization")
            
            if "performance" in description_lower:
                suggestion["suggestions"].append("Profile this code to identify bottlenecks")
                suggestion["suggestions"].append("Consider using more efficient algorithms or data structures")
            
            if suggestion["suggestions"]:
                suggestions.append(suggestion)
        
        return suggestions
