"""
Test Runner Service
Runs tests on code to identify issues
"""
import os
import logging
import asyncio
import tempfile
import subprocess
from typing import Dict, List, Any, Optional, Set, Tuple

from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory
from critique_engine.models.critique_result import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CodeIssue, IssueSeverity


class TestRunner:
    """
    Test Runner Service
    
    Runs tests on code to identify issues
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Test Runner
        
        Args:
            config: Test runner configuration
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self.timeout = self.config.get("timeout", 60)
    
    async def run_tests(self, request: CritiqueRequest) -> CritiqueResult:
        """
        Run tests on code
        
        Args:
            request: Critique request
            
        Returns:
            Critique result
        """
        self.logger.info(f"Running tests for task {request.task_id}")
        
        # Create result object
        result = CritiqueResult(
            task_id=request.task_id,
            request_id=request.request_id
        )
        
        # Skip if tests category not requested
        if not request.should_check_category(CritiqueCategory.TESTS):
            self.logger.info("Tests category not requested, skipping")
            return result
        
        # Create temporary directory for tests
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write files to temporary directory
            for file in request.files:
                file_path = os.path.join(temp_dir, file.filename)
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(file.content)
            
            # Run tests based on language
            if request.language.value == "python":
                await self._run_python_tests(temp_dir, result)
            elif request.language.value in ["javascript", "typescript"]:
                await self._run_js_tests(temp_dir, result)
            else:
                # Add issue for unsupported language
                result.add_issue(CodeIssue(
                    id=f"test-unsupported-{request.language.value}",
                    filename="",
                    category=CritiqueCategory.TESTS,
                    severity=IssueSeverity.INFO,
                    message=f"Test running not supported for {request.language.value}"
                ))
        
        self.logger.info(f"Test running complete for task {request.task_id}")
        
        return result
    
    async def _run_python_tests(self, directory: str, result: CritiqueResult) -> None:
        """
        Run Python tests
        
        Args:
            directory: Directory containing code
            result: Critique result to update
        """
        # Check for pytest
        # Look for test files
        test_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if file.startswith("test_") and file.endswith(".py"):
                    test_files.append(os.path.join(root, file))

        if not test_files:
            # No test files found
            result.add_issue(CodeIssue(
                id="test-no-files",
                filename="",
                category=CritiqueCategory.TESTS,
                severity=IssueSeverity.MEDIUM,
                message="No test files found. Test files should start with 'test_' and end with '.py'",
                suggestion="Create test files following the naming convention 'test_*.py'"
            ))
            return

        # Run pytest
        cmd = ["python", "-m", "pytest", "-v"]
        cmd.extend(test_files)

        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=directory
        )

        stdout, stderr = await asyncio.wait_for(process.communicate(), self.timeout)
        stdout_str = stdout.decode("utf-8")
        stderr_str = stderr.decode("utf-8")

        # Check for test failures
        if process.returncode != 0:
            # Parse test failures
            failures = self._parse_pytest_failures(stdout_str, stderr_str)

            for i, (file, line, error) in enumerate(failures):
                result.add_issue(CodeIssue(
                    id=f"test-failure-{i+1}",
                    filename=file,
                    line_start=line,
                    category=CritiqueCategory.TESTS,
                    severity=IssueSeverity.HIGH,
                    message=f"Test failure: {error}",
                    suggestion="Fix the implementation to pass the test"
                ))

        # Check for no tests
        if "no tests ran" in stdout_str.lower() or "no tests ran" in stderr_str.lower():
            result.add_issue(CodeIssue(
                id="test-none-ran",
                filename="",
                category=CritiqueCategory.TESTS,
                severity=IssueSeverity.MEDIUM,
                message="No tests were executed",
                suggestion="Ensure test files contain test functions/methods"
            ))
    
    async def _run_js_tests(self, directory: str, result: CritiqueResult) -> None:
        """
        Run JavaScript/TypeScript tests
        
        Args:
            directory: Directory containing code
            result: Critique result to update
        """
        # Check for package.json
        package_json_path = os.path.join(directory, "package.json")
        
        if not os.path.exists(package_json_path):
            # No package.json found
            result.add_issue(CodeIssue(
                id="test-no-package-json",
                filename="",
                category=CritiqueCategory.TESTS,
                severity=IssueSeverity.MEDIUM,
                message="No package.json found",
                suggestion="Create a package.json file with test script configuration"
            ))
            return
        
        # Check for test files
        test_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if (file.endswith(".test.js") or file.endswith(".spec.js") or
                    file.endswith(".test.ts") or file.endswith(".spec.ts")):
                    test_files.append(os.path.join(root, file))

        if not test_files:
            # No test files found
            result.add_issue(CodeIssue(
                id="test-no-files",
                filename="",
                category=CritiqueCategory.TESTS,
                severity=IssueSeverity.MEDIUM,
                message="No test files found. Test files should end with '.test.js', '.spec.js', '.test.ts', or '.spec.ts'",
                suggestion="Create test files following the naming convention '*.test.js' or '*.spec.js'"
            ))
            return

        # Run npm test
        cmd = ["npm", "test"]

        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=directory
        )

        stdout, stderr = await asyncio.wait_for(process.communicate(), self.timeout)
        stdout_str = stdout.decode("utf-8")
        stderr_str = stderr.decode("utf-8")

        # Check for test failures
        if process.returncode != 0:
            # Parse test failures
            failures = self._parse_js_test_failures(stdout_str, stderr_str)

            for i, (file, line, error) in enumerate(failures):
                result.add_issue(CodeIssue(
                    id=f"test-failure-{i+1}",
                    filename=file,
                    line_start=line,
                    category=CritiqueCategory.TESTS,
                    severity=IssueSeverity.HIGH,
                    message=f"Test failure: {error}",
                    suggestion="Fix the implementation to pass the test"
                ))

        # Check for no tests
        if "no tests found" in stdout_str.lower() or "no tests found" in stderr_str.lower():
            result.add_issue(CodeIssue(
                id="test-none-found",
                filename="",
                category=CritiqueCategory.TESTS,
                severity=IssueSeverity.MEDIUM,
                message="No tests were found",
                suggestion="Ensure test files contain test functions/methods"
            ))
    
    def _parse_pytest_failures(self, stdout: str, stderr: str) -> List[Tuple[str, int, str]]:
        """
        Parse pytest failures
        
        Args:
            stdout: Standard output
            stderr: Standard error
            
        Returns:
            List of (file, line, error) tuples
        """
        failures = []
        
        # Combine stdout and stderr
        output = stdout + "\n" + stderr
        
        # Split by test failure
        sections = output.split("FAILED")
        
        for section in sections[1:]:  # Skip first section
            lines = section.strip().split("\n")
            
            for i, line in enumerate(lines):
                if ".py:" in line:
                    # Extract file and line
                    parts = line.split(".py:", 1)
                    if len(parts) == 2:
                        file = parts[0].split("/")[-1] + ".py"
                        
                        # Extract line number
                        line_parts = parts[1].split(":", 1)
                        if line_parts[0].isdigit():
                            line_num = int(line_parts[0])
                        else:
                            line_num = None
                        
                        # Extract error message
                        error = ""
                        for j in range(i+1, min(i+5, len(lines))):
                            if lines[j].strip() and not lines[j].startswith(" "):
                                error = lines[j].strip()
                                break
                        
                        failures.append((file, line_num, error))
                        break
        
        return failures
    
    def _parse_js_test_failures(self, stdout: str, stderr: str) -> List[Tuple[str, int, str]]:
        """
        Parse JavaScript test failures
        
        Args:
            stdout: Standard output
            stderr: Standard error
            
        Returns:
            List of (file, line, error) tuples
        """
        failures = []
        
        # Combine stdout and stderr
        output = stdout + "\n" + stderr
        
        # Look for Jest or Mocha style failures
        lines = output.split("\n")
        
        for i, line in enumerate(lines):
            if "● " in line:  # Jest failure
                # Extract error message
                error = line.split("● ", 1)[1].strip()
                
                # Look for file and line in next lines
                for j in range(i+1, min(i+5, len(lines))):
                    if ".js:" in lines[j] or ".ts:" in lines[j]:
                        file_line = lines[j].strip()
                        
                        # Extract file
                        file_parts = file_line.split(":", 1)
                        file = file_parts[0].split("/")[-1]
                        
                        # Extract line number
                        line_part = file_parts[1].split(":")[0] if len(file_parts) > 1 else ""
                        if line_part.isdigit():
                            line_num = int(line_part)
                        else:
                            line_num = None
                        
                        failures.append((file, line_num, error))
                        break
            
            elif "AssertionError" in line:  # Mocha failure
                error = line.strip()
                
                # Look for file and line in previous lines
                for j in range(i-1, max(0, i-5), -1):
                    if ".js:" in lines[j] or ".ts:" in lines[j]:
                        file_line = lines[j].strip()
                        
                        # Extract file
                        file_parts = file_line.split(":", 1)
                        file = file_parts[0].split("/")[-1]
                        
                        # Extract line number
                        line_part = file_parts[1].split(":")[0] if len(file_parts) > 1 else ""
                        if line_part.isdigit():
                            line_num = int(line_part)
                        else:
                            line_num = None
                        
                        failures.append((file, line_num, error))
                        break
        
        return failures
