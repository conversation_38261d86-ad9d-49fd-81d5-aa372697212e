"""
Requirement Traceability Matrix - Expert-Level Project Management

Implements comprehensive requirement tracking and traceability as specified
in the expert critique agent framework
"""

import logging
import json
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field
from pathlib import Path


class RequirementStatus(Enum):
    """Requirement implementation status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress" 
    IMPLEMENTED = "implemented"
    TESTED = "tested"
    VERIFIED = "verified"
    COMPLETED = "completed"


class RequirementPriority(Enum):
    """Requirement priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class RequirementType(Enum):
    """Types of requirements"""
    FUNCTIONAL = "functional"
    NON_FUNCTIONAL = "non_functional"
    SECURITY = "security"
    PERFORMANCE = "performance"
    USABILITY = "usability"
    RELIABILITY = "reliability"
    MAINTAINABILITY = "maintainability"


@dataclass
class TestCase:
    """Individual test case for requirement verification"""
    id: str
    name: str
    description: str
    test_type: str  # unit, integration, e2e, performance
    status: str     # pending, passed, failed
    coverage_percentage: float = 0.0
    execution_time: float = 0.0
    last_run: Optional[datetime] = None


@dataclass
class ImplementationArtifact:
    """Code artifact implementing a requirement"""
    file_path: str
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    line_start: Optional[int] = None
    line_end: Optional[int] = None
    implementation_percentage: float = 0.0
    quality_score: float = 0.0
    last_modified: Optional[datetime] = None


@dataclass
class Requirement:
    """Comprehensive requirement definition with full traceability"""
    id: str
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: RequirementPriority
    requirement_type: RequirementType
    status: RequirementStatus
    
    # Traceability links
    parent_requirements: List[str] = field(default_factory=list)
    child_requirements: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    # Implementation tracking
    implementation_artifacts: List[ImplementationArtifact] = field(default_factory=list)
    test_cases: List[TestCase] = field(default_factory=list)
    
    # Progress tracking
    implementation_percentage: float = 0.0
    test_coverage: float = 0.0
    verification_notes: str = ""
    
    # Metadata
    created_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    assigned_developer: Optional[str] = None
    estimated_effort: Optional[float] = None
    actual_effort: Optional[float] = None


class RequirementTraceabilityMatrix:
    """
    Comprehensive requirement traceability matrix implementing expert standards
    
    Features:
    - Complete requirement lifecycle tracking
    - Implementation artifact mapping
    - Test case coverage analysis
    - Dependency management
    - Progress reporting
    - Quality metrics integration
    """
    
    def __init__(self, project_path: str):
        self.project_path = Path(project_path)
        self.logger = logging.getLogger(__name__)
        
        # Core data structures
        self.requirements: Dict[str, Requirement] = {}
        self.traceability_matrix: Dict[str, Dict[str, Any]] = {}
        
        # Configuration
        self.matrix_file = self.project_path / "requirements_matrix.json"
        self.load_requirements_matrix()
    
    def add_requirement(
        self, 
        req_id: str,
        title: str,
        description: str,
        acceptance_criteria: List[str],
        priority: RequirementPriority,
        requirement_type: RequirementType,
        parent_id: Optional[str] = None
    ) -> Requirement:
        """Add new requirement to the matrix"""
        
        requirement = Requirement(
            id=req_id,
            title=title,
            description=description,
            acceptance_criteria=acceptance_criteria,
            priority=priority,
            requirement_type=requirement_type,
            status=RequirementStatus.PENDING
        )
        
        # Handle parent-child relationships
        if parent_id and parent_id in self.requirements:
            requirement.parent_requirements.append(parent_id)
            self.requirements[parent_id].child_requirements.append(req_id)
        
        self.requirements[req_id] = requirement
        self._update_traceability_matrix(req_id)
        self.save_requirements_matrix()
        
        self.logger.info(f"Added requirement {req_id}: {title}")
        return requirement
    
    def link_implementation_artifact(
        self, 
        req_id: str, 
        file_path: str,
        function_name: Optional[str] = None,
        class_name: Optional[str] = None,
        line_start: Optional[int] = None,
        line_end: Optional[int] = None
    ) -> bool:
        """Link code artifact to requirement"""
        
        if req_id not in self.requirements:
            self.logger.error(f"Requirement {req_id} not found")
            return False
        
        artifact = ImplementationArtifact(
            file_path=file_path,
            function_name=function_name,
            class_name=class_name,
            line_start=line_start,
            line_end=line_end,
            last_modified=datetime.now()
        )
        
        # Calculate implementation percentage based on artifact analysis
        artifact.implementation_percentage = self._analyze_implementation_completeness(
            req_id, artifact
        )
        
        self.requirements[req_id].implementation_artifacts.append(artifact)
        self._update_requirement_progress(req_id)
        self.save_requirements_matrix()
        
        self.logger.info(f"Linked artifact {file_path} to requirement {req_id}")
        return True
    
    def add_test_case(
        self, 
        req_id: str,
        test_id: str,
        test_name: str,
        description: str,
        test_type: str
    ) -> bool:
        """Add test case for requirement verification"""
        
        if req_id not in self.requirements:
            self.logger.error(f"Requirement {req_id} not found")
            return False
        
        test_case = TestCase(
            id=test_id,
            name=test_name,
            description=description,
            test_type=test_type,
            status="pending"
        )
        
        self.requirements[req_id].test_cases.append(test_case)
        self._update_requirement_progress(req_id)
        self.save_requirements_matrix()
        
        self.logger.info(f"Added test case {test_id} for requirement {req_id}")
        return True
    
    def update_requirement_status(
        self, 
        req_id: str, 
        new_status: RequirementStatus,
        verification_notes: str = ""
    ) -> bool:
        """Update requirement status with verification"""
        
        if req_id not in self.requirements:
            self.logger.error(f"Requirement {req_id} not found")
            return False
        
        old_status = self.requirements[req_id].status
        self.requirements[req_id].status = new_status
        self.requirements[req_id].verification_notes = verification_notes
        self.requirements[req_id].last_updated = datetime.now()
        
        # Auto-update dependent requirements
        self._propagate_status_changes(req_id, new_status)
        
        self.save_requirements_matrix()
        
        self.logger.info(f"Updated requirement {req_id} status: {old_status.value} -> {new_status.value}")
        return True
    
    def generate_traceability_report(self) -> Dict[str, Any]:
        """Generate comprehensive traceability report"""
        
        total_requirements = len(self.requirements)
        status_counts = {}
        priority_counts = {}
        type_counts = {}
        
        # Calculate statistics
        for req in self.requirements.values():
            status_counts[req.status.value] = status_counts.get(req.status.value, 0) + 1
            priority_counts[req.priority.value] = priority_counts.get(req.priority.value, 0) + 1
            type_counts[req.requirement_type.value] = type_counts.get(req.requirement_type.value, 0) + 1
        
        # Calculate completion metrics
        completed_requirements = status_counts.get("completed", 0)
        completion_percentage = (completed_requirements / total_requirements * 100) if total_requirements > 0 else 0
        
        # Calculate test coverage
        total_test_coverage = sum(req.test_coverage for req in self.requirements.values())
        average_test_coverage = (total_test_coverage / total_requirements) if total_requirements > 0 else 0
        
        # Calculate implementation progress
        total_implementation = sum(req.implementation_percentage for req in self.requirements.values())
        average_implementation = (total_implementation / total_requirements) if total_requirements > 0 else 0
        
        # Identify gaps and issues
        gaps_and_issues = self._identify_traceability_gaps()
        
        return {
            "summary": {
                "total_requirements": total_requirements,
                "completion_percentage": completion_percentage,
                "average_test_coverage": average_test_coverage,
                "average_implementation": average_implementation
            },
            "status_breakdown": status_counts,
            "priority_breakdown": priority_counts,
            "type_breakdown": type_counts,
            "gaps_and_issues": gaps_and_issues,
            "detailed_requirements": self._generate_detailed_requirement_report(),
            "dependency_analysis": self._analyze_requirement_dependencies(),
            "critical_path": self._identify_critical_path(),
            "recommendations": self._generate_improvement_recommendations()
        }
    
    def verify_requirement_completion(self, req_id: str) -> Dict[str, Any]:
        """Verify if requirement is truly complete"""
        
        if req_id not in self.requirements:
            return {"error": f"Requirement {req_id} not found"}
        
        req = self.requirements[req_id]
        verification_results = {
            "requirement_id": req_id,
            "title": req.title,
            "current_status": req.status.value,
            "verification_passed": True,
            "issues": []
        }
        
        # Check implementation completeness
        if req.implementation_percentage < 100:
            verification_results["verification_passed"] = False
            verification_results["issues"].append(
                f"Implementation only {req.implementation_percentage:.1f}% complete"
            )
        
        # Check test coverage
        if req.test_coverage < 90:  # 90% minimum for completion
            verification_results["verification_passed"] = False
            verification_results["issues"].append(
                f"Test coverage only {req.test_coverage:.1f}% (minimum 90% required)"
            )
        
        # Check acceptance criteria
        unmet_criteria = self._check_acceptance_criteria(req)
        if unmet_criteria:
            verification_results["verification_passed"] = False
            verification_results["issues"].extend(unmet_criteria)
        
        # Check dependencies
        unresolved_dependencies = self._check_requirement_dependencies(req_id)
        if unresolved_dependencies:
            verification_results["verification_passed"] = False
            verification_results["issues"].append(
                f"Unresolved dependencies: {', '.join(unresolved_dependencies)}"
            )
        
        return verification_results
    
    def generate_llm_guidance_for_incomplete_requirements(self) -> List[str]:
        """Generate specific guidance for LLM to complete missing requirements"""
        
        guidance = []
        incomplete_requirements = [
            req for req in self.requirements.values()
            if req.status not in [RequirementStatus.COMPLETED, RequirementStatus.VERIFIED]
        ]
        
        # Sort by priority and dependency order
        sorted_requirements = self._sort_requirements_by_priority_and_dependencies(
            incomplete_requirements
        )
        
        for req in sorted_requirements[:5]:  # Top 5 priority items
            guidance.append(self._generate_requirement_guidance(req))
        
        return guidance
    
    def _analyze_implementation_completeness(
        self, 
        req_id: str, 
        artifact: ImplementationArtifact
    ) -> float:
        """Analyze how complete the implementation is for a requirement"""
        
        # This would integrate with actual code analysis
        # For now, return a placeholder based on artifact presence
        if artifact.function_name or artifact.class_name:
            return 75.0  # Assume 75% complete if specific functions/classes are identified
        else:
            return 50.0  # Assume 50% complete for general file-level implementation
    
    def _update_requirement_progress(self, req_id: str) -> None:
        """Update overall progress for a requirement"""
        
        req = self.requirements[req_id]
        
        # Calculate implementation percentage
        if req.implementation_artifacts:
            total_implementation = sum(
                artifact.implementation_percentage 
                for artifact in req.implementation_artifacts
            )
            req.implementation_percentage = min(100.0, total_implementation)
        
        # Calculate test coverage
        if req.test_cases:
            passed_tests = sum(1 for test in req.test_cases if test.status == "passed")
            req.test_coverage = (passed_tests / len(req.test_cases)) * 100
        
        # Auto-update status based on progress
        if req.implementation_percentage >= 100 and req.test_coverage >= 90:
            if req.status in [RequirementStatus.PENDING, RequirementStatus.IN_PROGRESS]:
                req.status = RequirementStatus.IMPLEMENTED
        elif req.implementation_percentage > 0:
            if req.status == RequirementStatus.PENDING:
                req.status = RequirementStatus.IN_PROGRESS
    
    def _update_traceability_matrix(self, req_id: str) -> None:
        """Update the traceability matrix for a requirement"""
        
        req = self.requirements[req_id]
        self.traceability_matrix[req_id] = {
            "title": req.title,
            "status": req.status.value,
            "priority": req.priority.value,
            "type": req.requirement_type.value,
            "implementation_files": [
                artifact.file_path for artifact in req.implementation_artifacts
            ],
            "test_cases": [test.id for test in req.test_cases],
            "dependencies": req.dependencies,
            "children": req.child_requirements,
            "parents": req.parent_requirements
        }
    
    def _propagate_status_changes(self, req_id: str, new_status: RequirementStatus) -> None:
        """Propagate status changes to dependent requirements"""
        
        req = self.requirements[req_id]
        
        # If requirement is completed, check if parent requirements can be updated
        if new_status == RequirementStatus.COMPLETED:
            for parent_id in req.parent_requirements:
                if parent_id in self.requirements:
                    self._check_parent_completion(parent_id)
    
    def _check_parent_completion(self, parent_id: str) -> None:
        """Check if parent requirement can be marked as completed"""
        
        parent_req = self.requirements[parent_id]
        
        # Check if all child requirements are completed
        all_children_complete = all(
            self.requirements[child_id].status == RequirementStatus.COMPLETED
            for child_id in parent_req.child_requirements
            if child_id in self.requirements
        )
        
        if all_children_complete and parent_req.implementation_percentage >= 100:
            parent_req.status = RequirementStatus.COMPLETED
            parent_req.last_updated = datetime.now()
    
    def _identify_traceability_gaps(self) -> List[str]:
        """Identify gaps in requirement traceability"""
        
        gaps = []
        
        for req_id, req in self.requirements.items():
            # Check for missing implementation artifacts
            if not req.implementation_artifacts and req.status != RequirementStatus.PENDING:
                gaps.append(f"Requirement {req_id} has no linked implementation artifacts")
            
            # Check for missing test cases
            if not req.test_cases:
                gaps.append(f"Requirement {req_id} has no test cases")
            
            # Check for orphaned requirements (no parents or children)
            if (not req.parent_requirements and not req.child_requirements and 
                req.requirement_type == RequirementType.FUNCTIONAL):
                gaps.append(f"Requirement {req_id} appears to be orphaned (no relationships)")
        
        return gaps
    
    def _generate_detailed_requirement_report(self) -> List[Dict[str, Any]]:
        """Generate detailed report for each requirement"""
        
        detailed_report = []
        
        for req in self.requirements.values():
            req_report = {
                "id": req.id,
                "title": req.title,
                "status": req.status.value,
                "priority": req.priority.value,
                "implementation_percentage": req.implementation_percentage,
                "test_coverage": req.test_coverage,
                "implementation_files": len(req.implementation_artifacts),
                "test_cases": len(req.test_cases),
                "dependencies": len(req.dependencies),
                "verification_status": self.verify_requirement_completion(req.id)
            }
            detailed_report.append(req_report)
        
        return detailed_report
    
    def _analyze_requirement_dependencies(self) -> Dict[str, Any]:
        """Analyze requirement dependencies and identify critical paths"""
        
        dependency_graph = {}
        circular_dependencies = []
        
        for req_id, req in self.requirements.items():
            dependency_graph[req_id] = req.dependencies
            
            # Check for circular dependencies
            if self._has_circular_dependency(req_id, req.dependencies, set()):
                circular_dependencies.append(req_id)
        
        return {
            "dependency_graph": dependency_graph,
            "circular_dependencies": circular_dependencies,
            "dependency_depth": self._calculate_dependency_depth()
        }
    
    def _has_circular_dependency(self, req_id: str, dependencies: List[str], visited: Set[str]) -> bool:
        """Check for circular dependencies"""
        
        if req_id in visited:
            return True
        
        visited.add(req_id)
        
        for dep_id in dependencies:
            if dep_id in self.requirements:
                dep_dependencies = self.requirements[dep_id].dependencies
                if self._has_circular_dependency(dep_id, dep_dependencies, visited.copy()):
                    return True
        
        return False
    
    def _calculate_dependency_depth(self) -> Dict[str, int]:
        """Calculate dependency depth for each requirement"""
        
        depth_map = {}
        
        for req_id in self.requirements:
            depth_map[req_id] = self._get_requirement_depth(req_id, set())
        
        return depth_map
    
    def _get_requirement_depth(self, req_id: str, visited: Set[str]) -> int:
        """Get the dependency depth of a requirement"""
        
        if req_id in visited or req_id not in self.requirements:
            return 0
        
        visited.add(req_id)
        req = self.requirements[req_id]
        
        if not req.dependencies:
            return 1
        
        max_depth = 0
        for dep_id in req.dependencies:
            depth = self._get_requirement_depth(dep_id, visited.copy())
            max_depth = max(max_depth, depth)
        
        return max_depth + 1
    
    def _identify_critical_path(self) -> List[str]:
        """Identify the critical path through requirements"""
        
        # Find requirements with highest priority and deepest dependencies
        critical_requirements = []
        
        for req_id, req in self.requirements.items():
            if req.priority in [RequirementPriority.CRITICAL, RequirementPriority.HIGH]:
                critical_requirements.append((req_id, self._get_requirement_depth(req_id, set())))
        
        # Sort by depth (longest path first)
        critical_requirements.sort(key=lambda x: x[1], reverse=True)
        
        return [req_id for req_id, _ in critical_requirements[:10]]
    
    def _generate_improvement_recommendations(self) -> List[str]:
        """Generate recommendations for improving requirement traceability"""
        
        recommendations = []
        gaps = self._identify_traceability_gaps()
        
        if gaps:
            recommendations.append(
                f"Address {len(gaps)} traceability gaps by linking missing artifacts and tests"
            )
        
        incomplete_high_priority = [
            req for req in self.requirements.values()
            if req.priority in [RequirementPriority.CRITICAL, RequirementPriority.HIGH]
            and req.status != RequirementStatus.COMPLETED
        ]
        
        if incomplete_high_priority:
            recommendations.append(
                f"Focus on completing {len(incomplete_high_priority)} high-priority requirements"
            )
        
        low_test_coverage = [
            req for req in self.requirements.values()
            if req.test_coverage < 80
        ]
        
        if low_test_coverage:
            recommendations.append(
                f"Improve test coverage for {len(low_test_coverage)} requirements below 80%"
            )
        
        return recommendations
    
    def _sort_requirements_by_priority_and_dependencies(
        self, 
        requirements: List[Requirement]
    ) -> List[Requirement]:
        """Sort requirements by priority and dependency order"""
        
        # Create priority weights
        priority_weights = {
            RequirementPriority.CRITICAL: 4,
            RequirementPriority.HIGH: 3,
            RequirementPriority.MEDIUM: 2,
            RequirementPriority.LOW: 1
        }
        
        # Sort by priority weight and dependency depth
        return sorted(
            requirements,
            key=lambda req: (
                priority_weights[req.priority],
                -self._get_requirement_depth(req.id, set())  # Negative for reverse order
            ),
            reverse=True
        )
    
    def _generate_requirement_guidance(self, req: Requirement) -> str:
        """Generate specific guidance for completing a requirement"""
        
        guidance = f"**REQUIREMENT {req.id}: {req.title}**\n"
        guidance += f"Priority: {req.priority.value.upper()}\n"
        guidance += f"Current Status: {req.status.value}\n"
        guidance += f"Implementation: {req.implementation_percentage:.1f}% complete\n"
        guidance += f"Test Coverage: {req.test_coverage:.1f}%\n\n"
        
        guidance += "**IMMEDIATE ACTIONS REQUIRED:**\n"
        
        if req.implementation_percentage < 100:
            guidance += f"• Complete implementation in files: {', '.join([a.file_path for a in req.implementation_artifacts])}\n"
        
        if req.test_coverage < 90:
            guidance += f"• Add comprehensive test cases (currently {len(req.test_cases)} tests)\n"
        
        if req.acceptance_criteria:
            guidance += f"• Verify all acceptance criteria are met:\n"
            for criteria in req.acceptance_criteria:
                guidance += f"  - {criteria}\n"
        
        guidance += f"\n**DO NOT PROCEED TO OTHER REQUIREMENTS UNTIL {req.id} IS COMPLETED.**"
        
        return guidance
    
    def _check_acceptance_criteria(self, req: Requirement) -> List[str]:
        """Check which acceptance criteria are not yet met"""
        
        # This would integrate with actual verification logic
        # For now, return placeholder based on implementation status
        unmet_criteria = []
        
        if req.implementation_percentage < 100:
            unmet_criteria.extend([
                criteria for criteria in req.acceptance_criteria
                if "implement" in criteria.lower()
            ])
        
        if req.test_coverage < 90:
            unmet_criteria.extend([
                criteria for criteria in req.acceptance_criteria
                if "test" in criteria.lower()
            ])
        
        return unmet_criteria
    
    def _check_requirement_dependencies(self, req_id: str) -> List[str]:
        """Check for unresolved requirement dependencies"""
        
        req = self.requirements[req_id]
        unresolved = []
        
        for dep_id in req.dependencies:
            if dep_id in self.requirements:
                dep_req = self.requirements[dep_id]
                if dep_req.status != RequirementStatus.COMPLETED:
                    unresolved.append(dep_id)
            else:
                unresolved.append(f"{dep_id} (not found)")
        
        return unresolved
    
    def save_requirements_matrix(self) -> None:
        """Save requirements matrix to file"""
        
        matrix_data = {
            "requirements": {
                req_id: {
                    "id": req.id,
                    "title": req.title,
                    "description": req.description,
                    "acceptance_criteria": req.acceptance_criteria,
                    "priority": req.priority.value,
                    "requirement_type": req.requirement_type.value,
                    "status": req.status.value,
                    "implementation_percentage": req.implementation_percentage,
                    "test_coverage": req.test_coverage,
                    "dependencies": req.dependencies,
                    "parent_requirements": req.parent_requirements,
                    "child_requirements": req.child_requirements,
                    "created_date": req.created_date.isoformat(),
                    "last_updated": req.last_updated.isoformat()
                }
                for req_id, req in self.requirements.items()
            },
            "traceability_matrix": self.traceability_matrix,
            "last_saved": datetime.now().isoformat()
        }
        
        with open(self.matrix_file, 'w') as f:
            json.dump(matrix_data, f, indent=2)
    
    def load_requirements_matrix(self) -> None:
        """Load requirements matrix from file"""
        
        if not self.matrix_file.exists():
            self.logger.info("No existing requirements matrix found, starting fresh")
            return
        
        with open(self.matrix_file, 'r') as f:
            matrix_data = json.load(f)
        
        # Reconstruct requirements objects
        for req_id, req_data in matrix_data.get("requirements", {}).items():
            req = Requirement(
                id=req_data["id"],
                title=req_data["title"],
                description=req_data["description"],
                acceptance_criteria=req_data["acceptance_criteria"],
                priority=RequirementPriority(req_data["priority"]),
                requirement_type=RequirementType(req_data["requirement_type"]),
                status=RequirementStatus(req_data["status"])
            )
            
            req.implementation_percentage = req_data.get("implementation_percentage", 0.0)
            req.test_coverage = req_data.get("test_coverage", 0.0)
            req.dependencies = req_data.get("dependencies", [])
            req.parent_requirements = req_data.get("parent_requirements", [])
            req.child_requirements = req_data.get("child_requirements", [])
            req.created_date = datetime.fromisoformat(req_data["created_date"])
            req.last_updated = datetime.fromisoformat(req_data["last_updated"])
            
            self.requirements[req_id] = req
        
        self.traceability_matrix = matrix_data.get("traceability_matrix", {})
        
        self.logger.info(f"Loaded {len(self.requirements)} requirements from matrix")
