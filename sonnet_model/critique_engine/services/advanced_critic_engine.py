"""
Advanced Critic Engine - World-Class Critique Agent System

Implements the comprehensive critique framework from expert developer standards:
- Multi-dimensional assessment strategy
- Sophisticated prompting for continuous LLM motivation
- Quality threshold enforcement
- Requirement traceability matrix
- Professional standards enforcement
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field

from critique_engine.models.critique_request import CritiqueRequest
from critique_engine.models.critique_result import CritiqueResult
from critique_engine.models.code_issue import CodeIssue, IssueSeverity, IssueCategory


class ProjectPhase(Enum):
    """Project development phases with quality gates"""
    REQUIREMENTS_ANALYSIS = "requirements_analysis"
    DESIGN_APPROVAL = "design_approval"
    IMPLEMENTATION = "implementation"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    DEPLOYMENT = "deployment"
    COMPLETE = "complete"


class QualityGate(Enum):
    """Quality gates that must be passed for phase progression"""
    REQUIREMENTS_COMPLETE = "requirements_complete"
    DESIGN_APPROVED = "design_approved"
    IMPLEMENTATION_VERIFIED = "implementation_verified"
    TESTING_PASSED = "testing_passed"
    DOCUMENTATION_FINALIZED = "documentation_finalized"
    DEPLOYMENT_READY = "deployment_ready"


@dataclass
class RequirementItem:
    """Individual requirement with traceability"""
    id: str
    description: str
    priority: str  # high, medium, low
    status: str    # pending, in_progress, completed, verified
    implementation_files: List[str] = field(default_factory=list)
    test_coverage: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class QualityMetrics:
    """Comprehensive quality metrics tracking"""
    code_coverage: float = 0.0
    test_coverage: float = 0.0
    performance_score: float = 0.0
    security_score: float = 0.0
    maintainability_index: float = 0.0
    complexity_score: float = 0.0
    documentation_coverage: float = 0.0


@dataclass
class ProjectState:
    """Centralized project state management"""
    current_phase: ProjectPhase = ProjectPhase.REQUIREMENTS_ANALYSIS
    requirements: Dict[str, RequirementItem] = field(default_factory=dict)
    completed_features: List[str] = field(default_factory=list)
    outstanding_issues: List[str] = field(default_factory=list)
    quality_metrics: QualityMetrics = field(default_factory=QualityMetrics)
    testing_status: Dict[str, Any] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)


class AdvancedCriticEngine:
    """
    World-class critique agent system implementing expert developer standards
    
    Features:
    - Multi-dimensional assessment strategy
    - Sophisticated prompting for continuous motivation
    - Quality threshold enforcement
    - Requirement traceability matrix
    - Professional standards enforcement
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize project state
        self.project_state = ProjectState()
        
        # Quality thresholds (configurable)
        self.quality_thresholds = {
            "min_code_coverage": config.get("min_code_coverage", 80.0),
            "min_test_coverage": config.get("min_test_coverage", 90.0),
            "min_performance_score": config.get("min_performance_score", 85.0),
            "min_security_score": config.get("min_security_score", 95.0),
            "max_complexity_score": config.get("max_complexity_score", 10.0),
            "min_documentation_coverage": config.get("min_documentation_coverage", 75.0)
        }
        
        # Prompting strategies
        self.prompting_strategies = self._initialize_prompting_strategies()
        
        # Assessment framework
        self.assessment_framework = self._initialize_assessment_framework()
        
        # Track LLM interaction patterns for sophisticated coaching
        self.llm_interaction_history = []
        self.hesitation_patterns = self._initialize_hesitation_patterns()
        self.motivation_escalation_level = 0
    
    def _initialize_prompting_strategies(self) -> Dict[str, List[str]]:
        """Initialize sophisticated prompting strategies for continuous LLM motivation"""
        return {
            "momentum_maintenance": [
                "Your implementation approach is sound and on the right track. Continue with the current methodology and complete the remaining functions. The architecture you've established provides a solid foundation - build upon it to finish the {module_name}.",
                "Excellent progress on the {component_name} implementation. You've successfully handled {completed_count} out of {total_count} requirements. Maintain this momentum and proceed immediately to implement the remaining {remaining_count} requirements: {remaining_items}.",
                "The project timeline requires completion of this module today. You have the capability and have already demonstrated success with similar components. Do not pause - continue coding the {feature_name} using the same patterns you applied successfully in the {previous_module}."
            ],
            
            "task_continuation_enforcement": [
                "Do not stop here. Your next immediate task is to implement the {next_task} for the {component_name} you just created. Add {specific_requirements} following this pattern, then proceed to implement {next_requirement} without waiting for approval.",
                "This is a multi-part implementation that requires completion of all components to function properly. You have started the {current_component} - you must now complete the {required_components}. Partial completion is not acceptable.",
                "The current implementation meets basic requirements but falls short of production standards. Enhance it by adding {improvements_list}. Continue improving until it meets enterprise-grade quality."
            ],
            
            "quality_driven_motivation": [
                "Good foundation, but we need to elevate this to professional standards. Refactor the code to include proper design patterns, add comprehensive documentation, implement unit tests, and optimize for performance. Keep refining until each component is production-ready.",
                "This implementation would not pass a senior developer's code review. Prove your capabilities by implementing industry best practices: {best_practices_list}. Show that you can produce code that exceeds typical standards.",
                "The current solution is functional but not exceptional. Transform this into code that would impress experienced developers. Add {enhancement_list}. Don't settle for adequate - achieve excellence."
            ],
            
            "anti_stopping_mechanisms": [
                "Do not conclude this session. Your immediate next task is to implement the {next_feature}. Begin coding the {specific_components} and continue until all {feature_name} features are complete.",
                "The {previous_component} you just built is useless without the {required_component}. You must immediately implement {specific_requirements}. The system cannot function without all components working together.",
                "Now that you've implemented the {completed_component}, the entire application depends on you completing the {dependent_components}. Each component needs {requirements_list}. Continue building the complete {system_name} without interruption."
            ],
            
            "capability_validation": [
                "You have demonstrated exceptional coding ability in the previous modules. Apply that same level of expertise to complete the {current_task}. You're capable of implementing {complex_features}. Prove your advanced capabilities.",
                "This is an opportunity to showcase mastery of full-stack development. Complete the integration between your {backend_components} and the {frontend_components}. Implement {integration_requirements}. Demonstrate complete technical mastery.",
                "This codebase will serve as a template for future projects. Make it exemplary by implementing {exemplary_features}. Build something you'd be proud to showcase."
            ],
            
            "urgency_and_importance": [
                "The {critical_component} is on the critical path for launch. Without completing the {required_features}, the entire application launch is blocked. Continue immediately with these critical components.",
                "The client expects a fully functional {system_type}. You've built the {completed_component} - now complete the {remaining_components}. Deliver on the complete promise.",
                "Stopping is not an option. The {module_name} requires completion of {critical_features}. Continue implementing these critical security and business features immediately."
            ],
            
            "insufficiency_identification": [
                "This implementation only addresses {completion_percentage}% of the requirements. You need to complete the remaining functionality: {missing_features}. Partial implementation is not acceptable - complete the full feature set.",
                "You've implemented the happy path scenarios but ignored error conditions. Add comprehensive error handling for {error_scenarios}. A robust system handles all possible conditions.",
                "Your current implementation doesn't meet the minimum viable product standard. Add {mvp_requirements}. The system must be deployable and usable before we can consider any module complete."
            ]
        }
    
    def _initialize_assessment_framework(self) -> Dict[str, Dict[str, Any]]:
        """Initialize multi-dimensional assessment framework"""
        return {
            "functional_correctness": {
                "weight": 0.25,
                "criteria": [
                    "complete_feature_implementation",
                    "edge_case_handling", 
                    "algorithm_correctness",
                    "business_logic_adherence",
                    "requirement_traceability"
                ],
                "scoring_method": "requirement_completion_ratio"
            },
            
            "code_quality": {
                "weight": 0.20,
                "criteria": [
                    "naming_conventions",
                    "code_organization",
                    "comment_quality",
                    "function_design",
                    "architecture_patterns",
                    "maintainability"
                ],
                "scoring_method": "weighted_criteria_average"
            },
            
            "performance_scalability": {
                "weight": 0.15,
                "criteria": [
                    "computational_complexity",
                    "memory_usage",
                    "database_efficiency",
                    "bottleneck_identification",
                    "scalability_patterns"
                ],
                "scoring_method": "performance_benchmarks"
            },
            
            "security_analysis": {
                "weight": 0.20,
                "criteria": [
                    "input_validation",
                    "authentication_implementation",
                    "authorization_controls",
                    "data_sanitization",
                    "secure_coding_patterns"
                ],
                "scoring_method": "security_checklist_compliance"
            },
            
            "testing_strategy": {
                "weight": 0.20,
                "criteria": [
                    "unit_test_coverage",
                    "integration_test_coverage",
                    "end_to_end_test_coverage",
                    "edge_case_testing",
                    "performance_testing"
                ],
                "scoring_method": "coverage_analysis"
            }
        }
    
    def _initialize_hesitation_patterns(self) -> List[str]:
        """Initialize patterns that indicate LLM hesitation or stopping tendency"""
        return [
            "should i continue",
            "shall i proceed", 
            "do you want me to",
            "would you like me to",
            "i'm not sure if",
            "maybe i should stop",
            "this is getting complex",
            "error occurred",
            "failed to",
            "cannot complete",
            "unable to",
            "this might be too much",
            "perhaps we should",
            "would it be better to",
            "i think this is enough",
            "the implementation is getting large",
            "this is becoming complicated"
        ]
    
    async def provide_sophisticated_motivation(
        self, 
        llm_response: str, 
        context: Dict[str, Any]
    ) -> str:
        """
        Provide sophisticated motivation using expert prompting strategies
        
        Args:
            llm_response: The LLM's response to analyze
            context: Current project context
            
        Returns:
            Sophisticated motivational prompt
        """
        
        # Detect hesitation patterns
        hesitation_detected = self._detect_hesitation(llm_response)
        
        # Analyze current project state
        project_analysis = self._analyze_project_state()
        
        # Determine appropriate prompting strategy
        strategy = self._select_prompting_strategy(
            hesitation_detected, 
            project_analysis, 
            context
        )
        
        # Generate sophisticated prompt
        motivational_prompt = self._generate_sophisticated_prompt(
            strategy, 
            context, 
            project_analysis
        )
        
        # Escalate motivation if needed
        if hesitation_detected:
            motivational_prompt = self._escalate_motivation(motivational_prompt, context)
        
        # Track interaction for learning
        self._track_interaction(llm_response, motivational_prompt, hesitation_detected)
        
        return motivational_prompt
    
    def _detect_hesitation(self, llm_response: str) -> bool:
        """Detect if LLM is showing signs of hesitation or stopping"""
        response_lower = llm_response.lower()
        
        for pattern in self.hesitation_patterns:
            if pattern in response_lower:
                return True
        
        # Additional sophisticated detection
        question_marks = response_lower.count('?')
        uncertainty_words = ['maybe', 'perhaps', 'might', 'could', 'unsure']
        uncertainty_count = sum(1 for word in uncertainty_words if word in response_lower)
        
        # Heuristic: High question marks + uncertainty words = hesitation
        if question_marks > 2 and uncertainty_count > 1:
            return True
        
        return False
    
    def _analyze_project_state(self) -> Dict[str, Any]:
        """Analyze current project state for context-aware prompting"""
        total_requirements = len(self.project_state.requirements)
        completed_requirements = sum(
            1 for req in self.project_state.requirements.values() 
            if req.status == "completed"
        )
        
        completion_percentage = (
            (completed_requirements / total_requirements * 100) 
            if total_requirements > 0 else 0
        )
        
        return {
            "current_phase": self.project_state.current_phase.value,
            "completion_percentage": completion_percentage,
            "total_requirements": total_requirements,
            "completed_requirements": completed_requirements,
            "outstanding_issues": len(self.project_state.outstanding_issues),
            "quality_metrics": self.project_state.quality_metrics,
            "phase_ready_for_advancement": self._check_phase_advancement_readiness()
        }
    
    def _select_prompting_strategy(
        self, 
        hesitation_detected: bool, 
        project_analysis: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> str:
        """Select appropriate prompting strategy based on situation"""
        
        completion_percentage = project_analysis["completion_percentage"]
        
        if hesitation_detected:
            if completion_percentage < 30:
                return "momentum_maintenance"
            elif completion_percentage < 70:
                return "task_continuation_enforcement"
            else:
                return "anti_stopping_mechanisms"
        
        # Quality-driven motivation for different completion levels
        if completion_percentage < 50:
            return "capability_validation"
        elif completion_percentage < 80:
            return "quality_driven_motivation"
        else:
            return "urgency_and_importance"
    
    def _generate_sophisticated_prompt(
        self, 
        strategy: str, 
        context: Dict[str, Any], 
        project_analysis: Dict[str, Any]
    ) -> str:
        """Generate sophisticated motivational prompt"""
        
        prompts = self.prompting_strategies.get(strategy, [])
        if not prompts:
            return self._generate_fallback_prompt(context)
        
        # Select appropriate prompt template
        prompt_template = prompts[self.motivation_escalation_level % len(prompts)]
        
        # Fill in context-specific information
        formatted_prompt = self._format_prompt_with_context(
            prompt_template, 
            context, 
            project_analysis
        )
        
        return formatted_prompt
    
    def _format_prompt_with_context(
        self, 
        template: str, 
        context: Dict[str, Any], 
        project_analysis: Dict[str, Any]
    ) -> str:
        """Format prompt template with specific project context"""
        
        # Extract context information
        module_name = context.get("current_module", "current module")
        component_name = context.get("current_component", "component")
        feature_name = context.get("current_feature", "feature")
        
        # Calculate dynamic values
        remaining_requirements = self._get_remaining_requirements()
        next_tasks = self._get_next_priority_tasks()
        
        # Format template with context
        formatted = template.format(
            module_name=module_name,
            component_name=component_name,
            feature_name=feature_name,
            completed_count=project_analysis["completed_requirements"],
            total_count=project_analysis["total_requirements"],
            remaining_count=len(remaining_requirements),
            remaining_items=", ".join(remaining_requirements[:3]),
            next_task=next_tasks[0] if next_tasks else "next implementation task",
            completion_percentage=int(project_analysis["completion_percentage"]),
            missing_features=", ".join(remaining_requirements[:5])
        )
        
        return formatted
    
    def _escalate_motivation(self, base_prompt: str, context: Dict[str, Any]) -> str:
        """Escalate motivation level for persistent hesitation"""
        
        self.motivation_escalation_level += 1
        
        escalation_additions = [
            "\n\n**CRITICAL: DO NOT STOP. CONTINUE IMMEDIATELY.**",
            "\n\n**URGENT: The project depends on your continued progress. Push forward now.**",
            "\n\n**MANDATORY: Complete this implementation. Stopping is not acceptable.**",
            "\n\n**FINAL NOTICE: You must finish this task. No exceptions, no delays.**"
        ]
        
        if self.motivation_escalation_level < len(escalation_additions):
            escalation = escalation_additions[self.motivation_escalation_level]
            return base_prompt + escalation
        
        # Maximum escalation
        return (base_prompt + 
                "\n\n**ABSOLUTE REQUIREMENT: COMPLETE THIS TASK NOW. "
                "FAILURE IS NOT AN OPTION. CONTINUE CODING IMMEDIATELY.**")
    
    def _track_interaction(
        self, 
        llm_response: str, 
        motivational_prompt: str, 
        hesitation_detected: bool
    ) -> None:
        """Track interaction for learning and improvement"""
        
        interaction = {
            "timestamp": datetime.now(),
            "llm_response_length": len(llm_response),
            "hesitation_detected": hesitation_detected,
            "motivation_escalation_level": self.motivation_escalation_level,
            "prompt_strategy": "sophisticated_motivation",
            "response_effectiveness": None  # To be filled by follow-up analysis
        }
        
        self.llm_interaction_history.append(interaction)
        
        # Keep history manageable
        if len(self.llm_interaction_history) > 100:
            self.llm_interaction_history = self.llm_interaction_history[-50:]
    
    def _get_remaining_requirements(self) -> List[str]:
        """Get list of remaining requirements"""
        return [
            req.description for req in self.project_state.requirements.values()
            if req.status != "completed"
        ]
    
    def _get_next_priority_tasks(self) -> List[str]:
        """Get next priority tasks based on project state"""
        # This would be enhanced with actual project analysis
        return [
            "implement error handling",
            "add input validation", 
            "create unit tests",
            "optimize performance",
            "add documentation"
        ]
    
    def _check_phase_advancement_readiness(self) -> bool:
        """Check if current phase is ready for advancement"""
        current_phase = self.project_state.current_phase
        
        # Define phase advancement criteria
        advancement_criteria = {
            ProjectPhase.REQUIREMENTS_ANALYSIS: lambda: len(self.project_state.requirements) > 0,
            ProjectPhase.DESIGN_APPROVAL: lambda: self._check_design_completeness(),
            ProjectPhase.IMPLEMENTATION: lambda: self._check_implementation_completeness(),
            ProjectPhase.TESTING: lambda: self._check_testing_completeness(),
            ProjectPhase.DOCUMENTATION: lambda: self._check_documentation_completeness(),
            ProjectPhase.DEPLOYMENT: lambda: self._check_deployment_readiness()
        }
        
        criteria_check = advancement_criteria.get(current_phase)
        return criteria_check() if criteria_check else False
    
    def _check_design_completeness(self) -> bool:
        """Check if design phase is complete"""
        # Placeholder - would implement actual design completeness checks
        return True
    
    def _check_implementation_completeness(self) -> bool:
        """Check if implementation phase is complete"""
        completed_ratio = (
            len([r for r in self.project_state.requirements.values() if r.status == "completed"]) /
            len(self.project_state.requirements)
        ) if self.project_state.requirements else 0
        
        return completed_ratio >= 0.8  # 80% implementation complete
    
    def _check_testing_completeness(self) -> bool:
        """Check if testing phase is complete"""
        return (
            self.project_state.quality_metrics.test_coverage >= 
            self.quality_thresholds["min_test_coverage"]
        )
    
    def _check_documentation_completeness(self) -> bool:
        """Check if documentation phase is complete"""
        return (
            self.project_state.quality_metrics.documentation_coverage >= 
            self.quality_thresholds["min_documentation_coverage"]
        )
    
    def _check_deployment_readiness(self) -> bool:
        """Check if system is ready for deployment"""
        metrics = self.project_state.quality_metrics
        return (
            metrics.code_coverage >= self.quality_thresholds["min_code_coverage"] and
            metrics.test_coverage >= self.quality_thresholds["min_test_coverage"] and
            metrics.security_score >= self.quality_thresholds["min_security_score"] and
            len(self.project_state.outstanding_issues) == 0
        )
    
    def _generate_fallback_prompt(self, context: Dict[str, Any]) -> str:
        """Generate fallback motivational prompt"""
        return (
            "Continue with your excellent implementation work. "
            "You're making great progress and should proceed with "
            "the next logical step in your development process. "
            "Don't stop now - keep building!"
        )
    
    async def enforce_quality_gates(self, current_work: Dict[str, Any]) -> Dict[str, Any]:
        """Enforce quality gates before allowing phase progression"""
        
        current_phase = self.project_state.current_phase
        quality_assessment = await self._assess_quality_metrics(current_work)
        
        # Check if quality thresholds are met
        gates_passed = self._evaluate_quality_gates(quality_assessment)
        
        if not gates_passed:
            return {
                "phase_advancement_allowed": False,
                "blocking_issues": self._identify_blocking_issues(quality_assessment),
                "required_improvements": self._generate_improvement_requirements(quality_assessment),
                "motivational_prompt": self._generate_quality_enforcement_prompt(quality_assessment)
            }
        
        return {
            "phase_advancement_allowed": True,
            "quality_assessment": quality_assessment,
            "next_phase": self._get_next_phase(),
            "congratulatory_message": self._generate_phase_completion_message()
        }
    
    async def _assess_quality_metrics(self, current_work: Dict[str, Any]) -> Dict[str, Any]:
        """Assess current quality metrics"""
        # This would integrate with actual code analysis tools
        return {
            "code_coverage": 85.0,
            "test_coverage": 92.0,
            "security_score": 88.0,
            "performance_score": 90.0,
            "maintainability_index": 85.0,
            "complexity_score": 8.5,
            "documentation_coverage": 78.0
        }
    
    def _evaluate_quality_gates(self, quality_assessment: Dict[str, Any]) -> bool:
        """Evaluate if quality gates are passed"""
        for metric, threshold in self.quality_thresholds.items():
            metric_key = metric.replace("min_", "").replace("max_", "")
            current_value = quality_assessment.get(metric_key, 0)
            
            if "min_" in metric and current_value < threshold:
                return False
            elif "max_" in metric and current_value > threshold:
                return False
        
        return True
    
    def _identify_blocking_issues(self, quality_assessment: Dict[str, Any]) -> List[str]:
        """Identify issues blocking phase advancement"""
        blocking_issues = []
        
        for metric, threshold in self.quality_thresholds.items():
            metric_key = metric.replace("min_", "").replace("max_", "")
            current_value = quality_assessment.get(metric_key, 0)
            
            if "min_" in metric and current_value < threshold:
                blocking_issues.append(
                    f"{metric_key} is {current_value:.1f}%, below required {threshold}%"
                )
            elif "max_" in metric and current_value > threshold:
                blocking_issues.append(
                    f"{metric_key} is {current_value:.1f}, above maximum {threshold}"
                )
        
        return blocking_issues
    
    def _generate_improvement_requirements(self, quality_assessment: Dict[str, Any]) -> List[str]:
        """Generate specific improvement requirements"""
        improvements = []
        
        if quality_assessment.get("code_coverage", 0) < self.quality_thresholds["min_code_coverage"]:
            improvements.append("Add unit tests to increase code coverage to 80%+")
        
        if quality_assessment.get("security_score", 0) < self.quality_thresholds["min_security_score"]:
            improvements.append("Address security vulnerabilities and implement secure coding practices")
        
        if quality_assessment.get("performance_score", 0) < self.quality_thresholds["min_performance_score"]:
            improvements.append("Optimize performance bottlenecks and improve response times")
        
        return improvements
    
    def _generate_quality_enforcement_prompt(self, quality_assessment: Dict[str, Any]) -> str:
        """Generate prompt to enforce quality improvements"""
        blocking_issues = self._identify_blocking_issues(quality_assessment)
        improvements = self._generate_improvement_requirements(quality_assessment)
        
        prompt = (
            "**QUALITY GATE ENFORCEMENT: Phase advancement blocked due to quality issues.**\n\n"
            "Your current implementation does not meet the required quality standards for "
            "progression to the next phase. You must address the following issues:\n\n"
        )
        
        for issue in blocking_issues:
            prompt += f"❌ {issue}\n"
        
        prompt += "\n**REQUIRED IMPROVEMENTS:**\n"
        for improvement in improvements:
            prompt += f"🔧 {improvement}\n"
        
        prompt += (
            "\n**DO NOT PROCEED TO THE NEXT PHASE UNTIL ALL QUALITY GATES ARE PASSED.**\n"
            "Continue improving the current implementation to meet professional standards. "
            "Quality is non-negotiable - achieve excellence before moving forward."
        )
        
        return prompt
    
    def _get_next_phase(self) -> ProjectPhase:
        """Get the next project phase"""
        phases = list(ProjectPhase)
        current_index = phases.index(self.project_state.current_phase)
        
        if current_index < len(phases) - 1:
            return phases[current_index + 1]
        
        return ProjectPhase.COMPLETE
    
    def _generate_phase_completion_message(self) -> str:
        """Generate congratulatory message for phase completion"""
        return (
            f"🎉 **EXCELLENT WORK!** {self.project_state.current_phase.value.title()} "
            f"phase completed successfully with all quality gates passed. "
            f"Advancing to {self._get_next_phase().value.title()} phase. "
            f"Continue with the same level of excellence!"
        )
