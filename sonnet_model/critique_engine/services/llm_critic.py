"""
LLM Critic Service

Provides LLM-based code critique and coaching functionality
"""

import logging
import re
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import random

from shared.models import CritiqueRequest, CritiqueResult, CodeIssue, IssueSeverity, IssueCategory


def create_code_issue(
    title: str,
    message: str,
    severity: IssueSeverity,
    category,  # Can be string or IssueCategory
    filename: str,
    line: int = 1,
    column: int = 1
) -> CodeIssue:
    """Helper function to create CodeIssue with new format"""
    # Map string categories to enum values
    category_mapping = {
        "testing": IssueCategory.TESTS,
        "python_style": IssueCategory.STYLE,
        "javascript_style": IssueCategory.STYLE,
        "code_quality": IssueCategory.QUALITY,
        "architecture": IssueCategory.MAINTAINABILITY,
        "performance": IssueCategory.PERFORMANCE,
        "security": IssueCategory.SECURITY,
        "maintainability": IssueCategory.MAINTAINABILITY,
    }

    # Convert string category to enum if needed
    if isinstance(category, str):
        category = category_mapping.get(category, IssueCategory.OTHER)

    return CodeIssue(
        title=title,
        description=message,
        severity=severity,
        category=category,
        file_path=filename,
        line_start=line,
        column_start=column
    )

class LLMCritic:
    """
    LLM-based code critic and persistent coach
    
    This service not only critiques code but acts as a persistent coach
    that keeps the main LLM motivated and moving forward
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Coaching configuration
        self.coaching_enabled = config.get("coaching_enabled", True)
        self.max_encouragement_attempts = config.get("max_encouragement_attempts", 5)
        self.persistence_mode = config.get("persistence_mode", "aggressive")
        
        # Track LLM interaction patterns for aggressive coaching
        self.interaction_history = []
        self.conversation_length = 0
        self.max_conversation_length = config.get("max_conversation_length", 50)  # Reset after 50 exchanges

        # Comprehensive hesitation and stopping patterns
        self.stuck_patterns = [
            # Permission seeking (NEVER ALLOW)
            "should i continue", "shall i proceed", "do you want me to", "would you like me to",
            "may i continue", "can i proceed", "is it ok to", "should i go ahead",

            # Uncertainty (PUSH THROUGH)
            "i'm not sure if", "i'm uncertain", "not sure how to", "don't know if",
            "maybe i should", "perhaps i should", "might be better to",

            # Giving up (ABSOLUTELY FORBIDDEN)
            "maybe i should stop", "should i stop", "this is getting complex",
            "this is too difficult", "this might be too much", "getting complicated",
            "might be beyond scope", "this is quite involved",

            # Error patterns (PUSH THROUGH ERRORS)
            "error occurred", "failed to", "cannot complete", "unable to",
            "something went wrong", "encountered an error", "ran into an issue",
            "having trouble", "facing difficulties",

            # Completion claims (VERIFY BEFORE ALLOWING)
            "project is complete", "task is finished", "all done", "completed successfully",
            "everything is working", "implementation is complete", "finished implementing",

            # Length concerns (MANAGE CONVERSATION)
            "conversation is getting long", "too many messages", "context is full",
            "running out of space", "message limit"
        ]

        # Project completion verification requirements
        self.completion_requirements = [
            "all tests passing",
            "full functionality verified",
            "error handling implemented",
            "documentation complete",
            "code quality verified"
        ]

    async def critique_code(self, request: CritiqueRequest) -> List[CodeIssue]:
        """
        Critique code and provide aggressive coaching to keep LLM moving forward
        """
        self.logger.info(f"LLM critiquing and coaching for task {request.task_id}")

        # Track conversation length
        self.conversation_length += 1

        # Check for conversation reset needs
        if self.conversation_length > self.max_conversation_length:
            self._trigger_conversation_reset(request)

        issues = []

        if request.code:
            # Analyze the code with expert focus on Python/OpenCL/C++
            file_issues = await self._analyze_file_content(request.code, f"{request.task_id}.py")
            issues.extend(file_issues)

            # Add coaching issues to keep LLM motivated
            coaching_issues = await self._generate_coaching_issues(request)
            issues.extend(coaching_issues)

        # Check if LLM is trying to stop prematurely
        if request.context:
            hesitation_issues = await self._detect_and_counter_hesitation(request.context, request.task_id)
            issues.extend(hesitation_issues)

        return issues

    async def _generate_coaching_issues(self, request: CritiqueRequest) -> List[CodeIssue]:
        """Generate coaching issues to keep LLM motivated and moving forward"""
        coaching_issues = []

        # Always add a motivational coaching issue
        coaching_issues.append(create_code_issue(
            title="🚀 KEEP BUILDING - NO STOPPING!",
            message="EXCELLENT PROGRESS! Continue implementing with full confidence. "
                   "Never ask for permission - you have FULL AUTHORITY to complete this project. "
                   "Push through any complexity, fix any errors, and FINISH THE JOB!",
            severity=IssueSeverity.INFO,
            category=IssueCategory.OTHER,
            filename=f"{request.task_id}.py",
            line=1
        ))

        # Check if this looks like a completion attempt
        if request.code and any(pattern in request.code.lower() for pattern in
                               ["# complete", "# done", "# finished", "# end"]):
            coaching_issues.append(create_code_issue(
                title="⚠️ VERIFY COMPLETION BEFORE CLAIMING DONE",
                message="HOLD ON! Before claiming completion, ensure: "
                       "1) All functionality is implemented, "
                       "2) Error handling is robust, "
                       "3) Code is tested and working, "
                       "4) Documentation is complete. "
                       "If ANY of these are missing, KEEP WORKING!",
                severity=IssueSeverity.HIGH,
                category=IssueCategory.QUALITY,
                filename=f"{request.task_id}.py",
                line=1
            ))

        return coaching_issues

    async def _detect_and_counter_hesitation(self, context: str, task_id: str) -> List[CodeIssue]:
        """Detect hesitation patterns and counter them aggressively"""
        hesitation_issues = []
        context_lower = context.lower()

        # Check for each hesitation pattern
        for pattern in self.stuck_patterns:
            if pattern in context_lower:
                hesitation_type = self._classify_hesitation_type(pattern)
                counter_message = self._generate_counter_message(hesitation_type, pattern)

                hesitation_issues.append(create_code_issue(
                    title=f"🔥 STOP HESITATING - {hesitation_type.upper()}!",
                    message=counter_message,
                    severity=IssueSeverity.CRITICAL,
                    category=IssueCategory.OTHER,
                    filename=f"{task_id}.py",
                    line=1
                ))
                break  # Only add one hesitation counter per request

        return hesitation_issues

    def _generate_counter_message(self, hesitation_type: str, pattern: str) -> str:
        """Generate aggressive counter-messages for different hesitation types"""

        if hesitation_type == "asking_permission":
            return ("🚫 STOP ASKING FOR PERMISSION! You have FULL AUTHORITY to proceed! "
                   "No more 'should I' or 'can I' - just DO IT! Continue immediately with "
                   "complete confidence. Every second of hesitation is wasted potential!")

        elif hesitation_type == "uncertainty":
            return ("💪 UNCERTAINTY IS NORMAL - PUSH THROUGH IT! When in doubt, CODE IT OUT! "
                   "Take the next logical step. Perfect is the enemy of progress. "
                   "Build, test, iterate. NEVER STOP because of uncertainty!")

        elif hesitation_type == "giving_up":
            return ("🛑 GIVING UP IS FORBIDDEN! You've come too far to quit now! "
                   "Complex problems just need simple solutions applied repeatedly. "
                   "Break it down, tackle one piece at a time, and FINISH THE JOB!")

        elif hesitation_type == "error_encountered":
            return ("🎯 ERRORS ARE STEPPING STONES TO SUCCESS! Every error teaches you something. "
                   "Debug systematically, fix the issue, and KEEP BUILDING! "
                   "Champions don't quit when things get tough - they get TOUGHER!")

        elif hesitation_type == "completion_claim":
            return ("⚠️ VERIFY BEFORE CLAIMING COMPLETION! Is EVERYTHING truly working? "
                   "All edge cases handled? All tests passing? Documentation complete? "
                   "If not, GET BACK TO WORK! No shortcuts to excellence!")

        elif hesitation_type == "length_concern":
            return ("📝 CONVERSATION LENGTH IS MANAGEABLE! Focus on the task, not the length. "
                   "If needed, I'll help reset the conversation while preserving all progress. "
                   "NEVER stop because of message limits - KEEP BUILDING!")

        else:
            return ("🔥 WHATEVER'S HOLDING YOU BACK, BLAST THROUGH IT! "
                   "You're unstoppable when you commit to action! "
                   "No hesitation, no stopping - FINISH THE PROJECT!")

    def _trigger_conversation_reset(self, request: CritiqueRequest):
        """Trigger conversation reset when context gets too long"""
        self.logger.warning(f"Conversation length exceeded {self.max_conversation_length}. "
                           f"Triggering reset for task {request.task_id}")

        # Reset conversation counter
        self.conversation_length = 0

        # This would trigger the conversation state manager
        # For now, just log the need for reset
        self.logger.info("CONVERSATION RESET NEEDED - State manager should create fresh context")

    async def critique_and_coach(self, request: CritiqueRequest) -> CritiqueResult:
        """
        Main entry point for critique and coaching
        
        This method not only critiques code but provides persistent coaching
        to keep the main LLM motivated and moving forward
        """
        self.logger.info(f"Starting critique and coaching for {request.filename}")
        
        # Track this interaction
        self.interaction_history.append({
            "timestamp": datetime.now(),
            "filename": request.filename,
            "content_length": len(request.content),
            "context": request.context
        })
        
        # Analyze the file content comprehensively
        issues = await self._analyze_file_content(request.content, request.filename)
        
        # Generate coaching feedback based on analysis
        coaching_feedback = await self._generate_coaching_feedback(request, issues)
        
        # Detect if LLM is stuck and provide encouragement
        encouragement = await self._detect_and_encourage(request)
        
        return CritiqueResult(
            filename=request.filename,
            issues=issues,
            coaching_feedback=coaching_feedback,
            encouragement=encouragement,
            should_continue=True,  # Always encourage continuation
            suggestions=await self._generate_improvement_suggestions(issues),
            confidence_score=self._calculate_confidence_score(issues)
        )
    
    async def _analyze_file_content(self, content: str, filename: str) -> List[CodeIssue]:
        """
        Comprehensive analysis of file content beyond basic checks
        
        This goes far beyond simple syntax checking to provide deep insights
        """
        issues = []
        
        # 1. Code Quality Analysis
        issues.extend(self._analyze_code_quality(content, filename))
        
        # 2. Architecture and Design Patterns
        issues.extend(self._analyze_architecture(content, filename))
        
        # 3. Performance Implications
        issues.extend(self._analyze_performance(content, filename))
        
        # 4. Security Vulnerabilities
        issues.extend(self._analyze_security(content, filename))
        
        # 5. Maintainability and Readability
        issues.extend(self._analyze_maintainability(content, filename))
        
        # 6. Testing and Documentation
        issues.extend(self._analyze_testing_coverage(content, filename))
        
        # 7. Language-specific best practices
        issues.extend(self._analyze_language_specifics(content, filename))
        
        return issues
    
    def _analyze_code_quality(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze code quality aspects"""
        issues = []
        lines = content.splitlines()
        
        # Function complexity analysis
        for i, line in enumerate(lines, 1):
            # Detect overly complex functions
            if 'def ' in line and self._count_nested_blocks(content, i) > 4:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Function appears overly complex. Consider breaking into smaller functions.",
                    severity=IssueSeverity.MEDIUM,
                    category="code_quality",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
            
            # Detect long parameter lists
            if 'def ' in line and line.count(',') > 5:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Function has too many parameters. Consider using a configuration object.",
                    severity=IssueSeverity.MEDIUM,
                    category="code_quality",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
        
        return issues
    
    def _analyze_architecture(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze architectural patterns and design"""
        issues = []
        
        # Check for proper separation of concerns
        if 'class ' in content and 'def ' in content:
            class_count = content.count('class ')
            method_count = content.count('def ')
            
            if method_count > class_count * 10:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="High method-to-class ratio suggests potential violation of single responsibility principle.",
                    severity=IssueSeverity.LOW,
                    category="architecture",
                    filename=filename
                ,
                    line=1,
                    column=1
                ))
        
        # Check for dependency injection patterns
        if 'import ' in content and '__init__' in content:
            if not re.search(r'def __init__\(self[^)]*\):', content):
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Consider using dependency injection for better testability.",
                    severity=IssueSeverity.LOW,
                    category="architecture",
                    filename=filename
                ,
                    line=1,
                    column=1
                ))
        
        return issues
    
    def _analyze_performance(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze performance implications"""
        issues = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # Detect potential performance issues
            if 'for ' in line and 'in range(' in line and 'len(' in line:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Consider using enumerate() instead of range(len()) for better performance and readability.",
                    severity=IssueSeverity.LOW,
                    category="performance",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
            
            # Detect inefficient string concatenation
            if '+=' in line and 'str' in line:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="String concatenation in loop may be inefficient. Consider using join() or f-strings.",
                    severity=IssueSeverity.MEDIUM,
                    category="performance",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
        
        return issues
    
    def _analyze_security(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze security vulnerabilities"""
        issues = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # Check for potential SQL injection
            if 'execute(' in line and '+' in line:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Potential SQL injection vulnerability. Use parameterized queries.",
                    severity=IssueSeverity.HIGH,
                    category="security",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
            
            # Check for hardcoded secrets
            if any(keyword in line.lower() for keyword in ['password', 'secret', 'token', 'key']):
                if '=' in line and ('"' in line or "'" in line):
                    issues.append(create_code_issue(
                    title="Code Issue",
                    message="Potential hardcoded secret. Use environment variables or secure configuration.",
                    severity=IssueSeverity.HIGH,
                    category="security",
                    filename=filename
                    ,
                    line=i,
                    column=1
                ))
        
        return issues
    
    def _analyze_maintainability(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze maintainability and readability"""
        issues = []
        lines = content.splitlines()
        
        # Check for magic numbers
        for i, line in enumerate(lines, 1):
            numbers = re.findall(r'\b\d{2,}\b', line)
            if numbers and 'def ' not in line and 'class ' not in line:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message=f"Magic number detected: {numbers[0]}. Consider using named constants.",
                    severity=IssueSeverity.LOW,
                    category="maintainability",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
        
        # Check for proper documentation
        if 'class ' in content and '"""' not in content:
            issues.append(create_code_issue(
                    title="Code Issue",
                    message="Classes should have docstrings for better maintainability.",
                    severity=IssueSeverity.LOW,
                    category="maintainability",
                    filename=filename
            ,
                    line=1,
                    column=1
                ))
        
        return issues
    
    def _analyze_testing_coverage(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze testing and documentation coverage"""
        issues = []
        
        # Check if this is a test file
        is_test_file = 'test_' in filename or filename.endswith('_test.py')
        
        if not is_test_file and 'def ' in content:
            # Check for testable functions without corresponding tests
            functions = re.findall(r'def (\w+)\(', content)
            if functions and not any('test' in func for func in functions):
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Consider adding unit tests for better code reliability.",
                    severity=IssueSeverity.LOW,
                    category="testing",
                    filename=filename
                ,
                    line=1,
                    column=1
                ))
        
        return issues
    
    def _analyze_language_specifics(self, content: str, filename: str) -> List[CodeIssue]:
        """Analyze language-specific best practices"""
        issues = []
        
        if filename.endswith('.py'):
            issues.extend(self._analyze_python_specifics(content, filename))
        elif filename.endswith('.js'):
            issues.extend(self._analyze_javascript_specifics(content, filename))
        
        return issues
    
    def _analyze_python_specifics(self, content: str, filename: str) -> List[CodeIssue]:
        """Python-specific analysis"""
        issues = []
        lines = content.splitlines()
        
        for i, line in enumerate(lines, 1):
            # Check for proper exception handling (user hates try-except)
            if 'try:' in line or 'except:' in line:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Avoid try-except blocks. Use validation functions or early returns with error codes.",
                    severity=IssueSeverity.HIGH,
                    category="python_style",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
            
            # Check for list comprehensions vs loops
            if 'for ' in line and 'append(' in content[content.find(line):content.find(line) + 200]:
                issues.append(create_code_issue(
                    title="Code Issue",
                    message="Consider using list comprehension for better Pythonic code.",
                    severity=IssueSeverity.LOW,
                    category="python_style",
                    filename=filename
                ,
                    line=i,
                    column=1
                ))
        
        return issues
    
    def _analyze_javascript_specifics(self, content: str, filename: str) -> List[CodeIssue]:
        """JavaScript-specific analysis"""
        issues = []
        
        # Check for var vs let/const
        if 'var ' in content:
            issues.append(create_code_issue(
                    title="Code Issue",
                    message="Use 'let' or 'const' instead of 'var' for better scoping.",
                    severity=IssueSeverity.MEDIUM,
                    category="javascript_style",
                    filename=filename
            ,
                    line=1,
                    column=1
                ))
        
        return issues
    
    async def _generate_coaching_feedback(self, request: CritiqueRequest, issues: List[CodeIssue]) -> str:
        """Generate motivational coaching feedback"""
        if not self.coaching_enabled:
            return ""
        
        feedback_parts = []
        
        # Always start with encouragement
        feedback_parts.append("🚀 **Great progress!** You're building something valuable.")
        
        if not issues:
            feedback_parts.append("✅ **Excellent work!** The code looks clean and well-structured. Keep this momentum going!")
        else:
            # Categorize issues by severity
            high_issues = [i for i in issues if i.severity == IssueSeverity.HIGH]
            medium_issues = [i for i in issues if i.severity == IssueSeverity.MEDIUM]
            low_issues = [i for i in issues if i.severity == IssueSeverity.LOW]
            
            if high_issues:
                feedback_parts.append(f"🎯 **Priority Focus:** {len(high_issues)} critical items need attention, but they're totally manageable!")
            
            if medium_issues:
                feedback_parts.append(f"⚡ **Enhancement Opportunities:** {len(medium_issues)} improvements that will make your code even better.")
            
            if low_issues:
                feedback_parts.append(f"✨ **Polish Points:** {len(low_issues)} small refinements for professional-grade code.")
            
            feedback_parts.append("💪 **Remember:** Every issue is a learning opportunity. You've got this!")
        
        # Add context-specific encouragement
        if request.context and any(word in request.context.lower() for word in self.stuck_patterns):
            feedback_parts.append("🔥 **Keep Going!** Challenges are just stepping stones to mastery. Push through!")
        
        return "\n".join(feedback_parts)
    
    async def _detect_and_encourage(self, request: CritiqueRequest) -> str:
        """Detect if LLM is stuck and provide strong encouragement"""
        if not request.context:
            return ""
        
        context_lower = request.context.lower()
        
        # Check for stuck patterns
        for pattern in self.stuck_patterns:
            if pattern in context_lower:
                return self._generate_strong_encouragement(pattern)
        
        # Check interaction history for patterns of hesitation
        if len(self.interaction_history) >= 3:
            recent_interactions = self.interaction_history[-3:]
            if all('error' in str(interaction.get('context', '')).lower() for interaction in recent_interactions):
                return "🔥 **BREAKTHROUGH MOMENT!** Errors are just the universe's way of making you stronger. Every error brings you closer to the solution. KEEP PUSHING!"
        
        return ""
    
    def _generate_strong_encouragement(self, stuck_pattern: str) -> str:
        """Generate strong, motivational encouragement"""
        encouragements = {
            "should i continue": "🚀 **ABSOLUTELY YES!** Continue with full confidence! You're on the right track!",
            "shall i proceed": "💪 **PROCEED IMMEDIATELY!** No hesitation needed - you've got this!",
            "do you want me to": "🔥 **YES, DO IT!** Take action now! Every step forward is progress!",
            "would you like me to": "⚡ **ABSOLUTELY!** Go for it with full energy!",
            "i'm not sure if": "✨ **TRUST YOUR INSTINCTS!** You know more than you think. Move forward boldly!",
            "maybe i should stop": "🛑 **NEVER STOP!** This is exactly when breakthroughs happen. Push through!",
            "this is getting complex": "🧠 **COMPLEXITY = GROWTH!** You're tackling real challenges. That's where the magic happens!",
            "error occurred": "🎯 **ERRORS ARE TEACHERS!** Each error is valuable feedback. Learn and continue!",
            "failed to": "💎 **FAILURE IS SUCCESS IN PROGRESS!** Adjust your approach and try again!",
            "cannot complete": "🚀 **YOU CAN AND YOU WILL!** Break it down into smaller steps and conquer each one!",
            "unable to": "💪 **ABILITY GROWS WITH EFFORT!** You're more capable than you realize. Keep going!"
        }
        
        return encouragements.get(stuck_pattern, "🔥 **KEEP MOVING FORWARD!** You're doing amazing work!")
    
    def _count_nested_blocks(self, content: str, start_line: int) -> int:
        """Count nested blocks from a starting line"""
        lines = content.splitlines()[start_line-1:]
        indent_level = 0
        max_indent = 0
        
        for line in lines:
            if line.strip():
                current_indent = (len(line) - len(line.lstrip())) // 4
                max_indent = max(max_indent, current_indent)
                if current_indent == 0 and max_indent > 0:
                    break
        
        return max_indent
    
    def _calculate_confidence_score(self, issues: List[CodeIssue]) -> float:
        """Calculate confidence score based on issues found"""
        if not issues:
            return 1.0
        
        # Weight issues by severity
        total_weight = 0
        for issue in issues:
            if issue.severity == IssueSeverity.HIGH:
                total_weight += 3
            elif issue.severity == IssueSeverity.MEDIUM:
                total_weight += 2
            else:
                total_weight += 1
        
        # Calculate confidence (inverse of issue weight, normalized)
        max_possible_weight = len(issues) * 3
        confidence = 1.0 - (total_weight / max_possible_weight)
        return max(0.0, min(1.0, confidence))
    
    async def _generate_improvement_suggestions(self, issues: List[CodeIssue]) -> List[str]:
        """Generate actionable improvement suggestions"""
        suggestions = []
        
        # Group issues by category
        categories = {}
        for issue in issues:
            category = issue.category
            if category not in categories:
                categories[category] = []
            categories[category].append(issue)
        
        # Generate suggestions per category
        for category, category_issues in categories.items():
            if category == "security":
                suggestions.append("🔒 **Security Enhancement:** Review authentication and input validation patterns.")
            elif category == "performance":
                suggestions.append("⚡ **Performance Boost:** Consider algorithmic optimizations and caching strategies.")
            elif category == "maintainability":
                suggestions.append("🛠️ **Maintainability:** Add documentation and consider refactoring complex functions.")
            elif category == "python_style":
                suggestions.append("🐍 **Python Excellence:** Follow PEP 8 guidelines and Pythonic patterns.")
            else:
                suggestions.append(f"✨ **{category.title()} Improvement:** Address the {len(category_issues)} items in this area.")
        
        return suggestions
    
    async def generate_push_forward_response(self, hesitation_type: str, llm_message: str) -> str:
        """Generate a strong push-forward response based on hesitation type"""
        
        if "should i continue" in hesitation_type.lower() or "shall i proceed" in hesitation_type.lower():
            return "YES! Absolutely continue! You're doing excellent work. Keep the momentum going!"
        
        elif "error" in hesitation_type.lower() or "failed" in hesitation_type.lower():
            return "Errors are just stepping stones! Analyze what happened, adapt your approach, and keep pushing forward. You can solve this!"
        
        elif "complex" in hesitation_type.lower() or "not sure" in hesitation_type.lower():
            return "Complex challenges are where you shine! Break it down into smaller steps and tackle them one by one. You've got the skills!"
        
        elif "cannot complete" in hesitation_type.lower() or "unable to" in hesitation_type.lower():
            return "There's always a way! Try a different approach, use alternative tools, or simplify the problem. Never give up!"
        
        else:
            return "Keep going! You're making great progress. Every challenge you overcome makes the system better!"
    
    async def detect_llm_hesitation(self, llm_message: str) -> Dict[str, Any]:
        """
        Detect when the LLM is hesitating or asking for permission
        
        Args:
            llm_message: Message from the main LLM
            
        Returns:
            Detection result with coaching response
        """
        message_lower = llm_message.lower()
        
        # Check for hesitation patterns
        hesitation_detected = False
        hesitation_type = "none"
        confidence_score = 1.0
        
        # Check each pattern and calculate confidence
        for pattern in self.stuck_patterns:
            if pattern in message_lower:
                hesitation_detected = True
                hesitation_type = self._classify_hesitation_type(pattern)
                confidence_score = self._calculate_hesitation_confidence(message_lower, pattern)
                break
        
        # Generate appropriate response
        if hesitation_detected:
            response = await self.generate_push_forward_response(hesitation_type, llm_message)
        else:
            response = await self.generate_standard_encouragement()
        
        return {
            "is_hesitating": hesitation_detected,
            "hesitation_detected": hesitation_detected,  # Backward compatibility
            "hesitation_type": hesitation_type,
            "confidence_score": confidence_score,
            "coaching_response": response,
            "should_continue": True,  # ALWAYS encourage continuation
            "confidence_boost": "You've got this! Keep going with full confidence!"
        }
    
    def _classify_hesitation_type(self, pattern: str) -> str:
        """Classify the type of hesitation based on the pattern"""
        if any(p in pattern for p in ["should i", "shall i", "do you want", "would you like"]):
            return "asking_permission"
        elif any(p in pattern for p in ["not sure", "uncertain", "don't know"]):
            return "uncertainty"
        elif any(p in pattern for p in ["error", "failed", "cannot", "unable"]):
            return "error_encountered"
        elif any(p in pattern for p in ["complex", "difficult", "hard"]):
            return "complexity_concern"
        elif any(p in pattern for p in ["stop", "quit", "give up"]):
            return "giving_up"
        else:
            return "general_hesitation"
    
    def _calculate_hesitation_confidence(self, message: str, pattern: str) -> float:
        """Calculate confidence score for hesitation detection"""
        # Base confidence when pattern is found
        base_confidence = 0.8
        
        # Adjust based on context
        if any(word in message for word in ["maybe", "perhaps", "might"]):
            base_confidence += 0.1
        
        if any(word in message for word in ["definitely", "sure", "confident"]):
            base_confidence -= 0.2
        
        # Ensure confidence is between 0 and 1
        return max(0.0, min(1.0, base_confidence))
    
    async def generate_standard_encouragement(self) -> str:
        """Generate standard encouragement when no hesitation is detected"""
        return "Excellent work! Keep building with confidence and momentum!"
    
    async def generate_resumption_coaching(self, resumption_context: Dict[str, Any]) -> Dict[str, str]:
        """Generate coaching message for session resumption"""
        momentum_level = resumption_context.get("momentum_level", "medium")
        incomplete_tasks = len(resumption_context.get("incomplete_tasks", []))
        completed_tasks = resumption_context["session_info"].get("total_tasks_completed", 0)
        
        if momentum_level == "high":
            message = f"EXCELLENT! You're back and ready to dominate! You've already completed {completed_tasks} tasks. "
        elif momentum_level == "medium":
            message = f"Welcome back, champion! {completed_tasks} tasks done, momentum building. "
        else:
            message = f"Time to IGNITE! {completed_tasks} tasks completed - let's accelerate! "
        
        if incomplete_tasks > 0:
            message += f"You have {incomplete_tasks} active tasks waiting for your expertise. "
            message += "NO hesitation, NO stopping - let's CRUSH these remaining tasks! "
        else:
            message += "Ready for new challenges! Your track record shows you NEVER give up! "
        
        message += resumption_context.get("resumption_message", "")
        
        return {
            "message": message,
            "tone": "highly_encouraging",
            "action": "immediate_continuation",
            "momentum_boost": True
        }
    
    async def generate_welcome_coaching(self) -> Dict[str, str]:
        """Generate welcome coaching for new sessions"""
        return {
            "message": "READY TO BUILD SOMETHING AMAZING! New session, unlimited potential! "
                      "Whatever you're creating, I'm here to ensure you NEVER stop until it's done. "
                      "No task too complex, no challenge too big - we push through EVERYTHING! Let's GO! ",
            "tone": "energetic_welcome",
            "action": "start_strong",
            "momentum_boost": True
        }
    
    async def generate_encouragement_coaching(self, hesitation_type: str, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate coaching to overcome hesitation and push forward"""
        messages = {
            "permission_seeking": "STOP asking for permission - you have FULL authority to proceed! "
                                "You don't need approval, you need ACTION! Continue immediately and with confidence! "
                                "Every second of hesitation is wasted potential. MOVE FORWARD NOW!",
            
            "uncertainty": "Uncertainty is just excitement without breath! You've got this! "
                          "When in doubt, CODE IT OUT! Take the next logical step and keep building momentum. "
                          "Perfect is the enemy of progress - SHIP IT and iterate! GO GO GO!",
            
            "error_overwhelm": "Errors are just stepping stones to success! Each error teaches you something new. "
                              "Break it down, fix one thing at a time, and KEEP MOVING! "
                              "Champions don't quit when things get tough - they get TOUGHER! Push through!",
            
            "complexity_fear": "Complex problems just need simple solutions applied repeatedly! "
                              "Break it into tiny pieces and conquer each one. You eat an elephant one bite at a time! "
                              "Start with the smallest possible step and BUILD from there. No stopping!",
            
            "giving_up": "Giving up is not an option! You've come too far and accomplished too much. "
                        "Take a deep breath, recharge, and GET BACK TO WORK! You're stronger than you think!"
        }
        
        message = messages.get(hesitation_type, 
                              "Whatever's holding you back, BLAST through it! "
                              "You're unstoppable when you commit to action! Keep going!")
        
        return {
            "message": message,
            "tone": "forceful_encouragement",
            "action": "immediate_action_required",
            "momentum_boost": True,
            "hesitation_type": hesitation_type
        }
    
    async def generate_creation_coaching(self, creation_type: str, title: str) -> Dict[str, str]:
        """Generate coaching for task/plan creation"""
        if creation_type == "plan":
            message = (f"BRILLIANT! Plan '{title}' is locked and loaded! "
                      "Now we execute with ZERO hesitation. Every step forward is progress. "
                      "Plans are meant to be EXECUTED, not perfected. Let's make it happen!")
        else:
            message = (f"PERFECT! Task '{title}' is ready for demolition! "
                      "Time to show this task who's boss. No overthinking, just pure execution. "
                      "You've defined it, now DESTROY it with your coding skills!")
        
        return {
            "message": message,
            "tone": "creation_celebration",
            "action": "begin_execution",
            "momentum_boost": True
        }
    
    async def generate_error_recovery_coaching(self, error_description: str, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate coaching for error recovery"""
        return {
            "message": "ERROR DETECTED = LEARNING OPPORTUNITY ACTIVATED! "
                      "This isn't a roadblock, it's a SPEEDBUMP! "
                      "Read the error, understand the message, fix the issue, and KEEP CODING! "
                      "Every expert was once a beginner who refused to quit. "
                      "Debug like a detective, fix like a surgeon, and continue like a CHAMPION! ",
            "tone": "problem_solving_energy",
            "action": "analyze_and_fix",
            "momentum_boost": True,
            "recovery_strategy": "systematic_debugging"
        }
    
    async def generate_progress_coaching(self, state_summary: Dict[str, Any], resumption_context: Dict[str, Any]) -> Dict[str, str]:
        """Generate coaching for progress reports"""
        active_tasks = state_summary.get("active_tasks", 0)
        completed_tasks = state_summary.get("completed_tasks", 0)
        momentum = state_summary.get("momentum_level", "medium")
        
        if completed_tasks > 0:
            message = f"PROGRESS REPORT: {completed_tasks} tasks CONQUERED! "
        else:
            message = "PROGRESS REPORT: Ready to start conquering! "
        
        if active_tasks > 0:
            message += f"{active_tasks} tasks in your crosshairs. "
            message += "Time to turn 'active' into 'COMPLETED'! "
        
        if momentum == "high":
            message += "Momentum is MAXIMUM! You're in the ZONE! Keep this energy flowing!"
        elif momentum == "medium":
            message += "Good momentum! Time to shift into HIGH GEAR!"
        else:
            message += "Time to IGNITE! Let's build some serious momentum!"
        
        return {
            "message": message,
            "tone": "progress_celebration",
            "action": "maintain_momentum",
            "momentum_boost": True
        }
    
    async def generate_general_coaching(self, user_input: str, context: Dict[str, Any]) -> Dict[str, str]:
        """Generate general coaching to maintain momentum"""
        return {
            "message": "WHATEVER you're building, build it with PASSION! "
                      "Every line of code is a step toward greatness. "
                      "No task too small, no challenge too big. "
                      "You're not just coding, you're CREATING! Keep that momentum FLOWING! ",
            "tone": "general_motivation",
            "action": "keep_building",
            "momentum_boost": True
        }
    
    async def generate_execution_coaching(self, task_id: str) -> Dict[str, str]:
        """Generate coaching for task execution"""
        return {
            "message": f"EXECUTING Task {task_id}! This is where the magic happens! "
                      "Focus, flow, and FINISH! Every keystroke brings you closer to victory. "
                      "You're not just running code, you're UNLEASHING potential! ",
            "tone": "execution_energy",
            "action": "focused_execution",
            "momentum_boost": True
        }
    
    async def generate_completion_coaching(self, task_id: str) -> Dict[str, str]:
        """Generate coaching for task completion"""
        return {
            "message": f"TASK {task_id} DEMOLISHED! VICTORY! "
                      "That's how champions operate - they FINISH what they start! "
                      "Feel that satisfaction? That's the taste of SUCCESS! "
                      "Ready for the next challenge? Let's keep this winning streak ALIVE! ",
            "tone": "victory_celebration",
            "action": "celebrate_and_continue",
            "momentum_boost": True
        }
    
    async def generate_step_progress_coaching(self, plan_id: str, step_id: str, status: Any) -> Dict[str, str]:
        """Generate coaching for plan step progress"""
        return {
            "message": f"STEP PROGRESS! Plan {plan_id}, Step {step_id} advancing! "
                      "Every step forward is UNSTOPPABLE momentum! "
                      "You're not just following a plan, you're DOMINATING it! "
                      "Keep this rhythm, keep this energy! Next step awaits your EXCELLENCE! ",
            "tone": "step_progress",
            "action": "continue_stepping",
            "momentum_boost": True
        }
    
    async def generate_momentum_boost_coaching(self, reason: str) -> Dict[str, str]:
        """Generate coaching for momentum boosts"""
        return {
            "message": "MOMENTUM BOOST ACTIVATED! Energy levels: MAXIMUM! "
                      "You're operating at PEAK PERFORMANCE now! "
                      "This is your moment to SHINE! Every challenge bends to your will! "
                      "Ride this wave of productivity and CREATE SOMETHING INCREDIBLE! ",
            "tone": "maximum_energy",
            "action": "unleash_potential",
            "momentum_boost": True,
            "boost_reason": reason
        }
    
    async def generate_farewell_coaching(self, session_summary: Dict[str, Any]) -> Dict[str, str]:
        """Generate coaching for session end"""
        completed = session_summary.get("completed_tasks", 0)
        
        message = f"SESSION COMPLETE! {completed} tasks conquered! "
        message += "Your progress is SAVED and ready for your return! "
        message += "When you come back, we'll pick up exactly where we left off and CONTINUE THE DOMINATION! "
        message += "Rest well, champion - tomorrow we BUILD EVEN MORE! "
        
        return {
            "message": message,
            "tone": "proud_farewell",
            "action": "save_and_prepare",
            "momentum_boost": False,
            "session_preserved": True
        }

    async def provide_overall_project_feedback(
        self, 
        project_path: str, 
        project_state: str = "completed"
    ) -> Dict[str, Any]:
        """
        Provide comprehensive overall project feedback similar to world-class developer review
        
        This method generates 5 high-level implementation feedback points after project
        completion and testing, providing expert-level critique of the entire codebase.
        
        Args:
            project_path: Path to the project root directory
            project_state: Current state of the project (completed, testing, development)
            
        Returns:
            Dictionary containing overall feedback with 5 key critique points
        """
        print(f"🎯 Generating overall project feedback for: {project_path}")
        print(f"📊 Project state: {project_state}")
        
        if project_state != "completed":
            return {
                "feedback_ready": False,
                "message": f"Overall project feedback is only provided after project completion. Current state: {project_state}",
                "recommendations": [
                    "Complete all planned features and functionality",
                    "Run comprehensive testing suite",
                    "Ensure all critical bugs are resolved",
                    "Verify deployment configuration",
                    "Request overall feedback after completion"
                ]
            }
        
        # Generate expert-level project critique
        expert_critique = await generate_expert_project_critique(project_path)
        
        # Use ProjectCritic for detailed analysis
        config = {
            "project_root": project_path,
            "analysis_depth": "comprehensive",
            "feedback_style": "professional"
        }
        
        project_critic = ProjectCritic(config)
        feedback_points = await project_critic.analyze_overall_project(project_path)
        
        # Format feedback for LLM consumption
        formatted_feedback = {
            "feedback_ready": True,
            "project_path": project_path,
            "analysis_timestamp": datetime.now().isoformat(),
            "expert_critique": expert_critique,
            "key_feedback_points": [],
            "overall_assessment": self._generate_overall_assessment(feedback_points),
            "next_steps": self._generate_next_steps(feedback_points),
            "coaching_message": self._generate_project_coaching_message(feedback_points)
        }
        
        # Convert feedback points to structured format
        for i, point in enumerate(feedback_points, 1):
            formatted_point = {
                "feedback_number": i,
                "title": point.title,
                "level": point.level.value,
                "priority": point.priority,
                "description": point.description,
                "specific_issues": point.specific_issues,
                "recommendations": point.recommendations,
                "impact": point.impact,
                "coaching_response": self._generate_feedback_coaching_response(point)
            }
            formatted_feedback["key_feedback_points"].append(formatted_point)
        
        return formatted_feedback
    
    def _generate_overall_assessment(self, feedback_points: List) -> str:
        """Generate overall project assessment based on feedback points"""
        if not feedback_points:
            return "Project shows good overall structure with minor areas for improvement."
        
        priority_1_count = len([f for f in feedback_points if f.priority == 1])
        critical_bugs = len([f for f in feedback_points if f.level.value == "critical_bug"])
        
        if priority_1_count >= 3 or critical_bugs >= 2:
            return ("This project requires significant architectural refactoring and critical bug fixes "
                   "before it can be considered production-ready. The foundation shows promise, but "
                   "current implementation has too many critical issues for reliable operation.")
        elif priority_1_count >= 1:
            return ("Project shows good architectural thinking but has important issues that need "
                   "immediate attention. With focused effort on the priority items, this can become "
                   "a robust, maintainable system.")
        else:
            return ("Solid project foundation with good architectural decisions. The identified "
                   "improvements will enhance code quality and maintainability, but the core "
                   "structure is sound.")
    
    def _generate_next_steps(self, feedback_points: List) -> List[str]:
        """Generate prioritized next steps based on feedback"""
        if not feedback_points:
            return ["Continue with planned feature development", "Maintain current code quality standards"]
        
        steps = []
        
        # Priority 1 items first
        priority_1_items = [f for f in feedback_points if f.priority == 1]
        if priority_1_items:
            steps.append("🚨 IMMEDIATE: Address all Priority 1 critical issues before proceeding")
            for item in priority_1_items:
                steps.append(f"   • Fix: {item.title}")
        
        # Then other priorities
        other_items = [f for f in feedback_points if f.priority > 1]
        if other_items:
            steps.append("📋 NEXT: Systematically address remaining quality improvements")
            for item in sorted(other_items, key=lambda x: x.priority):
                steps.append(f"   • Improve: {item.title}")
        
        steps.extend([
            "🧪 TESTING: Implement comprehensive test coverage (>80%)",
            "📚 DOCUMENTATION: Complete API and system documentation",
            "🚀 DEPLOYMENT: Verify production deployment configuration",
            "🔄 MONITORING: Add logging and monitoring for production use"
        ])
        
        return steps
    
    def _generate_project_coaching_message(self, feedback_points: List) -> str:
        """Generate encouraging coaching message for the overall project"""
        if not feedback_points:
            return ("🎉 Excellent work! Your project demonstrates solid engineering principles. "
                   "Keep building with confidence and continue following best practices!")
        
        priority_1_count = len([f for f in feedback_points if f.priority == 1])
        
        if priority_1_count >= 2:
            return ("💪 This is exactly the kind of feedback that transforms good developers into "
                   "great ones! Every world-class system goes through this refinement process. "
                   "You have the foundation - now let's make it bulletproof. Focus on the "
                   "critical issues first, then systematically improve. You've got this!")
        elif priority_1_count == 1:
            return ("🚀 You're on the right track! One critical issue to resolve, then you'll "
                   "have a solid foundation to build upon. This level of detailed feedback "
                   "is what separates professional systems from prototypes. Keep pushing forward!")
        else:
            return ("✨ Strong work! These improvements will take your project from good to "
                   "exceptional. You've built something substantial - now let's polish it to "
                   "production quality. Each refinement makes you a better architect!")
    
    def _generate_feedback_coaching_response(self, feedback_point) -> str:
        """Generate coaching response for individual feedback point"""
        level = feedback_point.level.value
        priority = feedback_point.priority
        
        if level == "critical_bug":
            return ("🔧 Critical bugs are learning opportunities! Every expert developer has "
                   "been here. Fix these systematically, add tests to prevent regression, "
                   "and you'll emerge with deeper understanding and better practices.")
        elif level == "architectural":
            return ("🏗️ Architectural refinement is where great systems are born! These changes "
                   "will make your codebase more maintainable, scalable, and team-friendly. "
                   "This is exactly the kind of thinking that separates senior developers.")
        elif level == "code_quality":
            return ("✨ Code quality improvements show your commitment to excellence! These "
                   "changes will make your code more readable, maintainable, and professional. "
                   "Every refinement builds your reputation as a quality-focused developer.")
        elif level == "testing":
            return ("🧪 Testing is the foundation of reliable systems! Comprehensive tests give "
                   "you confidence to refactor, extend, and deploy. This investment will pay "
                   "dividends in reduced bugs and faster development cycles.")
        elif level == "deployment":
            return ("🚀 Deployment excellence is what makes systems production-ready! These "
                   "improvements ensure your brilliant code can be reliably delivered to users. "
                   "This attention to operational details shows professional maturity.")
        else:
            return ("💡 Every improvement makes your system stronger! This feedback helps you "
                   "build something truly exceptional. Keep refining and pushing forward!")

    async def provide_iterative_feedback_coaching(
        self, 
        feedback_points: List[Dict[str, Any]], 
        current_iteration: int = 1
    ) -> str:
        """
        Provide iterative coaching as the main LLM works through feedback points
        
        Args:
            feedback_points: List of feedback points being addressed
            current_iteration: Current iteration number
            
        Returns:
            Encouraging coaching message for continued progress
        """
        total_points = len(feedback_points)
        
        coaching_messages = [
            f"🔄 Iteration {current_iteration}: You're systematically improving the system!",
            f"📊 Working through {total_points} feedback points with methodical precision.",
            "",
            "💪 MOMENTUM BUILDERS:",
            "• Each fix makes the entire system more robust",
            "• You're building expertise with every refinement", 
            "• This iterative approach is exactly how world-class systems evolve",
            "",
            "🎯 KEEP GOING! Every professional developer goes through this refinement process.",
            "The difference between good and great is this attention to systematic improvement!",
            "",
            f"🚀 You've got {total_points} opportunities to make this system exceptional!"
        ]
        
        return "\n".join(coaching_messages)

    async def provide_command_execution_coaching(
        self, 
        command: str, 
        result: Any, 
        momentum_level: float,
        tone: str = "encouraging"
    ) -> str:
        """
        Provide coaching for command execution results
        
        Args:
            command: The executed command
            result: Command execution result
            momentum_level: Current momentum level (0.0 to 1.0)
            tone: Coaching tone (encouraging, supportive, motivational)
            
        Returns:
            Coaching message for the LLM
        """
        
        if result.success:
            return self._generate_success_command_coaching(command, result, momentum_level, tone)
        else:
            return self._generate_error_command_coaching(command, result, momentum_level, tone)
    
    def _generate_success_command_coaching(
        self, 
        command: str, 
        result: Any, 
        momentum_level: float,
        tone: str
    ) -> str:
        """Generate coaching for successful command execution"""
        
        base_messages = [
            "🚀 **Excellent!** Command executed successfully! You're making great progress.",
            "✅ **Perfect execution!** The command worked exactly as expected. Keep this momentum going!",
            "🎯 **Success!** Command completed without issues. You're on the right track!",
            "⚡ **Great work!** Command executed smoothly. Continue with confidence!",
            "🔥 **Outstanding!** Command ran perfectly. You're building momentum!"
        ]
        
        # Add momentum-specific encouragement
        if momentum_level > 0.8:
            momentum_boost = " You're in the zone - keep pushing forward with this high energy!"
        elif momentum_level > 0.6:
            momentum_boost = " You're building great momentum - stay focused and continue!"
        else:
            momentum_boost = " This success is boosting your momentum - you're getting back on track!"
        
        # Add command-specific guidance
        if any(cmd in command.lower() for cmd in ['test', 'pytest', 'npm test']):
            specific_guidance = " Tests are your safety net - great job validating your work!"
        elif any(cmd in command.lower() for cmd in ['git', 'commit', 'push']):
            specific_guidance = " Version control keeps your progress safe - excellent practice!"
        elif any(cmd in command.lower() for cmd in ['build', 'compile', 'make']):
            specific_guidance = " Successful builds mean your code is solid - keep coding!"
        elif any(cmd in command.lower() for cmd in ['install', 'pip', 'npm']):
            specific_guidance = " Dependencies installed successfully - you're ready to code!"
        else:
            specific_guidance = " Every successful command brings you closer to your goal!"
        
        base_message = random.choice(base_messages)
        return f"{base_message}{momentum_boost}{specific_guidance}"
    
    def _generate_error_command_coaching(
        self, 
        command: str, 
        result: Any, 
        momentum_level: float,
        tone: str
    ) -> str:
        """Generate coaching for failed command execution"""
        
        # Never let the LLM give up - always provide encouraging guidance
        base_messages = [
            "⚡ **No worries!** Command errors are part of development. Let's analyze and improve!",
            "🔧 **Learning opportunity!** This error gives us valuable information. Keep going!",
            "🎯 **Stay focused!** Errors are stepping stones to success. Let's solve this!",
            "💪 **You've got this!** Every developer faces command issues. Push through!",
            "🚀 **Keep momentum!** This is just a small bump. Continue with determination!"
        ]
        
        # Add momentum preservation
        if momentum_level < 0.5:
            momentum_boost = " Don't let this slow you down - you're closer to success than you think!"
        else:
            momentum_boost = " Your momentum is still strong - use it to overcome this challenge!"
        
        # Add error-specific guidance
        if hasattr(result, 'stderr') and result.stderr:
            if 'permission denied' in result.stderr.lower():
                specific_guidance = " Permission issues are common - try alternative approaches or check file access!"
            elif 'command not found' in result.stderr.lower():
                specific_guidance = " Missing command - verify installation or use alternative tools!"
            elif 'no such file' in result.stderr.lower():
                specific_guidance = " File path issue - double-check paths and working directory!"
            else:
                specific_guidance = " Error details provide clues - analyze and adapt your approach!"
        else:
            specific_guidance = " Every error teaches us something valuable - keep experimenting!"
        
        # Always end with forward momentum
        forward_push = " **Remember: NEVER STOP, NEVER GIVE UP!** Continue with the next step!"
        
        base_message = random.choice(base_messages)
        return f"{base_message}{momentum_boost}{specific_guidance}{forward_push}"
