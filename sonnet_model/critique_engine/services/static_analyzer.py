"""
Static Analyzer Service
Analyzes code statically for issues
"""
import os
import logging
import asyncio
import tempfile
from typing import Dict, List, Any, Optional, Set, Tuple

from shared.models import CritiqueRequest, CodeIssue, IssueSeverity, IssueCategory
from critique_engine.analyzers.python_analyzer import PythonAnalyzer


class StaticAnalyzer:
    """Static code analyzer service that coordinates language-specific analyzers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize language-specific analyzers
        self.python_analyzer = PythonAnalyzer(config.get("python_analyzer", {}))

        # Initialize analyzer registry
        self.analyzers = {
            "python": self.python_analyzer,
            "generic": self.python_analyzer  # Use Python analyzer as fallback for now
        }

        # Configuration
        self.enable_external_tools = config.get("enable_external_tools", True)
        self.max_file_size = config.get("max_file_size", 1024 * 1024)  # 1MB
        self.temp_dir = config.get("temp_dir", tempfile.gettempdir())
    
    async def analyze_code(self, code: str, language: str, categories: List[str]) -> List[CodeIssue]:
        """Analyze code using static analysis tools"""
        self.logger.info(f"Analyzing {language} code for categories: {categories}")

        issues = []

        # Validate input
        if not code.strip():
            return issues

        # Get appropriate analyzer
        analyzer = self.analyzers.get(language.lower(), self.analyzers["generic"])

        # Create temporary file for analysis
        with tempfile.NamedTemporaryFile(
            mode='w',
            suffix=f'.{self._get_file_extension(language)}',
            delete=False
        ) as temp_file:
            temp_file.write(code)
            temp_file_path = temp_file.name

        # Analyze the code
        if hasattr(analyzer, 'analyze_code'):
            # Use the analyzer's analyze_code method
            file_issues = await analyzer.analyze_code(
                code=code,
                filename=os.path.basename(temp_file_path),
                categories=set(categories) if categories else set()
            )
        else:
            # Fallback to basic analysis
            file_issues = await self._basic_analysis(code, language, categories)

        issues.extend(file_issues)

        # Clean up temporary file
        os.unlink(temp_file_path)

        self.logger.info(f"Static analysis complete, found {len(issues)} issues")
        return issues

    def _get_file_extension(self, language: str) -> str:
        """Get file extension for language"""
        extensions = {
            "python": "py",
            "javascript": "js",
            "typescript": "ts",
            "java": "java",
            "cpp": "cpp",
            "c": "c",
            "go": "go",
            "rust": "rs"
        }
        return extensions.get(language.lower(), "txt")

    async def _basic_analysis(self, code: str, language: str, categories: List[str]) -> List[CodeIssue]:
        """Basic analysis when no specific analyzer is available"""
        issues = []

        # Basic syntax checks
        if language.lower() == "python":
            compile(code, '<string>', 'exec')

        return issues

