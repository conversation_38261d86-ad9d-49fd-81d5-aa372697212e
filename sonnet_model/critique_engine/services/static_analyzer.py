"""
Static Analyzer Service
Analyzes code statically for issues
"""
import os
import logging
import asyncio
import tempfile
from typing import Dict, List, Any, Optional, Set, Tuple

from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory
from critique_engine.models.critique_result import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CodeIssue, IssueSeverity
from critique_engine.analyzers.python_analyzer import PythonAnal<PERSON>zer
from critique_engine.analyzers.javascript_analyzer import JavaScriptAnalyzer
from critique_engine.analyzers.generic_analyzer import GenericAnalyzer


class StaticAnalyzer:
    """Static code analyzer service that coordinates language-specific analyzers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize language-specific analyzers
        self.python_analyzer = PythonAnalyzer(config.get("python_analyzer", {}))
        
        # Configuration
        self.enable_external_tools = config.get("enable_external_tools", True)
        self.max_file_size = config.get("max_file_size", 1024 * 1024)  # 1MB
        self.temp_dir = config.get("temp_dir", tempfile.gettempdir())
    
    async def analyze_code(self, code: str, language: str, categories: List[str]) -> List[CodeIssue]:
        """Analyze code using static analysis tools"""
        self.logger.info(f"Analyzing {language} code for categories: {categories}")
        """
        Analyze code
        
        Args:
            request: Critique request
            
        Returns:
            Critique result
        """
        self.logger.info(f"Starting static analysis for task {request.task_id}")
        
        # Create result object
        result = CritiqueResult(
            task_id=request.task_id,
            request_id=request.request_id
        )
        
        # Create temporary directory for analysis
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write files to temporary directory
            for file in request.files:
                file_path = os.path.join(temp_dir, file.filename)
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(file.content)
            
            # Analyze each file
            analysis_tasks = []
            
            for file in request.files:
                task = self._analyze_file(file, temp_dir, request)
                analysis_tasks.append(task)
            
            # Wait for all analysis tasks to complete
            file_results = await asyncio.gather(*analysis_tasks)
            
            # Combine results
            for file_result in file_results:
                for issue in file_result:
                    result.add_issue(issue)
        
        # Calculate overall score
        result.overall_score = result.calculate_overall_score()
        
        self.logger.info(f"Static analysis complete for task {request.task_id}, found {len(result.issues)} issues")
        
        return result
    
    async def _analyze_file(
        self, 
        file: Any, 
        temp_dir: str, 
        request: CritiqueRequest
    ) -> List[CodeIssue]:
        """
        Analyze a single file
        
        Args:
            file: Code file
            temp_dir: Temporary directory
            request: Critique request
            
        Returns:
            List of code issues
        """
        file_path = os.path.join(temp_dir, file.filename)
        language = file.language.value
        
        # Select appropriate analyzer
        analyzer = self.analyzers.get(language)
        
        if not analyzer:
            # Fall back to generic analyzer
            analyzer = self.analyzers["generic"]
        
        # Get categories to analyze
        categories = request.categories
        
        if not categories:
            # If no categories specified, analyze all
            categories = set(CritiqueCategory)
        
        # Analyze file
        try:
            issues = await analyzer.analyze_file(
                file_path=file_path,
                filename=file.filename,
                categories=categories,
                level=request.level
            )
            
            return issues
        
        except Exception as e:
            self.logger.error(f"Error analyzing file {file.filename}: {e}", exc_info=True)
            
            # Create error issue
            error_issue = CodeIssue(
                id=f"error-{file.filename}",
                filename=file.filename,
                category=CritiqueCategory.SYNTAX,
                severity=IssueSeverity.HIGH,
                message=f"Error analyzing file: {str(e)}"
            )
            
            return [error_issue]
