"""
Critique Request Model
Defines the data structure for code critique requests
"""
from enum import Enum
from typing import Dict, List, Optional, Any, Set
from pydantic import BaseModel, Field

from code_generator.models.generation_request import ProgrammingLanguage, Framework


class CritiqueLevel(str, Enum):
    """Critique level enum"""
    BASIC = "basic"  # Basic syntax and style checks
    STANDARD = "standard"  # Standard checks including best practices
    COMPREHENSIVE = "comprehensive"  # Comprehensive checks including performance and security


class CritiqueCategory(str, Enum):
    """Critique category enum"""
    SYNTAX = "syntax"  # Syntax errors and warnings
    STYLE = "style"  # Style issues and formatting
    LOGIC = "logic"  # Logic errors and bugs
    PERFORMANCE = "performance"  # Performance issues
    SECURITY = "security"  # Security vulnerabilities
    MAINTAINABILITY = "maintainability"  # Code maintainability issues
    BEST_PRACTICES = "best_practices"  # Best practices violations
    DOCUMENTATION = "documentation"  # Documentation issues
    TESTS = "tests"  # Test coverage and quality
    QUALITY = "quality"  # General code quality issues


class CodeFile(BaseModel):
    """
    Code file model
    
    Represents a code file to critique
    """
    filename: str = Field(..., description="Filename")
    content: str = Field(..., description="File content")
    language: ProgrammingLanguage = Field(
        default=ProgrammingLanguage.PYTHON,
        description="Programming language"
    )


class CritiqueRequest(BaseModel):
    """
    Code critique request model
    
    Represents a request to critique code
    """
    task_id: str = Field(..., description="Task ID")
    request_id: str = Field(..., description="Request ID")
    files: List[CodeFile] = Field(
        default_factory=list,
        description="List of code files to critique"
    )
    language: ProgrammingLanguage = Field(
        default=ProgrammingLanguage.PYTHON,
        description="Primary programming language"
    )
    framework: Optional[Framework] = Field(
        default=None,
        description="Framework used"
    )
    level: CritiqueLevel = Field(
        default=CritiqueLevel.STANDARD,
        description="Critique level"
    )
    categories: Set[CritiqueCategory] = Field(
        default_factory=set,
        description="Critique categories to include"
    )
    requirements: List[str] = Field(
        default_factory=list,
        description="List of requirements to check against"
    )
    context: Optional[str] = Field(
        default=None,
        description="Additional context for critique"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    
    def get_file_by_name(self, filename: str) -> Optional[CodeFile]:
        """
        Get file by name
        
        Args:
            filename: Filename to find
            
        Returns:
            Code file if found, None otherwise
        """
        for file in self.files:
            if file.filename == filename:
                return file
        
        return None
    
    def get_file_by_extension(self, extension: str) -> Optional[CodeFile]:
        """
        Get first file with the given extension
        
        Args:
            extension: File extension to find (without dot)
            
        Returns:
            Code file if found, None otherwise
        """
        for file in self.files:
            if file.filename.endswith(f".{extension}"):
                return file
        
        return None
    
    def should_check_category(self, category: CritiqueCategory) -> bool:
        """
        Check if category should be checked
        
        Args:
            category: Category to check
            
        Returns:
            True if category should be checked, False otherwise
        """
        # If no categories specified, check all
        if not self.categories:
            return True
        
        return category in self.categories
