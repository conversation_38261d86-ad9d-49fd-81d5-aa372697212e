"""
Analysis Report Model
Defines the data structure for code analysis reports
"""
from enum import Enum
from typing import Dict, List, Optional, Any, Set
from pydantic import BaseModel, Field

from critique_engine.models.critique_result import CritiqueResult, IssueSeverity
from critique_engine.models.critique_request import CritiqueCategory


class RecommendationType(str, Enum):
    """Recommendation type enum"""
    REFACTORING = "refactoring"  # Code refactoring
    OPTIMIZATION = "optimization"  # Performance optimization
    SECURITY = "security"  # Security improvement
    STYLE = "style"  # Style improvement
    TESTING = "testing"  # Testing improvement
    DOCUMENTATION = "documentation"  # Documentation improvement
    ARCHITECTURE = "architecture"  # Architecture improvement
    DEPENDENCY = "dependency"  # Dependency management


class CodeMetric(BaseModel):
    """
    Code metric model
    
    Represents a code quality metric
    """
    name: str = Field(..., description="Metric name")
    value: Any = Field(..., description="Metric value")
    description: str = Field(..., description="Metric description")
    threshold: Optional[Any] = Field(
        default=None,
        description="Metric threshold"
    )
    is_good: Optional[bool] = Field(
        default=None,
        description="Whether the metric value is good"
    )
    
    def is_within_threshold(self) -> bool:
        """
        Check if metric is within threshold
        
        Returns:
            True if metric is within threshold, False otherwise
        """
        if self.threshold is None or self.is_good is None:
            return True
        
        if isinstance(self.value, (int, float)) and isinstance(self.threshold, (int, float)):
            return self.value <= self.threshold if self.is_good else self.value >= self.threshold
        
        return True


class Recommendation(BaseModel):
    """
    Recommendation model
    
    Represents a recommendation for code improvement
    """
    id: str = Field(..., description="Recommendation ID")
    type: RecommendationType = Field(..., description="Recommendation type")
    title: str = Field(..., description="Recommendation title")
    description: str = Field(..., description="Recommendation description")
    priority: int = Field(
        default=3,
        description="Recommendation priority (1-5, lower is higher priority)"
    )
    effort: int = Field(
        default=3,
        description="Estimated effort to implement (1-5, lower is less effort)"
    )
    related_issues: List[str] = Field(
        default_factory=list,
        description="List of related issue IDs"
    )
    code_examples: List[str] = Field(
        default_factory=list,
        description="List of code examples"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )


class FileAnalysis(BaseModel):
    """
    File analysis model
    
    Represents an analysis of a file
    """
    filename: str = Field(..., description="Filename")
    metrics: Dict[str, CodeMetric] = Field(
        default_factory=dict,
        description="Code metrics"
    )
    recommendations: List[Recommendation] = Field(
        default_factory=list,
        description="List of recommendations"
    )
    summary: str = Field(
        default="",
        description="Analysis summary"
    )
    
    def add_metric(self, metric: CodeMetric) -> None:
        """
        Add metric to analysis
        
        Args:
            metric: Metric to add
        """
        self.metrics[metric.name] = metric
    
    def add_recommendation(self, recommendation: Recommendation) -> None:
        """
        Add recommendation to analysis
        
        Args:
            recommendation: Recommendation to add
        """
        self.recommendations.append(recommendation)


class AnalysisReport(BaseModel):
    """
    Analysis report model
    
    Represents a comprehensive code analysis report
    """
    task_id: str = Field(..., description="Task ID")
    request_id: str = Field(..., description="Request ID")
    critique_result: CritiqueResult = Field(
        ...,
        description="Critique result"
    )
    file_analyses: Dict[str, FileAnalysis] = Field(
        default_factory=dict,
        description="File analyses"
    )
    overall_metrics: Dict[str, CodeMetric] = Field(
        default_factory=dict,
        description="Overall code metrics"
    )
    overall_recommendations: List[Recommendation] = Field(
        default_factory=list,
        description="Overall recommendations"
    )
    summary: str = Field(
        default="",
        description="Report summary"
    )
    execution_time_ms: int = Field(
        default=0,
        description="Execution time in milliseconds"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    
    def add_file_analysis(self, analysis: FileAnalysis) -> None:
        """
        Add file analysis to report
        
        Args:
            analysis: File analysis to add
        """
        self.file_analyses[analysis.filename] = analysis
    
    def add_overall_metric(self, metric: CodeMetric) -> None:
        """
        Add overall metric to report
        
        Args:
            metric: Metric to add
        """
        self.overall_metrics[metric.name] = metric
    
    def add_overall_recommendation(self, recommendation: Recommendation) -> None:
        """
        Add overall recommendation to report
        
        Args:
            recommendation: Recommendation to add
        """
        self.overall_recommendations.append(recommendation)
    
    def get_top_recommendations(self, limit: int = 5) -> List[Recommendation]:
        """
        Get top recommendations
        
        Args:
            limit: Maximum number of recommendations to return
            
        Returns:
            List of top recommendations
        """
        # Sort by priority (lower is higher priority)
        sorted_recommendations = sorted(
            self.overall_recommendations,
            key=lambda r: r.priority
        )
        
        return sorted_recommendations[:limit]
    
    def get_critical_issues_count(self) -> int:
        """
        Get number of critical issues
        
        Returns:
            Number of critical issues
        """
        return len(self.critique_result.get_issues_by_severity(IssueSeverity.CRITICAL))
    
    def get_issues_by_category(self, category: CritiqueCategory) -> int:
        """
        Get number of issues by category
        
        Args:
            category: Issue category
            
        Returns:
            Number of issues in the given category
        """
        return len(self.critique_result.get_issues_by_category(category))
    
    def generate_summary(self) -> str:
        """
        Generate report summary
        
        Returns:
            Report summary
        """
        total_issues = len(self.critique_result.issues)
        critical_issues = self.get_critical_issues_count()
        high_issues = len(self.critique_result.get_issues_by_severity(IssueSeverity.HIGH))
        
        summary = f"Analysis found {total_issues} issues "
        
        if critical_issues > 0:
            summary += f"({critical_issues} critical, {high_issues} high severity). "
        elif high_issues > 0:
            summary += f"({high_issues} high severity). "
        else:
            summary += "(no critical or high severity issues). "
        
        if self.critique_result.overall_score is not None:
            summary += f"Overall code quality score: {self.critique_result.overall_score:.1f}/100. "
        
        if self.overall_recommendations:
            top_recommendations = self.get_top_recommendations(3)
            summary += "Top recommendations: "
            summary += ", ".join(rec.title for rec in top_recommendations)
        
        return summary
