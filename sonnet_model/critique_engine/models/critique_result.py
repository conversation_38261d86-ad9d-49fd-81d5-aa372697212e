"""
Critique Result Model
Defines the data structure for code critique results
"""
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

from .code_issue import CodeIssue, IssueSeverity, IssueCategory


class CritiqueCategory(str, Enum):
    """Critique categories"""
    QUALITY = "quality"
    SECURITY = "security"
    PERFORMANCE = "performance"
    STYLE = "style"
    COMPLEXITY = "complexity"


class FileSummary(BaseModel):
    """
    File summary model
    
    Represents a summary of issues in a file
    """
    filename: str = Field(..., description="Filename")
    issues_count: int = Field(
        default=0,
        description="Number of issues"
    )
    issues_by_severity: Dict[IssueSeverity, int] = Field(
        default_factory=dict,
        description="Issues count by severity"
    )
    issues_by_category: Dict[IssueCategory, int] = Field(
        default_factory=dict,
        description="Issues count by category"
    )
    
    def add_issue(self, issue: CodeIssue) -> None:
        """
        Add issue to summary
        
        Args:
            issue: Issue to add
        """
        self.issues_count += 1
        
        # Update severity count
        if issue.severity not in self.issues_by_severity:
            self.issues_by_severity[issue.severity] = 0
        self.issues_by_severity[issue.severity] += 1
        
        # Update category count
        if issue.category not in self.issues_by_category:
            self.issues_by_category[issue.category] = 0
        self.issues_by_category[issue.category] += 1


class CritiqueResult(BaseModel):
    """Code critique result model
    
    Represents the result of a code critique
    """
    task_id: str = Field(..., description="Task ID")
    request_id: str = Field(..., description="Request ID")
    issues: List[CodeIssue] = Field(
        default_factory=list,
        description="List of issues found"
    )
    summary: Dict[str, FileSummary] = Field(
        default_factory=dict,
        description="Summary of issues by file"
    )
    overall_score: Optional[float] = Field(
        default=None,
        description="Overall code quality score (0-100)"
    )
    execution_time_ms: int = Field(
        default=0,
        description="Execution time in milliseconds"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )
    
    def add_issue(self, issue: CodeIssue) -> None:
        """
        Add issue to result
        
        Args:
            issue: Issue to add
        """
        self.issues.append(issue)
        
        # Update file summary
        filename = issue.file_path or "unknown"
        if filename not in self.summary:
            self.summary[filename] = FileSummary(filename=filename)
        
        self.summary[filename].add_issue(issue)
    
    def get_issues_by_severity(self, severity: IssueSeverity) -> List[CodeIssue]:
        """
        Get issues by severity
        
        Args:
            severity: Issue severity
            
        Returns:
            List of issues with the given severity
        """
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_issues_by_category(self, category: IssueCategory) -> List[CodeIssue]:
        """
        Get issues by category
        
        Args:
            category: Issue category
            
        Returns:
            List of issues with the given category
        """
        return [issue for issue in self.issues if issue.category == category]
    
    def get_issues_by_file(self, filename: str) -> List[CodeIssue]:
        """
        Get issues by file
        
        Args:
            filename: Filename
            
        Returns:
            List of issues in the given file
        """
        return [issue for issue in self.issues if issue.file_path == filename]
    
    def has_critical_issues(self) -> bool:
        """
        Check if there are critical issues
        
        Returns:
            True if there are critical issues, False otherwise
        """
        return any(issue.severity == IssueSeverity.CRITICAL for issue in self.issues)
    
    def calculate_overall_score(self) -> float:
        """
        Calculate overall code quality score
        
        Returns:
            Overall code quality score (0-100)
        """
        if not self.issues:
            return 100.0
        
        # Weight issues by severity
        severity_weights = {
            IssueSeverity.CRITICAL: 20,
            IssueSeverity.ERROR: 15,
            IssueSeverity.HIGH: 10,
            IssueSeverity.WARNING: 5,
            IssueSeverity.MEDIUM: 3,
            IssueSeverity.LOW: 1,
            IssueSeverity.INFO: 0.5
        }
        
        total_penalty = sum(
            severity_weights.get(issue.severity, 1) 
            for issue in self.issues
        )
        
        # Calculate score (max penalty of 100 points)
        score = max(0, 100 - min(total_penalty, 100))
        
        self.overall_score = score
        return score
