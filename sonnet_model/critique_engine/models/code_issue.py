"""
Code Issue Model - Represents issues found during code analysis
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class IssueSeverity(str, Enum):
    """Issue severity levels"""
    CRITICAL = "critical"
    ERROR = "error"
    HIGH = "high"
    WARNING = "warning"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class IssueCategory(str, Enum):
    """Issue categories"""
    SYNTAX = "syntax"
    STYLE = "style"
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"
    CORRECTNESS = "correctness"
    COMPLEXITY = "complexity"
    COMPATIBILITY = "compatibility"
    OPENCL = "opencl"
    OTHER = "other"


class CodeIssue(BaseModel):
    """Code issue model representing a problem found during analysis"""
    
    id: str = Field(..., description="Unique issue identifier")
    title: str = Field(..., description="Short issue title")
    description: str = Field(..., description="Detailed issue description")
    
    severity: IssueSeverity = Field(default=IssueSeverity.MEDIUM, description="Issue severity")
    category: IssueCategory = Field(default=IssueCategory.OTHER, description="Issue category")
    
    # Location information
    file_path: Optional[str] = Field(default=None, description="File path")
    line_start: Optional[int] = Field(default=None, description="Starting line number")
    line_end: Optional[int] = Field(default=None, description="Ending line number")
    column_start: Optional[int] = Field(default=None, description="Starting column number")
    column_end: Optional[int] = Field(default=None, description="Ending column number")
    
    # Code context
    code_snippet: Optional[str] = Field(default=None, description="Relevant code snippet")
    
    # Resolution information
    fix_suggestions: List[str] = Field(default_factory=list, description="Suggested fixes")
    auto_fixable: bool = Field(default=False, description="Whether issue can be auto-fixed")
    
    # Metadata
    rule_id: Optional[str] = Field(default=None, description="ID of the rule that detected the issue")
    tool_source: Optional[str] = Field(default=None, description="Source tool (e.g., pylint, mypy)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    def get_location_str(self) -> str:
        """Get string representation of issue location"""
        if not self.file_path:
            return "Unknown location"
        
        location = self.file_path
        if self.line_start:
            location += f":{self.line_start}"
            if self.column_start:
                location += f":{self.column_start}"
        
        return location
    
    def get_severity_value(self) -> int:
        """Get numeric value for severity (for sorting/filtering)"""
        severity_values = {
            IssueSeverity.CRITICAL: 100,
            IssueSeverity.ERROR: 90,
            IssueSeverity.HIGH: 75,
            IssueSeverity.WARNING: 60,
            IssueSeverity.MEDIUM: 50,
            IssueSeverity.LOW: 25,
            IssueSeverity.INFO: 10
        }
        return severity_values.get(self.severity, 0)
    
    def has_fix_suggestion(self) -> bool:
        """Check if issue has fix suggestions"""
        return len(self.fix_suggestions) > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CodeIssue":
        """Create from dictionary"""
        return cls(**data)
    
    @classmethod
    def from_pylint(cls, pylint_issue: Dict[str, Any]) -> "CodeIssue":
        """Create from pylint issue"""
        severity_map = {
            "error": IssueSeverity.ERROR,
            "warning": IssueSeverity.WARNING,
            "convention": IssueSeverity.LOW,
            "refactor": IssueSeverity.LOW,
            "info": IssueSeverity.INFO
        }
        
        category_map = {
            "E": IssueCategory.CORRECTNESS,
            "W": IssueCategory.MAINTAINABILITY,
            "C": IssueCategory.STYLE,
            "R": IssueCategory.MAINTAINABILITY,
            "F": IssueCategory.CORRECTNESS
        }
        
        # Extract category from message ID (e.g., "C0103")
        category_code = pylint_issue.get("symbol", "")[0] if pylint_issue.get("symbol") else ""
        
        return cls(
            id=f"pylint-{pylint_issue.get('message-id', '')}",
            title=pylint_issue.get("symbol", "Unknown pylint issue"),
            description=pylint_issue.get("message", ""),
            severity=severity_map.get(pylint_issue.get("type", ""), IssueSeverity.MEDIUM),
            category=category_map.get(category_code, IssueCategory.OTHER),
            file_path=pylint_issue.get("path", None),
            line_start=pylint_issue.get("line", None),
            column_start=pylint_issue.get("column", None),
            tool_source="pylint",
            rule_id=pylint_issue.get("message-id", None),
            metadata={
                "pylint_symbol": pylint_issue.get("symbol", ""),
                "pylint_type": pylint_issue.get("type", "")
            }
        )
    
    @classmethod
    def from_mypy(cls, mypy_issue: str, file_path: str) -> "CodeIssue":
        """Create from mypy issue"""
        # Parse mypy output format: file:line: error: message
        parts = mypy_issue.split(":", 3)
        if len(parts) < 4:
            return cls(
                id=f"mypy-unknown",
                title="Mypy issue",
                description=mypy_issue,
                severity=IssueSeverity.MEDIUM,
                category=IssueCategory.CORRECTNESS,
                file_path=file_path,
                tool_source="mypy"
            )
        
        try:
            line_num = int(parts[1].strip())
        except ValueError:
            line_num = None
        
        error_type = parts[2].strip()
        message = parts[3].strip()
        
        return cls(
            id=f"mypy-{hash(message) % 10000}",
            title=f"Type error: {message[:30]}..." if len(message) > 30 else f"Type error: {message}",
            description=message,
            severity=IssueSeverity.ERROR if "error" in error_type else IssueSeverity.WARNING,
            category=IssueCategory.CORRECTNESS,
            file_path=file_path,
            line_start=line_num,
            tool_source="mypy",
            metadata={
                "error_type": error_type
            }
        )
    
    @classmethod
    def from_bandit(cls, bandit_issue: Dict[str, Any]) -> "CodeIssue":
        """Create from bandit issue"""
        severity_map = {
            "HIGH": IssueSeverity.HIGH,
            "MEDIUM": IssueSeverity.MEDIUM,
            "LOW": IssueSeverity.LOW
        }
        
        confidence_map = {
            "HIGH": "High confidence",
            "MEDIUM": "Medium confidence",
            "LOW": "Low confidence"
        }
        
        issue_data = bandit_issue.get("issue_data", {})
        location = bandit_issue.get("location", {})
        
        return cls(
            id=f"bandit-{issue_data.get('test_id', '')}",
            title=issue_data.get("test_name", "Security issue"),
            description=issue_data.get("description", ""),
            severity=severity_map.get(issue_data.get("severity", ""), IssueSeverity.MEDIUM),
            category=IssueCategory.SECURITY,
            file_path=location.get("path", None),
            line_start=location.get("line", None),
            code_snippet=bandit_issue.get("code", None),
            tool_source="bandit",
            rule_id=issue_data.get("test_id", None),
            metadata={
                "confidence": confidence_map.get(issue_data.get("confidence", ""), "Unknown confidence"),
                "cwe": issue_data.get("cwe", "")
            }
        )
