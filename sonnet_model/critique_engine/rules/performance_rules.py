"""
Performance Rules - Performance optimization rules
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class PerformanceImpact(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class PerformanceRule:
    """Represents a performance rule"""
    id: str
    name: str
    description: str
    impact: PerformanceImpact
    pattern: Optional[str] = None
    optimization: Optional[str] = None
    complexity_class: Optional[str] = None


class PerformanceRules:
    """Collection of performance rules"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize performance rules"""
        self.config = config
        self.rules = self._initialize_rules()
    
    def _initialize_rules(self) -> List[PerformanceRule]:
        """Initialize all performance rules"""
        return [
            # Algorithmic complexity
            PerformanceRule(
                id="nested-loops",
                name="Nested Loops",
                description="Nested loops can lead to O(n²) or worse complexity",
                impact=PerformanceImpact.HIGH,
                pattern=r'for\s+.*:\s*\n.*for\s+.*:',
                optimization="Consider using more efficient algorithms or data structures",
                complexity_class="O(n²)"
            ),
            
            PerformanceRule(
                id="list-comprehension",
                name="List Comprehension Opportunity",
                description="Loop can be replaced with list comprehension",
                impact=PerformanceImpact.MEDIUM,
                pattern=r'for\s+\w+\s+in\s+.*:\s*\n\s*\w+\.append\(',
                optimization="Use list comprehension for better performance"
            ),
            
            PerformanceRule(
                id="string-concatenation-loop",
                name="String Concatenation in Loop",
                description="String concatenation in loops is inefficient",
                impact=PerformanceImpact.HIGH,
                pattern=r'for\s+.*:\s*\n.*\w+\s*\+=\s*.*str',
                optimization="Use join() or StringIO for string concatenation",
                complexity_class="O(n²)"
            ),
            
            # Data structure usage
            PerformanceRule(
                id="list-membership-check",
                name="List Membership Check",
                description="Membership check in list is O(n)",
                impact=PerformanceImpact.MEDIUM,
                pattern=r'\w+\s+in\s+\[.*\]|\w+\s+in\s+list\(',
                optimization="Use set for O(1) membership checks",
                complexity_class="O(n)"
            ),
            
            PerformanceRule(
                id="dict-get-default",
                name="Dictionary Get with Default",
                description="Use dict.get() instead of key checking",
                impact=PerformanceImpact.LOW,
                pattern=r'if\s+\w+\s+in\s+\w+:\s*\n.*=\s*\w+\[\w+\]\s*\n.*else:\s*\n.*=',
                optimization="Use dict.get(key, default) for cleaner and faster code"
            ),
            
            # I/O operations
            PerformanceRule(
                id="file-read-line-by-line",
                name="Inefficient File Reading",
                description="Reading file line by line in loop",
                impact=PerformanceImpact.MEDIUM,
                pattern=r'for\s+line\s+in\s+open\(',
                optimization="Use with statement and consider reading in chunks"
            ),
            
            PerformanceRule(
                id="database-query-loop",
                name="Database Query in Loop",
                description="Database queries inside loops (N+1 problem)",
                impact=PerformanceImpact.CRITICAL,
                pattern=r'for\s+.*:\s*\n.*\.execute\(|for\s+.*:\s*\n.*\.query\(',
                optimization="Use batch queries or joins to avoid N+1 problem"
            ),
            
            # Memory usage
            PerformanceRule(
                id="large-list-creation",
                name="Large List Creation",
                description="Creating large lists in memory",
                impact=PerformanceImpact.HIGH,
                pattern=r'range\(\d{4,}\)|list\(range\(\d{4,}\)\)',
                optimization="Use generators or process data in chunks"
            ),
            
            PerformanceRule(
                id="global-variable-access",
                name="Global Variable Access",
                description="Frequent global variable access",
                impact=PerformanceImpact.LOW,
                pattern=r'global\s+\w+',
                optimization="Use local variables or pass as parameters"
            ),
            
            # Function calls
            PerformanceRule(
                id="function-call-loop",
                name="Function Call in Loop",
                description="Expensive function calls inside loops",
                impact=PerformanceImpact.MEDIUM,
                pattern=r'for\s+.*:\s*\n.*len\(|for\s+.*:\s*\n.*\.upper\(\)|for\s+.*:\s*\n.*\.lower\(\)',
                optimization="Cache function results outside the loop"
            ),
            
            # Regular expressions
            PerformanceRule(
                id="regex-compilation-loop",
                name="Regex Compilation in Loop",
                description="Compiling regex patterns inside loops",
                impact=PerformanceImpact.MEDIUM,
                pattern=r'for\s+.*:\s*\n.*re\.compile\(',
                optimization="Compile regex patterns outside loops"
            ),
            
            # Data processing
            PerformanceRule(
                id="pandas-iterrows",
                name="Pandas iterrows Usage",
                description="Using iterrows() is slow for pandas operations",
                impact=PerformanceImpact.HIGH,
                pattern=r'\.iterrows\(\)',
                optimization="Use vectorized operations or apply() instead"
            ),
            
            PerformanceRule(
                id="numpy-loops",
                name="NumPy Loop Operations",
                description="Using loops instead of vectorized NumPy operations",
                impact=PerformanceImpact.HIGH,
                pattern=r'for\s+.*:\s*\n.*np\.|for\s+.*:\s*\n.*numpy\.',
                optimization="Use vectorized NumPy operations"
            )
        ]
    
    def get_rules_by_impact(self, impact: PerformanceImpact) -> List[PerformanceRule]:
        """Get rules by performance impact"""
        return [rule for rule in self.rules if rule.impact == impact]
    
    def get_critical_rules(self) -> List[PerformanceRule]:
        """Get critical performance rules"""
        return self.get_rules_by_impact(PerformanceImpact.CRITICAL)
    
    def get_rule_by_id(self, rule_id: str) -> Optional[PerformanceRule]:
        """Get rule by ID"""
        for rule in self.rules:
            if rule.id == rule_id:
                return rule
        return None
    
    def analyze_performance(self, content: str, language: str = "python") -> List[Dict[str, Any]]:
        """Analyze code for performance issues"""
        issues = []
        lines = content.split('\n')
        
        for rule in self.rules:
            if rule.pattern:
                # Use multiline matching for patterns that span multiple lines
                if '\n' in rule.pattern:
                    matches = re.finditer(rule.pattern, content, re.MULTILINE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        issues.append({
                            "rule_id": rule.id,
                            "name": rule.name,
                            "description": rule.description,
                            "impact": rule.impact.value,
                            "line": line_num,
                            "optimization": rule.optimization,
                            "complexity_class": rule.complexity_class
                        })
                else:
                    for i, line in enumerate(lines):
                        line_num = i + 1
                        if re.search(rule.pattern, line):
                            issues.append({
                                "rule_id": rule.id,
                                "name": rule.name,
                                "description": rule.description,
                                "impact": rule.impact.value,
                                "line": line_num,
                                "code": line.strip(),
                                "optimization": rule.optimization,
                                "complexity_class": rule.complexity_class
                            })
        
        return issues
    
    def check_algorithmic_complexity(self, content: str) -> List[Dict[str, Any]]:
        """Check for algorithmic complexity issues"""
        issues = []
        lines = content.split('\n')
        
        # Track nested loops
        loop_stack = []
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Detect loop start
            if re.search(r'for\s+\w+\s+in\s+|while\s+', stripped):
                loop_stack.append({
                    "line": line_num,
                    "type": "for" if "for" in stripped else "while",
                    "indentation": len(line) - len(line.lstrip())
                })
            
            # Detect loop end (simplified)
            elif stripped == "" or (loop_stack and len(line) - len(line.lstrip()) <= loop_stack[-1]["indentation"]):
                if loop_stack:
                    loop = loop_stack.pop()
                    
                    # Check if we have nested loops
                    if len(loop_stack) > 0:
                        nesting_level = len(loop_stack) + 1
                        complexity = f"O(n^{nesting_level})"
                        
                        issues.append({
                            "rule_id": "nested-loops",
                            "line": loop["line"],
                            "message": f"Nested loop detected (nesting level: {nesting_level})",
                            "complexity": complexity,
                            "suggestion": "Consider optimizing algorithm or using different data structures"
                        })
        
        return issues
    
    def check_data_structure_usage(self, content: str) -> List[Dict[str, Any]]:
        """Check for inefficient data structure usage"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for list membership tests
            if re.search(r'\w+\s+in\s+\[.*\]', line):
                issues.append({
                    "rule_id": "list-membership-check",
                    "line": line_num,
                    "message": "Membership check in list is O(n)",
                    "suggestion": "Use set for O(1) membership checks"
                })
            
            # Check for string concatenation in loops
            if re.search(r'\w+\s*\+=\s*.*str', line):
                # Check if this is inside a loop (simplified check)
                for j in range(max(0, i - 10), i):
                    if re.search(r'for\s+|while\s+', lines[j]):
                        issues.append({
                            "rule_id": "string-concatenation-loop",
                            "line": line_num,
                            "message": "String concatenation in loop is inefficient",
                            "suggestion": "Use join() or list accumulation"
                        })
                        break
        
        return issues
    
    def suggest_optimizations(self, issues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Suggest specific optimizations for performance issues"""
        optimizations = []
        
        # Group issues by type
        issue_groups = {}
        for issue in issues:
            rule_id = issue.get("rule_id", "unknown")
            if rule_id not in issue_groups:
                issue_groups[rule_id] = []
            issue_groups[rule_id].append(issue)
        
        # Generate specific optimizations
        for rule_id, rule_issues in issue_groups.items():
            rule = self.get_rule_by_id(rule_id)
            if rule:
                optimizations.append({
                    "rule_id": rule_id,
                    "count": len(rule_issues),
                    "impact": rule.impact.value,
                    "optimization": rule.optimization,
                    "priority": self._calculate_priority(rule.impact, len(rule_issues))
                })
        
        # Sort by priority
        optimizations.sort(key=lambda x: x["priority"], reverse=True)
        return optimizations
    
    def _calculate_priority(self, impact: PerformanceImpact, count: int) -> int:
        """Calculate optimization priority"""
        impact_weights = {
            PerformanceImpact.CRITICAL: 10,
            PerformanceImpact.HIGH: 7,
            PerformanceImpact.MEDIUM: 4,
            PerformanceImpact.LOW: 1
        }
        
        return impact_weights[impact] * count
    
    def generate_performance_report(self, issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate performance analysis report"""
        impact_counts = {impact.value: 0 for impact in PerformanceImpact}
        complexity_issues = []
        
        for issue in issues:
            impact = issue.get("impact", "low")
            impact_counts[impact] += 1
            
            if issue.get("complexity_class"):
                complexity_issues.append(issue)
        
        return {
            "total_issues": len(issues),
            "impact_distribution": impact_counts,
            "complexity_issues": complexity_issues,
            "performance_score": self._calculate_performance_score(impact_counts),
            "optimizations": self.suggest_optimizations(issues)
        }
    
    def _calculate_performance_score(self, impact_counts: Dict[str, int]) -> float:
        """Calculate performance score (0-100)"""
        weights = {
            "critical": 15,
            "high": 8,
            "medium": 4,
            "low": 1
        }
        
        total_weight = sum(count * weights[level] for level, count in impact_counts.items())
        
        if total_weight == 0:
            return 100.0
        
        # Score decreases with more performance issues
        score = max(0, 100 - total_weight)
        return score
