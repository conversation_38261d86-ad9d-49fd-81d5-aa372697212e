"""
Quality Rules - Code quality and maintainability rules
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class RuleType(Enum):
    NAMING = "naming"
    STRUCTURE = "structure"
    DOCUMENTATION = "documentation"
    COMPLEXITY = "complexity"
    DUPLICATION = "duplication"


@dataclass
class QualityRule:
    """Represents a quality rule"""
    id: str
    name: str
    description: str
    rule_type: RuleType
    pattern: Optional[str] = None
    severity: str = "medium"
    auto_fixable: bool = False
    fix_template: Optional[str] = None


class QualityRules:
    """Collection of code quality rules"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize quality rules"""
        self.config = config
        self.rules = self._initialize_rules()
    
    def _initialize_rules(self) -> List[QualityRule]:
        """Initialize all quality rules"""
        return [
            # Naming rules
            QualityRule(
                id="naming-snake-case",
                name="Snake case naming",
                description="Variable and function names should use snake_case",
                rule_type=RuleType.NAMING,
                pattern=r'def\s+[A-Z]\w*|[a-z]+[A-Z]\w*\s*=',
                severity="low",
                auto_fixable=True
            ),
            
            QualityRule(
                id="naming-constant-case",
                name="Constant naming",
                description="Constants should be in UPPER_CASE",
                rule_type=RuleType.NAMING,
                pattern=r'^[a-z][a-z_]*\s*=\s*["\'\d]',
                severity="low",
                auto_fixable=True
            ),
            
            QualityRule(
                id="naming-class-case",
                name="Class naming",
                description="Class names should use PascalCase",
                rule_type=RuleType.NAMING,
                pattern=r'class\s+[a-z_]\w*',
                severity="medium",
                auto_fixable=True
            ),
            
            # Structure rules
            QualityRule(
                id="structure-function-length",
                name="Function length",
                description="Functions should not exceed 50 lines",
                rule_type=RuleType.STRUCTURE,
                severity="medium"
            ),
            
            QualityRule(
                id="structure-class-length",
                name="Class length",
                description="Classes should not exceed 500 lines",
                rule_type=RuleType.STRUCTURE,
                severity="medium"
            ),
            
            QualityRule(
                id="structure-parameter-count",
                name="Parameter count",
                description="Functions should not have more than 5 parameters",
                rule_type=RuleType.STRUCTURE,
                pattern=r'def\s+\w+\s*\([^)]*,\s*[^)]*,\s*[^)]*,\s*[^)]*,\s*[^)]*,',
                severity="medium"
            ),
            
            # Documentation rules
            QualityRule(
                id="doc-function-docstring",
                name="Function docstring",
                description="Public functions should have docstrings",
                rule_type=RuleType.DOCUMENTATION,
                severity="low"
            ),
            
            QualityRule(
                id="doc-class-docstring",
                name="Class docstring",
                description="Classes should have docstrings",
                rule_type=RuleType.DOCUMENTATION,
                severity="low"
            ),
            
            QualityRule(
                id="doc-module-docstring",
                name="Module docstring",
                description="Modules should have docstrings",
                rule_type=RuleType.DOCUMENTATION,
                severity="low"
            ),
            
            # Complexity rules
            QualityRule(
                id="complexity-cyclomatic",
                name="Cyclomatic complexity",
                description="Functions should have low cyclomatic complexity",
                rule_type=RuleType.COMPLEXITY,
                severity="high"
            ),
            
            QualityRule(
                id="complexity-nesting",
                name="Nesting depth",
                description="Code should not be nested too deeply",
                rule_type=RuleType.COMPLEXITY,
                severity="medium"
            ),
            
            # Duplication rules
            QualityRule(
                id="duplication-code-blocks",
                name="Code duplication",
                description="Avoid duplicated code blocks",
                rule_type=RuleType.DUPLICATION,
                severity="medium"
            ),
            
            QualityRule(
                id="duplication-magic-numbers",
                name="Magic numbers",
                description="Avoid magic numbers, use named constants",
                rule_type=RuleType.DUPLICATION,
                pattern=r'[^a-zA-Z_]\d{2,}[^a-zA-Z_]',
                severity="low",
                auto_fixable=True
            )
        ]
    
    def get_rules_by_type(self, rule_type: RuleType) -> List[QualityRule]:
        """Get rules by type"""
        return [rule for rule in self.rules if rule.rule_type == rule_type]
    
    def get_auto_fixable_rules(self) -> List[QualityRule]:
        """Get rules that can be auto-fixed"""
        return [rule for rule in self.rules if rule.auto_fixable]
    
    def get_rule_by_id(self, rule_id: str) -> Optional[QualityRule]:
        """Get rule by ID"""
        for rule in self.rules:
            if rule.id == rule_id:
                return rule
        return None
    
    def validate_naming_conventions(self, content: str, language: str = "python") -> List[Dict[str, Any]]:
        """Validate naming conventions"""
        violations = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check function naming
            func_match = re.search(r'def\s+([A-Z]\w*)', line)
            if func_match:
                violations.append({
                    "rule_id": "naming-snake-case",
                    "line": line_num,
                    "message": f"Function '{func_match.group(1)}' should use snake_case",
                    "suggestion": func_match.group(1).lower()
                })
            
            # Check class naming
            class_match = re.search(r'class\s+([a-z_]\w*)', line)
            if class_match:
                violations.append({
                    "rule_id": "naming-class-case",
                    "line": line_num,
                    "message": f"Class '{class_match.group(1)}' should use PascalCase",
                    "suggestion": class_match.group(1).title().replace('_', '')
                })
        
        return violations
    
    def check_function_complexity(self, content: str) -> List[Dict[str, Any]]:
        """Check function complexity"""
        violations = []
        lines = content.split('\n')
        
        current_function = None
        function_start = 0
        complexity = 0
        nesting_level = 0
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Track function start
            func_match = re.search(r'def\s+(\w+)', stripped)
            if func_match:
                if current_function:
                    # Check previous function
                    if complexity > 10:
                        violations.append({
                            "rule_id": "complexity-cyclomatic",
                            "line": function_start,
                            "message": f"Function '{current_function}' has high complexity: {complexity}",
                            "suggestion": "Break into smaller functions"
                        })
                
                current_function = func_match.group(1)
                function_start = line_num
                complexity = 1
                nesting_level = 0
            
            # Count complexity
            if any(keyword in stripped for keyword in ['if', 'elif', 'while', 'for', 'except']):
                complexity += 1
            
            # Track nesting
            if stripped.endswith(':'):
                nesting_level += 1
                if nesting_level > 4:
                    violations.append({
                        "rule_id": "complexity-nesting",
                        "line": line_num,
                        "message": f"Nesting level too deep: {nesting_level}",
                        "suggestion": "Extract nested logic into separate functions"
                    })
        
        return violations
    
    def check_documentation(self, content: str) -> List[Dict[str, Any]]:
        """Check documentation requirements"""
        violations = []
        lines = content.split('\n')
        
        # Check module docstring
        if not content.strip().startswith('"""') and not content.strip().startswith("'''"):
            violations.append({
                "rule_id": "doc-module-docstring",
                "line": 1,
                "message": "Module should have a docstring",
                "suggestion": "Add module docstring at the top"
            })
        
        # Check function and class docstrings
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Check function docstring
            func_match = re.search(r'def\s+(\w+)', stripped)
            if func_match and not stripped.startswith('_'):  # Public function
                # Look for docstring in next few lines
                has_docstring = False
                for j in range(i + 1, min(i + 5, len(lines))):
                    if '"""' in lines[j] or "'''" in lines[j]:
                        has_docstring = True
                        break
                
                if not has_docstring:
                    violations.append({
                        "rule_id": "doc-function-docstring",
                        "line": line_num,
                        "message": f"Function '{func_match.group(1)}' should have a docstring",
                        "suggestion": "Add docstring describing function purpose"
                    })
            
            # Check class docstring
            class_match = re.search(r'class\s+(\w+)', stripped)
            if class_match:
                # Look for docstring in next few lines
                has_docstring = False
                for j in range(i + 1, min(i + 5, len(lines))):
                    if '"""' in lines[j] or "'''" in lines[j]:
                        has_docstring = True
                        break
                
                if not has_docstring:
                    violations.append({
                        "rule_id": "doc-class-docstring",
                        "line": line_num,
                        "message": f"Class '{class_match.group(1)}' should have a docstring",
                        "suggestion": "Add docstring describing class purpose"
                    })
        
        return violations
    
    def suggest_auto_fixes(self, violations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Suggest automatic fixes for violations"""
        fixes = []
        
        for violation in violations:
            rule = self.get_rule_by_id(violation["rule_id"])
            if rule and rule.auto_fixable:
                fixes.append({
                    "rule_id": violation["rule_id"],
                    "line": violation["line"],
                    "original": violation.get("original", ""),
                    "fixed": violation.get("suggestion", ""),
                    "description": f"Auto-fix for {rule.name}"
                })
        
        return fixes
