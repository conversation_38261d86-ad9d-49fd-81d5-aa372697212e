"""
Security Rules - Security vulnerability detection rules
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class SecurityRiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityRule:
    """Represents a security rule"""
    id: str
    name: str
    description: str
    risk_level: SecurityRiskLevel
    pattern: Optional[str] = None
    cwe_id: Optional[str] = None
    mitigation: Optional[str] = None


class SecurityRules:
    """Collection of security rules"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize security rules"""
        self.config = config
        self.rules = self._initialize_rules()
    
    def _initialize_rules(self) -> List[SecurityRule]:
        """Initialize all security rules"""
        return [
            # Injection vulnerabilities
            SecurityRule(
                id="sql-injection",
                name="SQL Injection",
                description="Potential SQL injection vulnerability",
                risk_level=SecurityRiskLevel.HIGH,
                pattern=r'execute\s*\(\s*["\'].*%.*["\']|cursor\.execute\s*\(\s*f["\']',
                cwe_id="CWE-89",
                mitigation="Use parameterized queries or prepared statements"
            ),
            
            SecurityRule(
                id="command-injection",
                name="Command Injection",
                description="Potential command injection vulnerability",
                risk_level=SecurityRiskLevel.CRITICAL,
                pattern=r'os\.system\s*\(|subprocess\.call\s*\(.*shell=True|eval\s*\(',
                cwe_id="CWE-78",
                mitigation="Avoid shell=True, validate and sanitize input"
            ),
            
            SecurityRule(
                id="code-injection",
                name="Code Injection",
                description="Potential code injection vulnerability",
                risk_level=SecurityRiskLevel.CRITICAL,
                pattern=r'eval\s*\(|exec\s*\(|compile\s*\(',
                cwe_id="CWE-94",
                mitigation="Avoid dynamic code execution, use safe alternatives"
            ),
            
            # Cryptographic issues
            SecurityRule(
                id="weak-crypto",
                name="Weak Cryptography",
                description="Use of weak cryptographic algorithms",
                risk_level=SecurityRiskLevel.HIGH,
                pattern=r'md5\s*\(|sha1\s*\(|DES|RC4',
                cwe_id="CWE-327",
                mitigation="Use strong cryptographic algorithms like SHA-256, AES"
            ),
            
            SecurityRule(
                id="hardcoded-secret",
                name="Hardcoded Secret",
                description="Hardcoded password or secret key",
                risk_level=SecurityRiskLevel.HIGH,
                pattern=r'password\s*=\s*["\'][^"\']+["\']|api_key\s*=\s*["\'][^"\']+["\']|secret\s*=\s*["\'][^"\']+["\']',
                cwe_id="CWE-798",
                mitigation="Use environment variables or secure key management"
            ),
            
            # Input validation
            SecurityRule(
                id="path-traversal",
                name="Path Traversal",
                description="Potential path traversal vulnerability",
                risk_level=SecurityRiskLevel.HIGH,
                pattern=r'open\s*\([^)]*\.\./|os\.path\.join\s*\([^)]*\.\.',
                cwe_id="CWE-22",
                mitigation="Validate and sanitize file paths, use safe path operations"
            ),
            
            SecurityRule(
                id="unsafe-deserialization",
                name="Unsafe Deserialization",
                description="Unsafe deserialization of untrusted data",
                risk_level=SecurityRiskLevel.HIGH,
                pattern=r'pickle\.loads?\s*\(|yaml\.load\s*\((?!.*Loader=yaml\.SafeLoader)',
                cwe_id="CWE-502",
                mitigation="Use safe deserialization methods, validate input"
            ),
            
            # Web security
            SecurityRule(
                id="xss-vulnerability",
                name="Cross-Site Scripting",
                description="Potential XSS vulnerability",
                risk_level=SecurityRiskLevel.MEDIUM,
                pattern=r'innerHTML\s*=|document\.write\s*\(',
                cwe_id="CWE-79",
                mitigation="Escape output, use textContent instead of innerHTML"
            ),
            
            SecurityRule(
                id="csrf-missing",
                name="CSRF Protection Missing",
                description="Missing CSRF protection",
                risk_level=SecurityRiskLevel.MEDIUM,
                pattern=r'@app\.route.*methods=.*POST(?!.*csrf)',
                cwe_id="CWE-352",
                mitigation="Implement CSRF tokens for state-changing operations"
            ),
            
            # Authentication and authorization
            SecurityRule(
                id="weak-session",
                name="Weak Session Management",
                description="Weak session management",
                risk_level=SecurityRiskLevel.MEDIUM,
                pattern=r'session\[.*\]\s*=.*(?!.*secure)',
                cwe_id="CWE-384",
                mitigation="Use secure session configuration"
            ),
            
            # Information disclosure
            SecurityRule(
                id="debug-info-leak",
                name="Debug Information Leak",
                description="Debug information exposed in production",
                risk_level=SecurityRiskLevel.LOW,
                pattern=r'debug\s*=\s*True|app\.run\s*\(.*debug=True',
                cwe_id="CWE-200",
                mitigation="Disable debug mode in production"
            ),
            
            SecurityRule(
                id="sensitive-data-log",
                name="Sensitive Data in Logs",
                description="Sensitive data logged",
                risk_level=SecurityRiskLevel.MEDIUM,
                pattern=r'log.*password|print.*password|log.*secret|print.*secret',
                cwe_id="CWE-532",
                mitigation="Avoid logging sensitive information"
            )
        ]
    
    def get_rules_by_risk_level(self, risk_level: SecurityRiskLevel) -> List[SecurityRule]:
        """Get rules by risk level"""
        return [rule for rule in self.rules if rule.risk_level == risk_level]
    
    def get_critical_rules(self) -> List[SecurityRule]:
        """Get critical security rules"""
        return self.get_rules_by_risk_level(SecurityRiskLevel.CRITICAL)
    
    def get_rule_by_id(self, rule_id: str) -> Optional[SecurityRule]:
        """Get rule by ID"""
        for rule in self.rules:
            if rule.id == rule_id:
                return rule
        return None
    
    def scan_for_vulnerabilities(self, content: str, language: str = "python") -> List[Dict[str, Any]]:
        """Scan content for security vulnerabilities"""
        vulnerabilities = []
        lines = content.split('\n')
        
        for rule in self.rules:
            if rule.pattern:
                for i, line in enumerate(lines):
                    line_num = i + 1
                    
                    if re.search(rule.pattern, line, re.IGNORECASE):
                        vulnerabilities.append({
                            "rule_id": rule.id,
                            "name": rule.name,
                            "description": rule.description,
                            "risk_level": rule.risk_level.value,
                            "line": line_num,
                            "code": line.strip(),
                            "cwe_id": rule.cwe_id,
                            "mitigation": rule.mitigation
                        })
        
        return vulnerabilities
    
    def check_crypto_usage(self, content: str) -> List[Dict[str, Any]]:
        """Check cryptographic usage"""
        issues = []
        lines = content.split('\n')
        
        weak_crypto_patterns = {
            'md5': 'Use SHA-256 or stronger hash functions',
            'sha1': 'Use SHA-256 or stronger hash functions',
            'DES': 'Use AES encryption instead',
            'RC4': 'Use AES encryption instead'
        }
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            for weak_algo, suggestion in weak_crypto_patterns.items():
                if re.search(rf'\b{weak_algo}\b', line, re.IGNORECASE):
                    issues.append({
                        "rule_id": "weak-crypto",
                        "line": line_num,
                        "algorithm": weak_algo,
                        "message": f"Weak cryptographic algorithm: {weak_algo}",
                        "suggestion": suggestion
                    })
        
        return issues
    
    def check_input_validation(self, content: str) -> List[Dict[str, Any]]:
        """Check input validation"""
        issues = []
        lines = content.split('\n')
        
        dangerous_patterns = [
            (r'request\.args\.get\s*\([^)]*\)(?!.*validate)', "Validate user input"),
            (r'request\.form\.get\s*\([^)]*\)(?!.*validate)', "Validate form input"),
            (r'input\s*\([^)]*\)(?!.*validate)', "Validate user input"),
            (r'sys\.argv\[.*\](?!.*validate)', "Validate command line arguments")
        ]
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            for pattern, suggestion in dangerous_patterns:
                if re.search(pattern, line):
                    issues.append({
                        "rule_id": "input-validation",
                        "line": line_num,
                        "message": "Unvalidated input detected",
                        "suggestion": suggestion
                    })
        
        return issues
    
    def check_authentication(self, content: str) -> List[Dict[str, Any]]:
        """Check authentication and authorization"""
        issues = []
        lines = content.split('\n')
        
        auth_patterns = [
            (r'@app\.route.*(?!.*login_required)', "Add authentication to protected routes"),
            (r'session\[.*\](?!.*secure)', "Use secure session configuration"),
            (r'jwt\.decode\s*\([^)]*verify=False', "Enable JWT signature verification")
        ]
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            for pattern, suggestion in auth_patterns:
                if re.search(pattern, line):
                    issues.append({
                        "rule_id": "authentication",
                        "line": line_num,
                        "message": "Authentication/authorization issue",
                        "suggestion": suggestion
                    })
        
        return issues
    
    def generate_security_report(self, vulnerabilities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate security assessment report"""
        risk_counts = {level.value: 0 for level in SecurityRiskLevel}
        cwe_mapping = {}
        
        for vuln in vulnerabilities:
            risk_level = vuln.get("risk_level", "low")
            risk_counts[risk_level] += 1
            
            cwe_id = vuln.get("cwe_id")
            if cwe_id:
                if cwe_id not in cwe_mapping:
                    cwe_mapping[cwe_id] = []
                cwe_mapping[cwe_id].append(vuln)
        
        return {
            "total_vulnerabilities": len(vulnerabilities),
            "risk_distribution": risk_counts,
            "cwe_mapping": cwe_mapping,
            "security_score": self._calculate_security_score(risk_counts),
            "recommendations": self._generate_recommendations(vulnerabilities)
        }
    
    def _calculate_security_score(self, risk_counts: Dict[str, int]) -> float:
        """Calculate security score (0-100)"""
        weights = {
            "critical": 10,
            "high": 5,
            "medium": 2,
            "low": 1
        }
        
        total_weight = sum(count * weights[level] for level, count in risk_counts.items())
        
        if total_weight == 0:
            return 100.0
        
        # Score decreases with more vulnerabilities
        score = max(0, 100 - total_weight)
        return score
    
    def _generate_recommendations(self, vulnerabilities: List[Dict[str, Any]]) -> List[str]:
        """Generate security recommendations"""
        recommendations = []
        
        # Group by rule type
        rule_counts = {}
        for vuln in vulnerabilities:
            rule_id = vuln.get("rule_id", "unknown")
            rule_counts[rule_id] = rule_counts.get(rule_id, 0) + 1
        
        # Generate recommendations based on most common issues
        if rule_counts.get("sql-injection", 0) > 0:
            recommendations.append("Implement parameterized queries to prevent SQL injection")
        
        if rule_counts.get("command-injection", 0) > 0:
            recommendations.append("Avoid shell=True in subprocess calls, validate all input")
        
        if rule_counts.get("weak-crypto", 0) > 0:
            recommendations.append("Upgrade to strong cryptographic algorithms")
        
        if rule_counts.get("hardcoded-secret", 0) > 0:
            recommendations.append("Move secrets to environment variables or secure storage")
        
        if not recommendations:
            recommendations.append("Continue following security best practices")
        
        return recommendations
