"""
Generic Analyzer - Language-agnostic code analyzer
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set

from .base_analyzer import BaseAnalyzer
from ..models.code_issue import CodeIssue, IssueSeverity, IssueCategory
from ..models.analysis_report import AnalysisReport


class GenericAnalyzer(BaseAnalyzer):
    """Generic analyzer for language-agnostic code issues"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize generic analyzer"""
        super().__init__(config)
        
        # Generic patterns
        self.todo_patterns = [
            r'TODO:?',
            r'FIXME:?',
            r'HACK:?',
            r'XXX:?',
            r'BUG:?'
        ]
        
        self.long_line_threshold = config.get("long_line_threshold", 120)
        self.max_function_length = config.get("max_function_length", 50)
        self.max_complexity = config.get("max_complexity", 10)
        
        # Configuration
        self.check_todos = config.get("check_todos", True)
        self.check_line_length = config.get("check_line_length", True)
        self.check_complexity = config.get("check_complexity", True)
    
    async def analyze_code(
        self, 
        content: str, 
        filename: str, 
        categories: List[str],
        **kwargs
    ) -> List[CodeIssue]:
        """
        Analyze code for generic issues
        
        Args:
            content: Code content to analyze
            filename: Name of the file being analyzed
            categories: Categories to analyze for
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            List of code issues
        """
        self.logger.info(f"Analyzing generic code issues in {filename}")
        
        # Filter categories
        supported_categories = {"style", "maintainability", "documentation"}
        filtered_categories = self._filter_categories(categories, supported_categories)
        
        if not filtered_categories:
            self.logger.info("No relevant categories to analyze for generic issues")
            return []
        
        issues = []
        
        # TODO/FIXME comments
        if self.check_todos and ("documentation" in filtered_categories or "maintainability" in filtered_categories):
            todo_issues = self._check_todo_comments(content, filename)
            issues.extend(todo_issues)
        
        # Line length
        if self.check_line_length and ("style" in filtered_categories):
            line_issues = self._check_line_length(content, filename)
            issues.extend(line_issues)
        
        # Code complexity
        if self.check_complexity and ("maintainability" in filtered_categories):
            complexity_issues = self._check_complexity(content, filename)
            issues.extend(complexity_issues)
        
        return issues
    
    async def analyze(self, content: str, filename: str) -> AnalysisReport:
        """Analyze the given code content."""
        # TODO: Implement actual analysis
        return AnalysisReport(
            issues=[],
            summary="Dummy report"
        )
    
    def _check_todo_comments(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for TODO/FIXME comments"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            for pattern in self.todo_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    severity = IssueSeverity.LOW
                    if 'FIXME' in line.upper() or 'BUG' in line.upper():
                        severity = IssueSeverity.MEDIUM
                    
                    issues.append(self._create_issue(
                        id_prefix="generic",
                        id_value="todo-comment",
                        title=f"TODO/FIXME comment found",
                        description=f"Code contains a {pattern.rstrip(':?')} comment that should be addressed",
                        severity=severity,
                        category=IssueCategory.DOCUMENTATION,
                        file_path=filename,
                        line_start=line_num,
                        code_snippet=self._extract_code_snippet(content, i, i, context_lines=1),
                        fix_suggestions=[
                            "Address the TODO/FIXME comment",
                            "Create a ticket to track the work if it cannot be done immediately"
                        ],
                        rule_id="generic-todo-comments",
                        tool_source="generic_analyzer"
                    ))
        
        return issues
    
    def _check_line_length(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for long lines"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            if len(line) > self.long_line_threshold:
                issues.append(self._create_issue(
                    id_prefix="generic",
                    id_value="long-line",
                    title="Line too long",
                    description=f"Line {line_num} has {len(line)} characters, exceeds {self.long_line_threshold} character limit",
                    severity=IssueSeverity.LOW,
                    category=IssueCategory.STYLE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=0),
                    fix_suggestions=[
                        "Break long line into multiple lines",
                        "Extract complex expressions into variables"
                    ],
                    rule_id="generic-line-length",
                    tool_source="generic_analyzer"
                ))
        
        return issues
    
    def _check_complexity(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for code complexity issues"""
        issues = []
        lines = content.split('\n')
        
        # Simple complexity analysis based on control structures
        function_start = None
        function_name = None
        complexity = 0
        
        for i, line in enumerate(lines):
            line_num = i + 1
            stripped = line.strip()
            
            # Detect function start (simple heuristic)
            func_match = re.search(r'(def|function|func)\s+(\w+)', stripped)
            if func_match:
                if function_start is not None:
                    # End of previous function
                    if complexity > self.max_complexity:
                        issues.append(self._create_issue(
                            id_prefix="generic",
                            id_value="high-complexity",
                            title="High cyclomatic complexity",
                            description=f"Function '{function_name}' has complexity {complexity}, exceeds {self.max_complexity}",
                            severity=IssueSeverity.MEDIUM,
                            category=IssueCategory.MAINTAINABILITY,
                            file_path=filename,
                            line_start=function_start,
                            fix_suggestions=[
                                "Break function into smaller functions",
                                "Reduce nested control structures"
                            ],
                            rule_id="generic-complexity",
                            tool_source="generic_analyzer"
                        ))
                
                function_start = line_num
                function_name = func_match.group(2)
                complexity = 1  # Base complexity
            
            # Count complexity-increasing constructs
            if any(keyword in stripped for keyword in ['if', 'elif', 'else if', 'while', 'for', 'switch', 'case', 'catch', 'except']):
                complexity += 1
        
        # Check last function
        if function_start is not None and complexity > self.max_complexity:
            issues.append(self._create_issue(
                id_prefix="generic",
                id_value="high-complexity",
                title="High cyclomatic complexity",
                description=f"Function '{function_name}' has complexity {complexity}, exceeds {self.max_complexity}",
                severity=IssueSeverity.MEDIUM,
                category=IssueCategory.MAINTAINABILITY,
                file_path=filename,
                line_start=function_start,
                fix_suggestions=[
                    "Break function into smaller functions",
                    "Reduce nested control structures"
                ],
                rule_id="generic-complexity",
                tool_source="generic_analyzer"
            ))
        
        return issues
