"""
Base Analyzer - Abstract base class for all code analyzers
"""

import abc
import logging
from typing import Dict, List, Any, Optional, Set

from ..models.code_issue import CodeIssue, IssueSeverity, IssueCategory


class BaseAnalyzer(abc.ABC):
    """Abstract base class for all code analyzers"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize base analyzer
        
        Args:
            config: Analyzer configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abc.abstractmethod
    async def analyze_code(
        self, 
        content: str, 
        filename: str, 
        categories: List[str],
        **kwargs
    ) -> List[CodeIssue]:
        """
        Analyze code and return issues
        
        Args:
            content: Code content to analyze
            filename: Name of the file being analyzed
            categories: Categories to analyze for
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            List of code issues
        """
        pass
    
    def _create_issue(
        self,
        id_prefix: str,
        id_value: str,
        title: str,
        description: str,
        severity: IssueSeverity,
        category: IssueCategory,
        file_path: Optional[str] = None,
        line_start: Optional[int] = None,
        line_end: Optional[int] = None,
        column_start: Optional[int] = None,
        column_end: Optional[int] = None,
        code_snippet: Optional[str] = None,
        fix_suggestions: List[str] = None,
        auto_fixable: bool = False,
        rule_id: Optional[str] = None,
        tool_source: Optional[str] = None,
        metadata: Dict[str, Any] = None
    ) -> CodeIssue:
        """
        Create a code issue
        
        Args:
            id_prefix: Prefix for issue ID
            id_value: Value for issue ID
            title: Issue title
            description: Issue description
            severity: Issue severity
            category: Issue category
            file_path: Path to file
            line_start: Starting line number
            line_end: Ending line number
            column_start: Starting column number
            column_end: Ending column number
            code_snippet: Code snippet
            fix_suggestions: List of fix suggestions
            auto_fixable: Whether issue is auto-fixable
            rule_id: ID of rule that detected issue
            tool_source: Source tool
            metadata: Additional metadata
            
        Returns:
            Code issue
        """
        return CodeIssue(
            id=f"{id_prefix}-{id_value}",
            title=title,
            description=description,
            severity=severity,
            category=category,
            file_path=file_path,
            line_start=line_start,
            line_end=line_end,
            column_start=column_start,
            column_end=column_end,
            code_snippet=code_snippet,
            fix_suggestions=fix_suggestions or [],
            auto_fixable=auto_fixable,
            rule_id=rule_id,
            tool_source=tool_source,
            metadata=metadata or {}
        )
    
    def _extract_code_snippet(
        self, 
        content: str, 
        line_start: int, 
        line_end: Optional[int] = None,
        context_lines: int = 1
    ) -> str:
        """
        Extract code snippet from content
        
        Args:
            content: Code content
            line_start: Starting line number (1-indexed)
            line_end: Ending line number (1-indexed)
            context_lines: Number of context lines to include
            
        Returns:
            Code snippet
        """
        if not content:
            return ""
        
        lines = content.split("\n")
        total_lines = len(lines)
        
        # Adjust line numbers to 0-indexed
        line_start_idx = max(0, line_start - 1)
        line_end_idx = min(total_lines - 1, (line_end or line_start) - 1)
        
        # Add context lines
        snippet_start = max(0, line_start_idx - context_lines)
        snippet_end = min(total_lines - 1, line_end_idx + context_lines)
        
        # Extract snippet
        snippet_lines = lines[snippet_start:snippet_end + 1]
        
        # Add line numbers
        numbered_lines = [
            f"{i+1+snippet_start}: {line}" for i, line in enumerate(snippet_lines)
        ]
        
        return "\n".join(numbered_lines)
    
    def _filter_categories(self, categories: List[str], supported_categories: Set[str]) -> List[str]:
        """
        Filter categories to only include supported ones
        
        Args:
            categories: Categories to filter
            supported_categories: Set of supported categories
            
        Returns:
            Filtered categories
        """
        if not categories:
            return list(supported_categories)
        
        return [cat for cat in categories if cat.lower() in supported_categories]
