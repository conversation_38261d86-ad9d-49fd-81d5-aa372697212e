"""
Python Analyzer
Analyzes Python code for issues with special focus on PyOpenCL/OpenCL code
"""
import ast
import os
import re
import json
import asyncio
import logging
import subprocess
from typing import Dict, List, Any, Optional, Set, Tuple

from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory, CritiqueLevel
from critique_engine.models.critique_result import CodeIssue, IssueSeverity
from critique_engine.models.analysis_report import AnalysisReport  # Import AnalysisReport


class PythonAnalyzer:
    """
    Python Analyzer
    
    Analyzes Python code for issues using AST and external tools, with special focus on PyOpenCL/OpenCL code
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Python Analyzer
        
        Args:
            config: Analyzer configuration
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Configure tools
        self.use_pylint = self.config.get("use_pylint", True)
        self.use_mypy = self.config.get("use_mypy", True)
        self.use_bandit = self.config.get("use_bandit", True)
        self.use_pycodestyle = self.config.get("use_pycodestyle", True)
        
        # Configure style options
        self.max_line_length = self.config.get("max_line_length", 88)
        self.indent_size = self.config.get("indent_size", 4)
        
        # Configure complexity thresholds
        self.max_complexity = self.config.get("max_complexity", 10)
        self.max_function_length = self.config.get("max_function_length", 50)
        self.max_arguments = self.config.get("max_arguments", 5)
        
        # Configure OpenCL specific options
        self.analyze_opencl = self.config.get("analyze_opencl", True)
        self.opencl_patterns = [
            r'import\s+pyopencl',
            r'cl\.[A-Za-z_]+',
            r'\.get_info\(',
            r'\.create_\w+',
            r'\.enqueue_\w+'
        ]
        
        # OpenCL specific analysis settings
        self.opencl_config = {
            'max_work_group_size': self.config.get("max_work_group_size", 256),
            'check_memory_leaks': self.config.get("check_memory_leaks", True),
            'check_barrier_sync': self.config.get("check_barrier_sync", True),
            'check_buffer_bounds': self.config.get("check_buffer_bounds", True)
        }
        
        # Initialize AST analyzer
        self.ast_analyzer = ASTAnalyzer(
            max_complexity=self.max_complexity,
            max_function_length=self.max_function_length,
            max_arguments=self.max_arguments
        )
    
    async def analyze_file(
        self, 
        file_path: str, 
        filename: str,
        categories: Set[CritiqueCategory],
        level: CritiqueLevel
    ) -> List[CodeIssue]:
        """
        Analyze Python file with special focus on PyOpenCL/OpenCL code
        
        Args:
            file_path: Path to file
            filename: Original filename
            categories: Categories to analyze
            level: Analysis level
            
        Returns:
            List of code issues
        """
        self.logger.info(f"Analyzing Python file: {filename}")
        
        issues = []
        
        # Read file content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic syntax check
        if CritiqueCategory.SYNTAX in categories or not categories:
            syntax_issues = self._check_syntax(content, filename)
            issues.extend(syntax_issues)
            
            # If there are syntax errors, skip further analysis
            if any(issue.severity == IssueSeverity.HIGH for issue in syntax_issues):
                return issues
        
        # AST analysis
        if level != CritiqueLevel.BASIC:
            try:
                tree = ast.parse(content)
                ast_issues = self._analyze_ast(tree, content, filename, categories)
                issues.extend(ast_issues)
            except SyntaxError:
                # Already reported in syntax check
                pass
        
        # Style analysis
        if CritiqueCategory.STYLE in categories or not categories:
            style_issues = self._analyze_style(content, filename)
            issues.extend(style_issues)
        
        # OpenCL specific analysis
        if self.analyze_opencl and self._contains_opencl(content):
            opencl_issues = self._analyze_opencl(content, filename)
            issues.extend(opencl_issues)
        
        # External tools analysis for comprehensive level
        if level == CritiqueLevel.COMPREHENSIVE:
            # Run external tools if enabled
            if self.use_pylint:
                pylint_issues = await self._run_pylint(file_path, filename)
                issues.extend(pylint_issues)
            
            if self.use_mypy:
                mypy_issues = await self._run_mypy(file_path, filename)
                issues.extend(mypy_issues)
            
            if self.use_bandit and CritiqueCategory.SECURITY in categories:
                bandit_issues = await self._run_bandit(file_path, filename)
                issues.extend(bandit_issues)
        
        return issues

    async def analyze(self, content: str, filename: str) -> AnalysisReport:
        """Analyze the given code content."""
        # TODO: Implement actual analysis
        return AnalysisReport(
            issues=[],
            summary="Dummy report"
        )

    def _check_syntax(self, content: str, filename: str) -> List[CodeIssue]:
        """
        Check Python syntax
        
        Args:
            content: File content
            filename: Original filename
            
        Returns:
            List of syntax issues
        """
        issues = []
        
        try:
            ast.parse(content)
        except SyntaxError as e:
            issues.append(CodeIssue(
                line=e.lineno or 1,
                column=e.offset or 1,
                message=f"Syntax error: {str(e)}",
                severity=IssueSeverity.HIGH,
                category=CritiqueCategory.SYNTAX,
                filename=filename
            ))
        
        return issues

    def _analyze_ast(self, tree: ast.AST, content: str, filename: str, categories: Set[CritiqueCategory]) -> List[CodeIssue]:
        """
        Analyze Python AST
        
        Args:
            tree: AST tree
            content: File content
            filename: Original filename
            categories: Categories to analyze
            
        Returns:
            List of AST analysis issues
        """
        issues = []
        
        # Analyze complexity and structure
        if CritiqueCategory.COMPLEXITY in categories or not categories:
            issues.extend(self.ast_analyzer.analyze_complexity(tree, filename))
        
        # Analyze imports and dependencies
        if CritiqueCategory.IMPORTS in categories or not categories:
            issues.extend(self.ast_analyzer.analyze_imports(tree, filename))
        
        # Analyze function and class definitions
        if CritiqueCategory.STRUCTURE in categories or not categories:
            issues.extend(self.ast_analyzer.analyze_definitions(tree, filename))
        
        # Analyze OpenCL specific patterns in AST
        if self.analyze_opencl:
            issues.extend(self._analyze_opencl_ast(tree, filename))
        
        return issues
        
    def _analyze_opencl_ast(self, tree: ast.AST, filename: str) -> List[CodeIssue]:
        """
        Analyze OpenCL specific patterns in AST
        
        Args:
            tree: AST tree
            filename: Original filename
            
        Returns:
            List of OpenCL-specific issues
        """
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                # Check for OpenCL kernel function calls
                if isinstance(node.func, ast.Attribute) and any(attr in node.func.attr for attr in ['create_kernel', 'create_program']):
                    # Check work group size
                    if hasattr(node, 'keywords'):
                        for keyword in node.keywords:
                            if keyword.arg == 'global_work_size':
                                if isinstance(keyword.value, ast.Constant) and keyword.value.value > self.opencl_config['max_work_group_size']:
                                    issues.append(CodeIssue(
                                        line=node.lineno,
                                        column=node.col_offset,
                                        message=f"Work group size ({keyword.value.value}) exceeds maximum ({self.opencl_config['max_work_group_size']})",
                                        severity=IssueSeverity.MEDIUM,
                                        category=CritiqueCategory.PERFORMANCE,
                                        filename=filename
                                    ))
                
                # Check for proper memory management
                if isinstance(node.func, ast.Attribute) and 'create_buffer' in node.func.attr:
                    # Track buffer creation for memory leak detection
                    issues.extend(self._check_buffer_management(node, filename))
                
                # Check for barrier synchronization
                if isinstance(node.func, ast.Attribute) and 'barrier' in node.func.attr:
                    issues.extend(self._check_barrier_sync(node, filename))
        
        return issues
    
    def _check_buffer_management(self, node: ast.Call, filename: str) -> List[CodeIssue]:
        """
        Check OpenCL buffer management
        
        Args:
            node: AST call node
            filename: Original filename
            
        Returns:
            List of buffer management issues
        """
        issues = []
        
        # Look for buffer release in the same function
        function_def = self.ast_analyzer._get_parent_function(node)
        if function_def:
            has_release = False
            for child in ast.walk(function_def):
                if isinstance(child, ast.Call) and \
                   isinstance(child.func, ast.Attribute) and \
                   'release' in child.func.attr:
                    has_release = True
                    break
            
            if not has_release:
                issues.append(CodeIssue(
                    line=node.lineno,
                    column=node.col_offset,
                    message="OpenCL buffer created but not explicitly released",
                    severity=IssueSeverity.MEDIUM,
                    category=CritiqueCategory.MEMORY,
                    filename=filename
                ))
        
        return issues
    
    def _check_barrier_sync(self, node: ast.Call, filename: str) -> List[CodeIssue]:
        """
        Check OpenCL barrier synchronization
        
        Args:
            node: AST call node
            filename: Original filename
            
        Returns:
            List of barrier synchronization issues
        """
        issues = []
        
        # Check if barrier is inside a conditional
        parent_if = self.ast_analyzer._get_parent_if(node)
        if parent_if:
            issues.append(CodeIssue(
                line=node.lineno,
                column=node.col_offset,
                message="Barrier synchronization inside conditional block may cause deadlock",
                severity=IssueSeverity.HIGH,
                category=CritiqueCategory.CONCURRENCY,
                filename=filename
            ))
        
        return issues
    
    def _analyze_style(self, content: str, filename: str) -> List[CodeIssue]:
        """
        Analyze code style
        
        Args:
            content: File content
            filename: Original filename
            
        Returns:
            List of style issues
        """
        issues = []
        lines = content.splitlines()
        
        # Check line length
        for i, line in enumerate(lines):
            if len(line) > self.max_line_length:
                issues.append(CodeIssue(
                    line=i + 1,
                    column=self.max_line_length + 1,
                    message=f"Line too long ({len(line)} > {self.max_line_length})",
                    severity=IssueSeverity.LOW,
                    category=CritiqueCategory.STYLE,
                    filename=filename
                ))
        
        # Check indentation
        for i, line in enumerate(lines):
            if line.startswith(' '):
                indent_count = len(line) - len(line.lstrip(' '))
                if indent_count % self.indent_size != 0:
                    issues.append(CodeIssue(
                        line=i + 1,
                        column=1,
                        message=f"Inconsistent indentation (found {indent_count} spaces, expected multiple of {self.indent_size})",
                        severity=IssueSeverity.LOW,
                        category=CritiqueCategory.STYLE,
                        filename=filename
                    ))
        
        # Check trailing whitespace
        for i, line in enumerate(lines):
            if line.rstrip() != line:
                issues.append(CodeIssue(
                    line=i + 1,
                    column=len(line.rstrip()) + 1,
                    message="Trailing whitespace",
                    severity=IssueSeverity.LOW,
                    category=CritiqueCategory.STYLE,
                    filename=filename
                ))
        
        # Check for blank lines at end of file
        if content.endswith('\n\n'):
            issues.append(CodeIssue(
                line=len(lines),
                column=1,
                message="Multiple blank lines at end of file",
                severity=IssueSeverity.LOW,
                category=CritiqueCategory.STYLE,
                filename=filename
            ))
        
        return issues
    
    def _contains_opencl(self, content: str) -> bool:
        """
        Check if content contains OpenCL code
        
        Args:
            content: File content
            
        Returns:
            True if content contains OpenCL code
        """
        for pattern in self.opencl_patterns:
            if re.search(pattern, content):
                return True
        return False
    
    def _analyze_opencl(self, content: str, filename: str) -> List[CodeIssue]:
        """
        Analyze OpenCL specific code patterns
        
        Args:
            content: File content
            filename: Original filename
            
        Returns:
            List of OpenCL-specific issues
        """
        issues = []
        
        # Check for error handling in OpenCL code
        if 'pyopencl' in content and not re.search(r'try\s*:', content):
            issues.append(CodeIssue(
                line=1,
                column=1,
                message="OpenCL code should include error handling with try/except",
                severity=IssueSeverity.MEDIUM,
                category=CritiqueCategory.ERROR_HANDLING,
                filename=filename
            ))
        
        # Check for potential buffer overflows
        if self.opencl_config['check_buffer_bounds']:
            buffer_access_pattern = r'\.get_\w+\(\s*\w+\s*,\s*\d+\s*\)'
            for match in re.finditer(buffer_access_pattern, content):
                issues.append(CodeIssue(
                    line=content[:match.start()].count('\n') + 1,
                    column=match.start() - content.rfind('\n', 0, match.start()),
                    message="Potential buffer access without bounds checking",
                    severity=IssueSeverity.MEDIUM,
                    category=CritiqueCategory.SECURITY,
                    filename=filename
                ))
        
        # Check for memory leaks in with blocks
        if self.opencl_config['check_memory_leaks'] and 'create_' in content:
            if not re.search(r'with\s+\w+\.\w+\s+as\s+\w+:', content):
                issues.append(CodeIssue(
                    line=1,
                    column=1,
                    message="Consider using 'with' statement for OpenCL resources to ensure proper cleanup",
                    severity=IssueSeverity.LOW,
                    category=CritiqueCategory.MEMORY,
                    filename=filename
                ))
        
        return issues
        
    async def _run_pylint(self, file_path: str, filename: str) -> List[CodeIssue]:
        """
        Run Pylint analysis
        
        Args:
            file_path: Path to file
            filename: Original filename
            
        Returns:
            List of pylint issues
        """
        issues = []
        
        if not file_path or not os.path.exists(file_path):
            return issues
            
        process = await asyncio.create_subprocess_exec(
            'pylint',
            '--output-format=json',
            file_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0 and stdout:
            decoded_output = stdout.decode()
            if decoded_output.strip():
                pylint_issues = json.loads(decoded_output) if decoded_output.startswith('[') else []
                for issue in pylint_issues:
                    severity = IssueSeverity.LOW
                    if issue.get('type') in ['error', 'fatal']:
                        severity = IssueSeverity.HIGH
                    elif issue.get('type') == 'warning':
                        severity = IssueSeverity.MEDIUM
                    
                    issues.append(CodeIssue(
                        line=issue.get('line', 1),
                        column=issue.get('column', 1),
                        message=issue.get('message', ''),
                        severity=severity,
                        category=CritiqueCategory.QUALITY,
                        filename=filename
                    ))
        
        return issues

    async def _run_mypy(self, file_path: str, filename: str) -> List[CodeIssue]:
        """
        Run Mypy type checking
        
        Args:
            file_path: Path to file
            filename: Original filename
            
        Returns:
            List of mypy issues
        """
        issues = []
        
        if not file_path or not os.path.exists(file_path):
            return issues
            
        process = await asyncio.create_subprocess_exec(
            'mypy',
            '--show-column-numbers',
            '--no-error-summary',
            file_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if stdout:
            for line in stdout.decode().splitlines():
                # Parse Mypy output format: file:line:col: severity: message
                match = re.match(r'.*:(\d+):(\d+): (\w+): (.+)', line)
                if match:
                    line_num, col, level, message = match.groups()
                    
                    severity = IssueSeverity.MEDIUM
                    if level == 'error':
                        severity = IssueSeverity.HIGH
                    
                    issues.append(CodeIssue(
                        line=int(line_num),
                        column=int(col),
                        message=message,
                        severity=severity,
                        category=CritiqueCategory.TYPING,
                        filename=filename
                    ))
        
        return issues

    async def _run_bandit(self, file_path: str, filename: str) -> List[CodeIssue]:
        """
        Run Bandit security analysis
        
        Args:
            file_path: Path to file
            filename: Original filename
            
        Returns:
            List of bandit issues
        """
        issues = []
        
        if not file_path or not os.path.exists(file_path):
            return issues
            
        process = await asyncio.create_subprocess_exec(
            'bandit',
            '-f', 'json',
            '-n', '0',
            file_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if stdout:
            decoded_output = stdout.decode()
            if decoded_output.strip():
                result = json.loads(decoded_output) if decoded_output.startswith('{') else []
                for issue in result.get('results', []):
                    severity = IssueSeverity.MEDIUM
                    if issue.get('issue_severity') == 'HIGH':
                        severity = IssueSeverity.HIGH
                    elif issue.get('issue_severity') == 'LOW':
                        severity = IssueSeverity.LOW
                    
                    issues.append(CodeIssue(
                        line=issue.get('line_number', 1),
                        column=1,
                        message=f"Security issue: {issue.get('issue_text', '')} (CWE: {issue.get('cwe', 'N/A')})",
                        severity=severity,
                        category=CritiqueCategory.SECURITY,
                        filename=filename
                    ))
        
        return issues

class ASTAnalyzer:
    """AST Analysis Helper for complexity, imports, and definitions"""
    
    def __init__(self, max_complexity: int, max_function_length: int, max_arguments: int):
        self.max_complexity = max_complexity
        self.max_function_length = max_function_length
        self.max_arguments = max_arguments
    
    def analyze_complexity(self, tree: ast.AST, filename: str) -> List[CodeIssue]:
        """Analyze code complexity"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Check cyclomatic complexity
                complexity = self._calculate_complexity(node)
                if complexity > self.max_complexity:
                    issues.append(CodeIssue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' has high cyclomatic complexity ({complexity})",
                        severity=IssueSeverity.MEDIUM,
                        category=CritiqueCategory.COMPLEXITY,
                        filename=filename
                    ))
                
                # Check function length
                if hasattr(node, 'end_lineno') and node.end_lineno - node.lineno > self.max_function_length:
                    issues.append(CodeIssue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' is too long ({node.end_lineno - node.lineno} lines)",
                        severity=IssueSeverity.MEDIUM,
                        category=CritiqueCategory.COMPLEXITY,
                        filename=filename
                    ))
                
                # Check number of arguments
                args_count = len([arg for arg in node.args.args if arg.arg != 'self'])
                if args_count > self.max_arguments:
                    issues.append(CodeIssue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' has too many arguments ({args_count})",
                        severity=IssueSeverity.LOW,
                        category=CritiqueCategory.COMPLEXITY,
                        filename=filename
                    ))
        
        return issues
    
    def analyze_imports(self, tree: ast.AST, filename: str) -> List[CodeIssue]:
        """Analyze imports"""
        issues = []
        imports = set()
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                # Check for duplicate imports
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in imports:
                            issues.append(CodeIssue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Duplicate import of '{alias.name}'",
                                severity=IssueSeverity.LOW,
                                category=CritiqueCategory.IMPORTS,
                                filename=filename
                            ))
                        imports.add(alias.name)
                
                # Check for relative imports
                if isinstance(node, ast.ImportFrom) and node.level > 0:
                    issues.append(CodeIssue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="Avoid relative imports",
                        severity=IssueSeverity.LOW,
                        category=CritiqueCategory.IMPORTS,
                        filename=filename
                    ))
        
        return issues
    
    def analyze_definitions(self, tree: ast.AST, filename: str) -> List[CodeIssue]:
        """Analyze function and class definitions"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                # Check for missing docstrings
                if not ast.get_docstring(node):
                    issues.append(CodeIssue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Missing docstring in {node.__class__.__name__.lower()} '{node.name}'",
                        severity=IssueSeverity.LOW,
                        category=CritiqueCategory.STRUCTURE,
                        filename=filename
                    ))
        
        return issues
    
    def _calculate_complexity(self, node: ast.AST) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
    
    def _get_parent_function(self, node: ast.AST) -> Optional[ast.FunctionDef]:
        """Get the parent function of a node"""
        parent = getattr(node, 'parent', None)
        while parent:
            if isinstance(parent, ast.FunctionDef):
                return parent
            parent = getattr(parent, 'parent', None)
        return None
    
    def _get_parent_if(self, node: ast.AST) -> Optional[ast.If]:
        """Get the parent if statement of a node"""
        parent = getattr(node, 'parent', None)
        while parent:
            if isinstance(parent, ast.If):
                return parent
            parent = getattr(parent, 'parent', None)
        return None
