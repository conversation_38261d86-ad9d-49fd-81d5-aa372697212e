"""
JavaScript Analyzer - Specialized analyzer for JavaScript/TypeScript code
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set

from .base_analyzer import BaseAnalyzer
from ..models.code_issue import CodeIssue, IssueSeverity, IssueCategory
from ..models.analysis_report import AnalysisReport


class JavaScriptAnalyzer(BaseAnalyzer):
    """Specialized analyzer for JavaScript and TypeScript code"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize JavaScript analyzer"""
        super().__init__(config)
        
        # JavaScript specific patterns
        self.var_patterns = [
            r'\bvar\s+\w+',
            r'==\s*',
            r'!=\s*'
        ]
        
        self.security_patterns = [
            r'eval\s*\(',
            r'innerHTML\s*=',
            r'document\.write\s*\(',
            r'setTimeout\s*\(\s*["\']',
            r'setInterval\s*\(\s*["\']'
        ]
        
        self.async_patterns = [
            r'new\s+Promise\s*\(',
            r'\.then\s*\(',
            r'\.catch\s*\(',
            r'async\s+function',
            r'await\s+'
        ]
        
        # Configuration
        self.check_es6_features = config.get("check_es6_features", True)
        self.check_security = config.get("check_security", True)
        self.check_async_patterns = config.get("check_async_patterns", True)
    
    async def analyze_code(
        self, 
        content: str, 
        filename: str, 
        categories: List[str],
        **kwargs
    ) -> List[CodeIssue]:
        """
        Analyze JavaScript/TypeScript code and return issues
        
        Args:
            content: Code content to analyze
            filename: Name of the file being analyzed
            categories: Categories to analyze for
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            List of code issues
        """
        self.logger.info(f"Analyzing JavaScript code in {filename}")
        
        # Filter categories
        supported_categories = {"javascript", "typescript", "security", "style", "async"}
        filtered_categories = self._filter_categories(categories, supported_categories)
        
        if not filtered_categories:
            self.logger.info("No relevant categories to analyze for JavaScript")
            return []
        
        issues = []
        
        # ES6+ best practices
        if self.check_es6_features and ("style" in filtered_categories or "javascript" in filtered_categories):
            es6_issues = self._check_es6_practices(content, filename)
            issues.extend(es6_issues)
        
        # Security analysis
        if self.check_security and ("security" in filtered_categories or "javascript" in filtered_categories):
            security_issues = self._check_security_issues(content, filename)
            issues.extend(security_issues)
        
        # Async/await patterns
        if self.check_async_patterns and ("async" in filtered_categories or "javascript" in filtered_categories):
            async_issues = self._check_async_patterns(content, filename)
            issues.extend(async_issues)
        
        return issues
    
    async def analyze(self, content: str, filename: str) -> AnalysisReport:
        """Analyze the given code content."""
        # TODO: Implement actual analysis
        return AnalysisReport(
            issues=[],
            summary="Dummy report"
        )
    
    def _check_es6_practices(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for ES6+ best practices"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for var usage
            if re.search(r'\bvar\s+\w+', line):
                issues.append(self._create_issue(
                    id_prefix="js",
                    id_value="var-usage",
                    title="Use of 'var' keyword",
                    description="Use 'let' or 'const' instead of 'var' for better scoping",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.STYLE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Replace 'var' with 'let' for mutable variables",
                        "Replace 'var' with 'const' for immutable variables"
                    ],
                    rule_id="js-no-var",
                    tool_source="javascript_analyzer"
                ))
            
            # Check for loose equality
            if re.search(r'==\s*[^=]', line) or re.search(r'!=\s*[^=]', line):
                issues.append(self._create_issue(
                    id_prefix="js",
                    id_value="loose-equality",
                    title="Loose equality comparison",
                    description="Use strict equality (=== or !==) instead of loose equality",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.STYLE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Replace '==' with '==='",
                        "Replace '!=' with '!=='"
                    ],
                    rule_id="js-strict-equality",
                    tool_source="javascript_analyzer"
                ))
        
        return issues
    
    def _check_security_issues(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for security vulnerabilities"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for eval usage
            if re.search(r'eval\s*\(', line):
                issues.append(self._create_issue(
                    id_prefix="js",
                    id_value="eval-usage",
                    title="Use of eval() function",
                    description="eval() can execute arbitrary code and is a security risk",
                    severity=IssueSeverity.HIGH,
                    category=IssueCategory.SECURITY,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Use JSON.parse() for parsing JSON",
                        "Use Function constructor if dynamic code execution is necessary"
                    ],
                    rule_id="js-no-eval",
                    tool_source="javascript_analyzer"
                ))
            
            # Check for innerHTML usage
            if re.search(r'innerHTML\s*=', line):
                issues.append(self._create_issue(
                    id_prefix="js",
                    id_value="innerHTML-xss",
                    title="Potential XSS vulnerability",
                    description="innerHTML can lead to XSS attacks if user input is not sanitized",
                    severity=IssueSeverity.HIGH,
                    category=IssueCategory.SECURITY,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Use textContent for plain text",
                        "Sanitize HTML content before setting innerHTML"
                    ],
                    rule_id="js-no-innerHTML",
                    tool_source="javascript_analyzer"
                ))
            
            # Check for setTimeout/setInterval with string
            if re.search(r'setTimeout\s*\(\s*["\']', line) or re.search(r'setInterval\s*\(\s*["\']', line):
                issues.append(self._create_issue(
                    id_prefix="js",
                    id_value="timer-string",
                    title="Timer function with string argument",
                    description="Passing strings to setTimeout/setInterval is equivalent to eval",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.SECURITY,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Pass a function reference instead of a string",
                        "Use arrow functions for inline code"
                    ],
                    rule_id="js-no-timer-string",
                    tool_source="javascript_analyzer"
                ))
        
        return issues
    
    def _check_async_patterns(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for async/await patterns and Promise handling"""
        issues = []
        lines = content.split('\n')
        
        # Track Promise chains and async functions
        has_promise_chain = False
        has_async_function = False
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for Promise chains
            if re.search(r'\.then\s*\(', line):
                has_promise_chain = True
            
            # Check for async functions
            if re.search(r'async\s+function', line):
                has_async_function = True
            
            # Check for unhandled Promise rejections
            if re.search(r'new\s+Promise\s*\(', line) and not re.search(r'\.catch\s*\(', content[content.find(line):]):
                issues.append(self._create_issue(
                    id_prefix="js",
                    id_value="unhandled-promise",
                    title="Unhandled Promise rejection",
                    description="Promise should have error handling with .catch() or try/catch",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.ASYNC,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Add .catch() to handle Promise rejections",
                        "Use try/catch with async/await"
                    ],
                    rule_id="js-promise-handling",
                    tool_source="javascript_analyzer"
                ))
            
            # Check for missing await
            if re.search(r'async\s+function', line):
                # Look for Promise-returning function calls without await
                function_body_start = i
                brace_count = 0
                for j in range(i, min(i + 50, len(lines))):  # Check next 50 lines
                    if '{' in lines[j]:
                        brace_count += lines[j].count('{')
                    if '}' in lines[j]:
                        brace_count -= lines[j].count('}')
                        if brace_count == 0:
                            break
                    
                    # Check for function calls that likely return Promises
                    if re.search(r'fetch\s*\(|axios\.|\.get\(|\.post\(', lines[j]) and not re.search(r'await\s+', lines[j]):
                        issues.append(self._create_issue(
                            id_prefix="js",
                            id_value="missing-await",
                            title="Missing await keyword",
                            description="Async function call should use await keyword",
                            severity=IssueSeverity.MEDIUM,
                            category=IssueCategory.ASYNC,
                            file_path=filename,
                            line_start=j + 1,
                            code_snippet=self._extract_code_snippet(content, j, j, context_lines=2),
                            fix_suggestions=[
                                "Add 'await' before the async function call",
                                "Handle the Promise with .then() if await is not appropriate"
                            ],
                            rule_id="js-missing-await",
                            tool_source="javascript_analyzer"
                        ))
        
        return issues
