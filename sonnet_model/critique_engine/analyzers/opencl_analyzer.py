"""
OpenCL Analyzer - Specialized analyzer for OpenCL code
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set

from .base_analyzer import BaseAnalyzer
from ..models.code_issue import CodeIssue, IssueSeverity, IssueCategory


class OpenCLAnalyzer(BaseAnalyzer):
    """Specialized analyzer for OpenCL code embedded in Python"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize OpenCL analyzer"""
        super().__init__(config)
        
        # OpenCL-specific patterns
        self.kernel_pattern = re.compile(r'__kernel\s+void\s+(\w+)')
        self.barrier_pattern = re.compile(r'barrier\s*\(')
        self.local_mem_pattern = re.compile(r'__local\s+(\w+)\s+(\w+)')
        self.global_mem_pattern = re.compile(r'__global\s+(\w+)\s+(\w+)')
        self.atomic_pattern = re.compile(r'atomic_\w+\s*\(')
        self.get_global_id_pattern = re.compile(r'get_global_id\s*\(\s*(\d+)\s*\)')
        self.get_local_id_pattern = re.compile(r'get_local_id\s*\(\s*(\d+)\s*\)')
        self.get_group_id_pattern = re.compile(r'get_group_id\s*\(\s*(\d+)\s*\)')
        
        # Common OpenCL issues
        self.race_condition_patterns = [
            (r'atomic_\w+\s*\([^)]*\)', r'[^atomic_]\w+\s*\[[^\]]*\]\s*[=+-/*]'),
            (r'barrier\s*\([^)]*\)', r'if\s*\([^)]*\)\s*\{\s*barrier')
        ]
        
        # Configuration
        self.max_work_group_size = config.get("max_work_group_size", 256)
        self.check_memory_coalescing = config.get("check_memory_coalescing", True)
        self.check_bank_conflicts = config.get("check_bank_conflicts", True)
    
    async def analyze_code(
        self, 
        content: str, 
        filename: str, 
        categories: List[str],
        **kwargs
    ) -> List[CodeIssue]:
        """
        Analyze OpenCL code and return issues
        
        Args:
            content: Code content to analyze
            filename: Name of the file being analyzed
            categories: Categories to analyze for
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            List of code issues
        """
        self.logger.info(f"Analyzing OpenCL code in {filename}")
        
        # Filter categories
        supported_categories = {"opencl", "performance", "correctness", "security"}
        filtered_categories = self._filter_categories(categories, supported_categories)
        
        if not filtered_categories:
            self.logger.info("No relevant categories to analyze for OpenCL")
            return []
        
        # Extract OpenCL kernels
        kernels = self._extract_kernels(content)
        
        if not kernels:
            self.logger.info("No OpenCL kernels found in the code")
            return []
        
        # Analyze each kernel
        issues = []
        for kernel_name, kernel_code, line_offset in kernels:
            kernel_issues = self._analyze_kernel(
                kernel_name, 
                kernel_code, 
                line_offset, 
                filename, 
                filtered_categories
            )
            issues.extend(kernel_issues)
        
        return issues
    
    def _extract_kernels(self, content: str) -> List[tuple]:
        """
        Extract OpenCL kernels from code
        
        Args:
            content: Code content
            
        Returns:
            List of tuples (kernel_name, kernel_code, line_offset)
        """
        kernels = []
        
        # Look for kernel definitions in string literals
        lines = content.split('\n')
        in_string = False
        string_start_line = 0
        string_content = []
        
        for i, line in enumerate(lines):
            # Simple string detection (not handling all edge cases)
            if '"""' in line or "'''" in line:
                if not in_string:
                    in_string = True
                    string_start_line = i
                    string_content = [line]
                else:
                    in_string = False
                    string_content.append(line)
                    
                    # Join the string content
                    string_text = '\n'.join(string_content)
                    
                    # Look for kernel definitions
                    kernel_matches = self.kernel_pattern.findall(string_text)
                    if kernel_matches:
                        for kernel_name in kernel_matches:
                            # Extract the kernel code
                            kernel_start = string_text.find(f"__kernel void {kernel_name}")
                            if kernel_start >= 0:
                                # Find the end of the kernel (closing brace)
                                brace_count = 0
                                kernel_end = kernel_start
                                for j, char in enumerate(string_text[kernel_start:]):
                                    if char == '{':
                                        brace_count += 1
                                    elif char == '}':
                                        brace_count -= 1
                                        if brace_count == 0:
                                            kernel_end = kernel_start + j + 1
                                            break
                                
                                kernel_code = string_text[kernel_start:kernel_end]
                                kernels.append((kernel_name, kernel_code, string_start_line))
            
            elif in_string:
                string_content.append(line)
        
        return kernels
    
    def _analyze_kernel(
        self, 
        kernel_name: str, 
        kernel_code: str, 
        line_offset: int, 
        filename: str, 
        categories: List[str]
    ) -> List[CodeIssue]:
        """
        Analyze an OpenCL kernel
        
        Args:
            kernel_name: Name of the kernel
            kernel_code: Kernel code
            line_offset: Line offset in the original file
            filename: Name of the file
            categories: Categories to analyze for
            
        Returns:
            List of code issues
        """
        issues = []
        
        # Check for barrier synchronization issues
        if "correctness" in categories or "opencl" in categories:
            barrier_issues = self._check_barrier_sync(kernel_name, kernel_code, line_offset, filename)
            issues.extend(barrier_issues)
        
        # Check for memory access patterns
        if "performance" in categories or "opencl" in categories:
            if self.check_memory_coalescing:
                coalescing_issues = self._check_memory_coalescing(kernel_name, kernel_code, line_offset, filename)
                issues.extend(coalescing_issues)
            
            if self.check_bank_conflicts:
                bank_conflict_issues = self._check_bank_conflicts(kernel_name, kernel_code, line_offset, filename)
                issues.extend(bank_conflict_issues)
        
        # Check for race conditions
        if "correctness" in categories or "security" in categories or "opencl" in categories:
            race_issues = self._check_race_conditions(kernel_name, kernel_code, line_offset, filename)
            issues.extend(race_issues)
        
        # Check for work group size
        if "performance" in categories or "opencl" in categories:
            wg_issues = self._check_work_group_size(kernel_name, kernel_code, line_offset, filename)
            issues.extend(wg_issues)
        
        return issues
    
    def _check_barrier_sync(
        self, 
        kernel_name: str, 
        kernel_code: str, 
        line_offset: int, 
        filename: str
    ) -> List[CodeIssue]:
        """Check for barrier synchronization issues"""
        issues = []
        
        # Check for barriers inside conditionals
        lines = kernel_code.split('\n')
        in_conditional = False
        conditional_start = 0
        
        for i, line in enumerate(lines):
            line_num = i + line_offset + 1  # 1-indexed line number
            
            # Check for conditional start
            if re.search(r'if\s*\(', line) or re.search(r'else\s*\{', line):
                in_conditional = True
                conditional_start = line_num
            
            # Check for conditional end
            elif in_conditional and '}' in line:
                in_conditional = False
            
            # Check for barrier in conditional
            if in_conditional and self.barrier_pattern.search(line):
                issues.append(self._create_issue(
                    id_prefix="opencl",
                    id_value="barrier-in-conditional",
                    title="Barrier inside conditional",
                    description=(
                        f"Barrier synchronization inside conditional blocks can lead to "
                        f"undefined behavior if some work-items take the branch and others don't."
                    ),
                    severity=IssueSeverity.HIGH,
                    category=IssueCategory.OPENCL,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(kernel_code, i, i, context_lines=2),
                    fix_suggestions=[
                        "Move the barrier outside of the conditional block",
                        "Ensure all work-items execute the barrier by using a predicate variable"
                    ],
                    rule_id="opencl-barrier-sync",
                    tool_source="opencl_analyzer"
                ))
        
        return issues
    
    def _check_memory_coalescing(
        self, 
        kernel_name: str, 
        kernel_code: str, 
        line_offset: int, 
        filename: str
    ) -> List[CodeIssue]:
        """Check for memory coalescing issues"""
        issues = []
        
        # Check for non-coalesced global memory access
        lines = kernel_code.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + line_offset + 1  # 1-indexed line number
            
            # Look for global memory access with get_local_id
            if self.global_mem_pattern.search(line) and self.get_local_id_pattern.search(line):
                issues.append(self._create_issue(
                    id_prefix="opencl",
                    id_value="non-coalesced-memory",
                    title="Non-coalesced memory access",
                    description=(
                        f"Global memory access based on local ID can lead to non-coalesced "
                        f"memory access patterns, reducing performance."
                    ),
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.PERFORMANCE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(kernel_code, i, i, context_lines=2),
                    fix_suggestions=[
                        "Restructure memory access to use global ID for coalesced access",
                        "Consider using local memory as a cache for non-coalesced patterns"
                    ],
                    rule_id="opencl-memory-coalescing",
                    tool_source="opencl_analyzer"
                ))
        
        return issues
    
    def _check_bank_conflicts(
        self, 
        kernel_name: str, 
        kernel_code: str, 
        line_offset: int, 
        filename: str
    ) -> List[CodeIssue]:
        """Check for local memory bank conflicts"""
        issues = []
        
        # Check for potential bank conflicts in local memory
        lines = kernel_code.split('\n')
        local_vars = []
        
        # First pass: identify local memory variables
        for line in lines:
            local_match = self.local_mem_pattern.search(line)
            if local_match:
                local_vars.append(local_match.group(2))
        
        # Second pass: check for bank conflicts
        for i, line in enumerate(lines):
            line_num = i + line_offset + 1  # 1-indexed line number
            
            for var in local_vars:
                # Look for array access with stride
                pattern = rf'{var}\s*\[\s*(\w+)\s*\*\s*(\w+)\s*\]'
                if re.search(pattern, line):
                    issues.append(self._create_issue(
                        id_prefix="opencl",
                        id_value="bank-conflict",
                        title="Potential local memory bank conflict",
                        description=(
                            f"Access to local memory array '{var}' with stride pattern "
                            f"may cause bank conflicts, reducing performance."
                        ),
                        severity=IssueSeverity.MEDIUM,
                        category=IssueCategory.PERFORMANCE,
                        file_path=filename,
                        line_start=line_num,
                        code_snippet=self._extract_code_snippet(kernel_code, i, i, context_lines=2),
                        fix_suggestions=[
                            "Pad the local memory array to avoid conflicts",
                            "Restructure memory access pattern to avoid strided access"
                        ],
                        rule_id="opencl-bank-conflict",
                        tool_source="opencl_analyzer"
                    ))
        
        return issues
    
    def _check_race_conditions(
        self, 
        kernel_name: str, 
        kernel_code: str, 
        line_offset: int, 
        filename: str
    ) -> List[CodeIssue]:
        """Check for potential race conditions"""
        issues = []
        
        # Check for non-atomic operations on shared data
        lines = kernel_code.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + line_offset + 1  # 1-indexed line number
            
            # Look for array writes without atomic operations
            if (re.search(r'global\s+\w+\s*\*', kernel_code) and  # Has global pointer
                re.search(r'\[\s*get_global_id', line) and        # Indexed by global ID
                re.search(r'[=+\-*/]', line) and                  # Has assignment or operation
                not re.search(r'atomic_', line)):                 # Not using atomic
                
                issues.append(self._create_issue(
                    id_prefix="opencl",
                    id_value="race-condition",
                    title="Potential race condition",
                    description=(
                        f"Multiple work-items may write to the same global memory location "
                        f"without atomic operations, causing a race condition."
                    ),
                    severity=IssueSeverity.HIGH,
                    category=IssueCategory.CORRECTNESS,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(kernel_code, i, i, context_lines=2),
                    fix_suggestions=[
                        "Use atomic operations for shared memory updates",
                        "Restructure algorithm to avoid concurrent writes to the same location"
                    ],
                    rule_id="opencl-race-condition",
                    tool_source="opencl_analyzer"
                ))
        
        return issues
    
    def _check_work_group_size(
        self, 
        kernel_name: str, 
        kernel_code: str, 
        line_offset: int, 
        filename: str
    ) -> List[CodeIssue]:
        """Check for work group size issues"""
        issues = []
        
        # Look for explicit work group size
        wg_size_match = re.search(r'get_local_size\s*\(\s*\d+\s*\)\s*==\s*(\d+)', kernel_code)
        if wg_size_match:
            size = int(wg_size_match.group(1))
            if size > self.max_work_group_size:
                line_num = kernel_code.find(wg_size_match.group(0)) // 40 + line_offset + 1
                
                issues.append(self._create_issue(
                    id_prefix="opencl",
                    id_value="large-work-group",
                    title="Large work group size",
                    description=(
                        f"Work group size {size} exceeds recommended maximum of "
                        f"{self.max_work_group_size}, which may not be supported on all devices."
                    ),
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.COMPATIBILITY,
                    file_path=filename,
                    line_start=line_num,
                    fix_suggestions=[
                        f"Reduce work group size to {self.max_work_group_size} or less",
                        "Query device for max work group size at runtime"
                    ],
                    rule_id="opencl-work-group-size",
                    tool_source="opencl_analyzer"
                ))
        
        # Check for local memory usage
        local_mem_count = len(self.local_mem_pattern.findall(kernel_code))
        if local_mem_count > 5:  # Arbitrary threshold
            issues.append(self._create_issue(
                id_prefix="opencl",
                id_value="excessive-local-mem",
                title="Excessive local memory usage",
                description=(
                    f"Kernel uses {local_mem_count} local memory variables, which may "
                    f"exceed available local memory on some devices."
                ),
                severity=IssueSeverity.LOW,
                category=IssueCategory.PERFORMANCE,
                file_path=filename,
                line_start=line_offset + 1,
                fix_suggestions=[
                    "Reduce local memory usage by reusing buffers",
                    "Split kernel into multiple kernels with less local memory usage"
                ],
                rule_id="opencl-local-mem-usage",
                tool_source="opencl_analyzer"
            ))
        
        return issues
