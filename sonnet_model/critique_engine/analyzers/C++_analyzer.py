"""
C++ Analyzer - Specialized analyzer for C++ code
"""

import re
import logging
from typing import Dict, List, Any, Optional, Set

from .base_analyzer import BaseAnalyzer
from ..models.code_issue import CodeIssue, IssueSeverity, IssueCategory


class CppAnalyzer(BaseAnalyzer):
    """Specialized analyzer for C++ code"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize C++ analyzer"""
        super().__init__(config)
        
        # C++ specific patterns
        self.memory_leak_patterns = [
            r'new\s+\w+',
            r'malloc\s*\(',
            r'calloc\s*\(',
            r'realloc\s*\('
        ]
        
        self.delete_patterns = [
            r'delete\s+\w+',
            r'delete\[\]\s*\w+',
            r'free\s*\('
        ]
        
        self.unsafe_patterns = [
            r'strcpy\s*\(',
            r'strcat\s*\(',
            r'sprintf\s*\(',
            r'gets\s*\('
        ]
        
        self.modern_cpp_violations = [
            (r'NULL', 'Use nullptr instead of NULL'),
            (r'typedef\s+struct', 'Use struct directly in modern C++'),
            (r'#define\s+\w+\s+\d+', 'Use const/constexpr instead of #define')
        ]
        
        # Configuration
        self.check_memory_safety = config.get("check_memory_safety", True)
        self.check_modern_cpp = config.get("check_modern_cpp", True)
        self.check_performance = config.get("check_performance", True)
    
    async def analyze_code(
        self, 
        content: str, 
        filename: str, 
        categories: List[str],
        **kwargs
    ) -> List[CodeIssue]:
        """
        Analyze C++ code and return issues
        
        Args:
            content: Code content to analyze
            filename: Name of the file being analyzed
            categories: Categories to analyze for
            **kwargs: Additional analyzer-specific parameters
            
        Returns:
            List of code issues
        """
        self.logger.info(f"Analyzing C++ code in {filename}")
        
        # Filter categories
        supported_categories = {"cpp", "memory", "security", "performance", "style"}
        filtered_categories = self._filter_categories(categories, supported_categories)
        
        if not filtered_categories:
            self.logger.info("No relevant categories to analyze for C++")
            return []
        
        issues = []
        
        # Memory safety analysis
        if self.check_memory_safety and ("memory" in filtered_categories or "cpp" in filtered_categories):
            memory_issues = self._check_memory_safety(content, filename)
            issues.extend(memory_issues)
        
        # Security analysis
        if "security" in filtered_categories or "cpp" in filtered_categories:
            security_issues = self._check_security_issues(content, filename)
            issues.extend(security_issues)
        
        # Modern C++ best practices
        if self.check_modern_cpp and ("style" in filtered_categories or "cpp" in filtered_categories):
            modern_issues = self._check_modern_cpp(content, filename)
            issues.extend(modern_issues)
        
        # Performance analysis
        if self.check_performance and ("performance" in filtered_categories or "cpp" in filtered_categories):
            performance_issues = self._check_performance_issues(content, filename)
            issues.extend(performance_issues)
        
        return issues
    
    def _check_memory_safety(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for memory safety issues"""
        issues = []
        lines = content.split('\n')
        
        # Track allocations and deallocations
        allocations = {}
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for memory allocations
            for pattern in self.memory_leak_patterns:
                matches = re.finditer(pattern, line)
                for match in matches:
                    var_match = re.search(r'(\w+)\s*=.*' + pattern, line)
                    if var_match:
                        var_name = var_match.group(1)
                        allocations[var_name] = line_num
                    
                    # Check if allocation result is checked
                    if 'new' in line and 'if' not in line and 'assert' not in line:
                        issues.append(self._create_issue(
                            id_prefix="cpp",
                            id_value="unchecked-allocation",
                            title="Unchecked memory allocation",
                            description="Memory allocation result should be checked for null pointer",
                            severity=IssueSeverity.MEDIUM,
                            category=IssueCategory.MEMORY,
                            file_path=filename,
                            line_start=line_num,
                            code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                            fix_suggestions=[
                                "Check allocation result for null pointer",
                                "Use smart pointers instead of raw pointers"
                            ],
                            rule_id="cpp-unchecked-allocation",
                            tool_source="cpp_analyzer"
                        ))
            
            # Check for double delete
            for pattern in self.delete_patterns:
                if re.search(pattern, line):
                    var_match = re.search(r'delete\s+(\w+)', line)
                    if var_match:
                        var_name = var_match.group(1)
                        if var_name in allocations:
                            del allocations[var_name]
                        else:
                            issues.append(self._create_issue(
                                id_prefix="cpp",
                                id_value="potential-double-delete",
                                title="Potential double delete",
                                description=f"Variable '{var_name}' may be deleted multiple times",
                                severity=IssueSeverity.HIGH,
                                category=IssueCategory.MEMORY,
                                file_path=filename,
                                line_start=line_num,
                                code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                                fix_suggestions=[
                                    "Set pointer to nullptr after delete",
                                    "Use smart pointers to avoid manual memory management"
                                ],
                                rule_id="cpp-double-delete",
                                tool_source="cpp_analyzer"
                            ))
        
        # Check for memory leaks (allocations without deallocations)
        for var_name, alloc_line in allocations.items():
            issues.append(self._create_issue(
                id_prefix="cpp",
                id_value="memory-leak",
                title="Potential memory leak",
                description=f"Variable '{var_name}' allocated but never freed",
                severity=IssueSeverity.HIGH,
                category=IssueCategory.MEMORY,
                file_path=filename,
                line_start=alloc_line,
                fix_suggestions=[
                    "Add corresponding delete/free statement",
                    "Use smart pointers for automatic memory management"
                ],
                rule_id="cpp-memory-leak",
                tool_source="cpp_analyzer"
            ))
        
        return issues
    
    def _check_security_issues(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for security vulnerabilities"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for unsafe functions
            for pattern in self.unsafe_patterns:
                if re.search(pattern, line):
                    function_name = pattern.split('\\')[0]
                    safe_alternatives = {
                        'strcpy': 'strncpy or std::string',
                        'strcat': 'strncat or std::string',
                        'sprintf': 'snprintf',
                        'gets': 'fgets'
                    }
                    
                    issues.append(self._create_issue(
                        id_prefix="cpp",
                        id_value="unsafe-function",
                        title=f"Unsafe function: {function_name}",
                        description=f"Function {function_name} is unsafe and can cause buffer overflows",
                        severity=IssueSeverity.HIGH,
                        category=IssueCategory.SECURITY,
                        file_path=filename,
                        line_start=line_num,
                        code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                        fix_suggestions=[
                            f"Replace {function_name} with {safe_alternatives.get(function_name, 'safer alternative')}",
                            "Use std::string for string operations"
                        ],
                        rule_id="cpp-unsafe-function",
                        tool_source="cpp_analyzer"
                    ))
            
            # Check for buffer overflow potential
            if re.search(r'char\s+\w+\[\d+\]', line) and re.search(r'scanf|gets', content):
                issues.append(self._create_issue(
                    id_prefix="cpp",
                    id_value="buffer-overflow-risk",
                    title="Buffer overflow risk",
                    description="Fixed-size buffer used with unsafe input functions",
                    severity=IssueSeverity.HIGH,
                    category=IssueCategory.SECURITY,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Use std::string instead of char arrays",
                        "Use safe input functions with size limits"
                    ],
                    rule_id="cpp-buffer-overflow",
                    tool_source="cpp_analyzer"
                ))
        
        return issues
    
    def _check_modern_cpp(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for modern C++ best practices"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for modern C++ violations
            for pattern, suggestion in self.modern_cpp_violations:
                if re.search(pattern, line):
                    issues.append(self._create_issue(
                        id_prefix="cpp",
                        id_value="modern-cpp-violation",
                        title="Modern C++ best practice violation",
                        description=suggestion,
                        severity=IssueSeverity.LOW,
                        category=IssueCategory.STYLE,
                        file_path=filename,
                        line_start=line_num,
                        code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                        fix_suggestions=[suggestion],
                        rule_id="cpp-modern-practices",
                        tool_source="cpp_analyzer"
                    ))
            
            # Check for raw pointers when smart pointers could be used
            if re.search(r'\w+\s*\*\s*\w+\s*=\s*new', line):
                issues.append(self._create_issue(
                    id_prefix="cpp",
                    id_value="raw-pointer-usage",
                    title="Raw pointer usage",
                    description="Consider using smart pointers instead of raw pointers",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.STYLE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Use std::unique_ptr for exclusive ownership",
                        "Use std::shared_ptr for shared ownership"
                    ],
                    rule_id="cpp-smart-pointers",
                    tool_source="cpp_analyzer"
                ))
        
        return issues
    
    def _check_performance_issues(self, content: str, filename: str) -> List[CodeIssue]:
        """Check for performance issues"""
        issues = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # Check for inefficient string concatenation
            if re.search(r'\w+\s*\+=?\s*\w+\s*\+\s*\w+', line) and 'string' in line:
                issues.append(self._create_issue(
                    id_prefix="cpp",
                    id_value="inefficient-string-concat",
                    title="Inefficient string concatenation",
                    description="String concatenation in loop can be inefficient",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.PERFORMANCE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Use std::stringstream for multiple concatenations",
                        "Reserve string capacity if size is known"
                    ],
                    rule_id="cpp-string-performance",
                    tool_source="cpp_analyzer"
                ))
            
            # Check for pass by value of large objects
            if re.search(r'void\s+\w+\s*\(\s*std::(vector|string|map)\s+\w+\s*\)', line):
                issues.append(self._create_issue(
                    id_prefix="cpp",
                    id_value="pass-by-value",
                    title="Pass by value of large object",
                    description="Large objects should be passed by const reference",
                    severity=IssueSeverity.MEDIUM,
                    category=IssueCategory.PERFORMANCE,
                    file_path=filename,
                    line_start=line_num,
                    code_snippet=self._extract_code_snippet(content, i, i, context_lines=2),
                    fix_suggestions=[
                        "Pass by const reference: const std::vector<T>&",
                        "Use move semantics if object will be modified"
                    ],
                    rule_id="cpp-pass-by-reference",
                    tool_source="cpp_analyzer"
                ))
        
        return issues
