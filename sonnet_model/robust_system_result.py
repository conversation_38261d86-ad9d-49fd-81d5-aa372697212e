# Generated by Robust Iterative System
# Iterations: 1
# Final Quality: 5.0/10

import jwt
import bcrypt
from flask import Flask, request, jsonify, g
from datetime import datetime, timedelta
import sqlite3
import re
import logging

app = Flask(__name__)

# Configuration
JWT_SECRET = 'your-256-bit-secret'  # Replace with a strong secret key
JWT_ALGORITHM = 'HS256'
DB_NAME = 'users.db'
LOG_LEVEL = logging.INFO
LOG_FILE = 'security.log'
RATE_LIMIT_PER_MINUTE = 10

# Setup Logging
logging.basicConfig(level=LOG_LEVEL, filename=LOG_FILE, filemode='a', format='%(asctime)s - %(levelname)s - %(message)s')

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(DB_NAME)
        create_user_table(db)
    return db

def create_user_table(db):
    cursor = db.cursor()
    cursor.execute('''CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY, username TEXT UNIQUE, password TEXT, salt TEXT)''')
    db.commit()

def hash_password(password, salt=None):
    if not salt:
        salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed, salt

def validate_user_input(data):
    if not data['username'] or not re.match("^[a-zA-Z0-9]+$", data['username']):
        return False, "Invalid username"
    if len(data['password']) < 8:
        return False, "Password must be at least 8 characters long"
    return True, ""

def create_token(user_id):
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(minutes=30)
    }
    token = jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return token

@app.before_request
def check_rate_limit():
    if request.method == 'POST' and request.path == '/login':
        ip = request.remote_addr
        current_time = datetime.now()
        if not hasattr(g, 'login_attempts'):
            g.login_attempts = {}
        if not g.login_attempts.get(ip):
            g.login_attempts[ip] = []
        attempts = g.login_attempts[ip]
        if len(attempts) >= RATE_LIMIT_PER_MINUTE:
            oldest_attempt = attempts[0]
            if (current_time - oldest_attempt).total_seconds() < 60:
                return jsonify({'error': 'Too many login attempts. Please try again later.'}), 429
        g.login_attempts[ip].append(current_time)
        if len(g.login_attempts[ip]) > RATE_LIMIT_PER_MINUTE:
            g.login_attempts[ip].pop(0)

@app.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    valid, msg = validate_user_input(data)
    if not valid:
        return jsonify({'error': msg}), 400
    hashed_password, salt = hash_password(data['password'])
    db = get_db()
    cursor = db.cursor()
    try:
        cursor.execute("INSERT INTO users (username, password, salt) VALUES (?, ?, ?)", (data['username'], hashed_password.decode('utf-8'), salt.decode('utf-8')))
        db.commit()
        return jsonify({'message': 'User registered successfully'}), 201
    except sqlite3.IntegrityError:
        return jsonify({'error': 'Username already exists'}), 409

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    db = get_db()
    cursor = db.cursor()
    cursor.execute("SELECT * FROM users WHERE username=?", (data['username'],))
    user = cursor.fetchone()
    if not user:
        return jsonify({'error': 'Invalid credentials'}), 401
    hashed_password, salt = hash_password(data['password'], bytes(user[3]))
    if bcrypt.checkpw(data['password'].encode('utf-8'), bytes(user[2])):
        token = create_token(user[0])
        return jsonify({'token': token}), 200
    else:
        return jsonify({'error': 'Invalid credentials'}), 401

@app.route('/protected', methods=['GET'])
def protected():
    token = request.headers.get('Authorization').split(" ")[1]
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        user_id = payload['user_id']
        db = get_db()
        cursor = db.cursor()
        cursor.execute("SELECT * FROM users WHERE id=?", (user_id,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': 'User not found'}), 404
        return jsonify({'message': 'Access granted'}), 200
    except jwt.ExpiredSignatureError:
        return jsonify({'error': 'Token has expired'}), 401
    except jwt.InvalidTokenError:
        return jsonify({'error': 'Invalid token'}), 401

if __name__ == '__main__':
    app.run(debug=True)