"""
Diagnose Feedback Loop Issues

This will analyze why the feedback loop is broken:
1. Test if critique is actually different each time
2. Test if code generator responds to specific feedback
3. Identify where the loop breaks down
4. Create a fixed version
"""

import asyncio
import json
import logging
from typing import Dict, Any
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class FeedbackLoopDiagnostic:
    """Diagnose and fix the feedback loop"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
    async def diagnose_feedback_loop(self):
        """Run comprehensive diagnosis of the feedback loop"""
        
        print("🔍 DIAGNOSING FEEDBACK LOOP ISSUES")
        print("=" * 60)
        
        # Test 1: Is critique actually different for different code?
        await self._test_critique_variation()
        
        # Test 2: Does code generator respond to specific feedback?
        await self._test_generator_responsiveness()
        
        # Test 3: Test the complete loop with tracking
        await self._test_complete_loop_with_tracking()
        
        # Test 4: Create a fixed version
        await self._create_fixed_feedback_loop()
        
    async def _test_critique_variation(self):
        """Test if critique gives different feedback for different code"""
        
        print("\n🧪 TEST 1: Critique Variation")
        print("-" * 40)
        
        test_codes = [
            # Good code
            '''def add(a: int, b: int) -> int:
    """Add two integers and return the result."""
    return a + b''',
            
            # Bad code
            '''def add(a, b):
    return a + b''',
            
            # Buggy code
            '''def add(a, b):
    if a > 0:
        return a + b
    return "error"'''
        ]
        
        for i, code in enumerate(test_codes, 1):
            print(f"\n📋 Testing Code {i}:")
            print(code)
            
            critique = await self._get_critique(code, "Simple addition function")
            
            print(f"📊 Quality Score: {critique.get('quality_score', 'N/A')}")
            print(f"🔍 Issues: {len(critique.get('issues', []))}")
            for issue in critique.get('issues', [])[:2]:
                print(f"   - {issue}")
            
        print("\n✅ Critique variation test completed")
        
    async def _test_generator_responsiveness(self):
        """Test if generator responds to specific feedback"""
        
        print("\n🧪 TEST 2: Generator Responsiveness")
        print("-" * 40)
        
        # Start with bad code
        initial_code = '''def calculate(x, y):
    return x + y'''
        
        # Give specific feedback
        specific_feedback = [
            "Add type hints for parameters and return value",
            "Add comprehensive docstring with Args and Returns",
            "Add input validation for edge cases",
            "Add error handling for invalid inputs"
        ]
        
        print("📝 Initial Code:")
        print(initial_code)
        
        print(f"\n📋 Specific Feedback:")
        for feedback in specific_feedback:
            print(f"   - {feedback}")
        
        # Test if generator incorporates feedback
        improved_code = await self._get_improved_code(initial_code, specific_feedback)
        
        print(f"\n📝 Improved Code:")
        print(improved_code[:300] + "..." if len(improved_code) > 300 else improved_code)
        
        # Check if feedback was addressed
        addressed_feedback = []
        if ":" in improved_code and "->" in improved_code:
            addressed_feedback.append("Type hints added")
        if '"""' in improved_code:
            addressed_feedback.append("Docstring added")
        if "raise" in improved_code or "ValueError" in improved_code:
            addressed_feedback.append("Error handling added")
        
        print(f"\n✅ Feedback Addressed: {len(addressed_feedback)}/{len(specific_feedback)}")
        for feedback in addressed_feedback:
            print(f"   ✓ {feedback}")
        
    async def _test_complete_loop_with_tracking(self):
        """Test complete loop with detailed tracking"""
        
        print("\n🧪 TEST 3: Complete Loop Tracking")
        print("-" * 40)
        
        # Simple task to track
        task_info = {
            "description": "Create a function to validate email addresses",
            "requirements": [
                "Use regex for validation",
                "Return boolean result",
                "Handle edge cases",
                "Add proper documentation"
            ]
        }
        
        current_code = ""
        
        for iteration in range(1, 4):  # Test 3 iterations
            print(f"\n🔄 ITERATION {iteration}")
            print("-" * 20)
            
            # Generate code
            print("🤖 Generating code...")
            new_code = await self._generate_code_with_context(task_info, current_code, iteration)
            
            print(f"📄 Generated {len(new_code)} characters")
            print(f"📝 Code preview: {new_code[:100]}...")
            
            # Critique code
            print("🔍 Critiquing code...")
            critique = await self._get_detailed_critique(new_code, task_info)
            
            print(f"📊 Quality: {critique.get('quality_score', 'N/A')}/10")
            print(f"🔍 Issues: {len(critique.get('issues', []))}")
            
            # Track changes between iterations
            if current_code:
                changes = self._analyze_code_changes(current_code, new_code)
                print(f"🔄 Changes: {changes}")
            
            current_code = new_code
            
        print("\n✅ Complete loop tracking completed")
        
    async def _create_fixed_feedback_loop(self):
        """Create a fixed version of the feedback loop"""
        
        print("\n🔧 CREATING FIXED FEEDBACK LOOP")
        print("-" * 40)
        
        print("📝 Key fixes needed:")
        print("   1. More specific critique prompts")
        print("   2. Better feedback parsing")
        print("   3. Explicit feedback incorporation in generation")
        print("   4. Change tracking between iterations")
        
        # Create a fixed version
        fixed_code = await self._demonstrate_fixed_loop()
        
        print("✅ Fixed feedback loop demonstrated")
        
    async def _get_critique(self, code: str, description: str) -> Dict[str, Any]:
        """Get critique for code"""
        
        prompt = f"""Analyze this code and provide specific feedback:

Description: {description}

Code:
```python
{code}
```

Provide analysis in JSON format:
{{
    "quality_score": <1-10>,
    "issues": ["specific issue 1", "specific issue 2"],
    "suggestions": ["specific suggestion 1", "specific suggestion 2"]
}}

Analysis:"""
        
        response = await self._send_llm_request(prompt)
        return self._parse_json_response(response)
        
    async def _get_improved_code(self, code: str, feedback: list) -> str:
        """Get improved code based on feedback"""
        
        prompt = f"""Improve this code based on the specific feedback:

Current code:
```python
{code}
```

Specific feedback to address:
{chr(10).join(f"- {item}" for item in feedback)}

Please provide improved code that addresses ALL the feedback points above.

Improved code:"""
        
        response = await self._send_llm_request(prompt)
        return self._extract_code_from_response(response)
        
    async def _generate_code_with_context(self, task_info: Dict, existing_code: str, iteration: int) -> str:
        """Generate code with full context"""
        
        if iteration == 1:
            prompt = f"""Create a Python function for this task:

Description: {task_info['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task_info['requirements'])}

Provide complete implementation:"""
        else:
            prompt = f"""Improve this code based on the requirements:

Description: {task_info['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task_info['requirements'])}

Current code:
```python
{existing_code}
```

Provide improved version that better meets the requirements:"""
        
        response = await self._send_llm_request(prompt)
        return self._extract_code_from_response(response)
        
    async def _get_detailed_critique(self, code: str, task_info: Dict) -> Dict[str, Any]:
        """Get detailed critique"""
        
        prompt = f"""Provide detailed critique of this code:

Task: {task_info['description']}

Requirements:
{chr(10).join(f"- {req}" for req in task_info['requirements'])}

Code:
```python
{code}
```

Analyze and provide JSON:
{{
    "quality_score": <1-10>,
    "issues": ["issue1", "issue2"],
    "suggestions": ["suggestion1", "suggestion2"],
    "requirements_analysis": {{
        "requirement1": "met/not_met",
        "requirement2": "met/not_met"
    }}
}}

Analysis:"""
        
        response = await self._send_llm_request(prompt)
        return self._parse_json_response(response)
        
    def _analyze_code_changes(self, old_code: str, new_code: str) -> str:
        """Analyze changes between code versions"""
        
        if old_code == new_code:
            return "No changes"
        
        changes = []
        
        if len(new_code) > len(old_code):
            changes.append(f"+{len(new_code) - len(old_code)} chars")
        elif len(new_code) < len(old_code):
            changes.append(f"-{len(old_code) - len(new_code)} chars")
        
        if '"""' in new_code and '"""' not in old_code:
            changes.append("Added docstring")
        
        if "->" in new_code and "->" not in old_code:
            changes.append("Added type hints")
        
        if "raise" in new_code and "raise" not in old_code:
            changes.append("Added error handling")
        
        return ", ".join(changes) if changes else "Minor changes"
        
    async def _demonstrate_fixed_loop(self) -> str:
        """Demonstrate a working fixed loop"""
        
        print("\n🔧 DEMONSTRATING FIXED LOOP:")
        
        # Simple task
        task = "Create a function to check if a number is prime"
        
        # Iteration 1: Basic implementation
        code1 = await self._send_llm_request(f"Create a basic Python function to {task}")
        code1 = self._extract_code_from_response(code1)
        print(f"📝 Iteration 1: {len(code1)} chars")
        
        # Critique iteration 1
        critique1 = await self._send_llm_request(f"""Critique this code for task '{task}':
```python
{code1}
```
Provide specific issues in JSON format.""")
        critique1_data = self._parse_json_response(critique1)
        issues1 = critique1_data.get('issues', [])
        print(f"🔍 Found {len(issues1)} issues")
        
        # Iteration 2: Address specific issues
        if issues1:
            improvement_prompt = f"""Improve this code to fix these specific issues:

Current code:
```python
{code1}
```

Issues to fix:
{chr(10).join(f"- {issue}" for issue in issues1[:3])}

Provide improved code:"""
            
            code2 = await self._send_llm_request(improvement_prompt)
            code2 = self._extract_code_from_response(code2)
            print(f"📝 Iteration 2: {len(code2)} chars")
            
            # Check if issues were addressed
            addressed = 0
            if "def " in code2 and ":" in code2:
                addressed += 1
            if '"""' in code2:
                addressed += 1
            if "return" in code2:
                addressed += 1
                
            print(f"✅ Addressed {addressed}/{min(len(issues1), 3)} issues")
        
        return "Fixed loop demonstration completed"
        
    async def _send_llm_request(self, prompt: str) -> str:
        """Send request to LLM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.3,
                            "top_p": 0.9,
                            "num_predict": 2048
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "")
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return ""
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract code from response"""
        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        elif "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        return response.strip()
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON from response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        return {
            "quality_score": 5,
            "issues": ["Could not parse response"],
            "suggestions": ["Manual review needed"]
        }


async def main():
    """Run the diagnostic"""
    
    print("🔍 FEEDBACK LOOP DIAGNOSTIC")
    print("Analyzing why the feedback loop is broken")
    print("=" * 60)
    
    diagnostic = FeedbackLoopDiagnostic()
    await diagnostic.diagnose_feedback_loop()
    
    print("\n🎯 DIAGNOSIS COMPLETE!")
    print("Check the results to understand the feedback loop issues.")


if __name__ == "__main__":
    asyncio.run(main())
