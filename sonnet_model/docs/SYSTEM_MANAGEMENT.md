# System Management Guide

## 🚀 **Starting the Sonnet Model System**

### **Prerequisites Check**

Before starting, ensure you have:

```bash
# Check Python version (3.8+ required)
python --version

# Check GPU availability (optional for local LLM)
nvidia-smi

# Check Docker (for containerized deployment)
docker --version
docker-compose --version

# Check Redis (for multi-worker state management)
redis-cli ping
```

### **Quick Start Script**

Create and use the system startup script:

```bash
# Make the startup script executable
chmod +x scripts/start_system.sh

# Start the complete system
./scripts/start_system.sh
```

### **Manual Startup Process**

#### **1. Environment Setup**
```bash
# Set up environment variables
export OPENAI_API_KEY="your-openai-api-key"  # For cloud LLM
export ANTHROPIC_API_KEY="your-anthropic-api-key"  # Alternative cloud LLM
export REDIS_URL="redis://localhost:6379"  # For state management
export USE_REDIS="true"  # Enable Redis for multi-worker

# Optional: Local LLM setup
export OLLAMA_HOST="localhost:11434"
```

#### **2. Install Dependencies**
```bash
# Install Python dependencies
pip install -r requirements.txt

# Install optional GPU dependencies (for local LLM)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### **3. Start Core Services**

**Option A: Using Docker Compose (Recommended)**
```bash
# Start all services (Redis, API, Workers)
docker-compose up -d

# View logs
docker-compose logs -f
```

**Option B: Manual Service Startup**
```bash
# 1. Start Redis (in separate terminal)
redis-server

# 2. Start the main API server
python -m api.app

# 3. Start additional workers (optional, in separate terminals)
python -m api.app --workers 4
```

#### **4. Start Local LLM (Optional)**
```bash
# Start Ollama server
ollama serve

# Pull required models
ollama pull deepseek-coder-v2:16b
ollama pull codellama:34b-instruct
```

### **Verification**

Check that all services are running:

```bash
# Check API health
curl http://localhost:8000/health

# Check system status
curl http://localhost:8000/api/v1/status

# Run system tests
python test_complete_system.py
```

---

## 🛑 **Stopping the System**

### **Quick Stop Script**

```bash
# Stop all services gracefully
./scripts/stop_system.sh
```

### **Manual Shutdown Process**

#### **1. Stop Docker Services**
```bash
# Stop all containers
docker-compose down

# Stop and remove volumes (clears data)
docker-compose down -v
```

#### **2. Stop Manual Services**
```bash
# Stop API server (Ctrl+C in terminal or)
pkill -f "python -m api.app"

# Stop Redis
redis-cli shutdown

# Stop Ollama
pkill ollama
```

#### **3. Free GPU Memory**
```bash
# Check GPU usage
nvidia-smi

# Kill GPU processes if needed
sudo fuser -v /dev/nvidia*
sudo kill -9 <process_ids>

# Clear GPU memory cache
python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || echo "PyTorch not available"
```

---

## 🔄 **System Restart and Maintenance**

### **Graceful Restart**
```bash
# Restart with state preservation
./scripts/restart_system.sh

# Or manually:
docker-compose restart
```

### **Clean Restart**
```bash
# Stop everything
./scripts/stop_system.sh

# Clear temporary data
rm -rf data/temp/*
rm -rf logs/*

# Start fresh
./scripts/start_system.sh
```

### **Update System**
```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt --upgrade

# Restart services
./scripts/restart_system.sh
```

---

## 💾 **GPU Memory Management**

### **Monitor GPU Usage**
```bash
# Real-time GPU monitoring
watch -n 1 nvidia-smi

# Check specific process GPU usage
nvidia-smi pmon -i 0
```

### **Free GPU Memory**
```bash
# Script to free GPU memory
./scripts/free_gpu_memory.sh

# Manual GPU cleanup
python -c "
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print('GPU cache cleared')
else:
    print('CUDA not available')
"
```

### **GPU Memory Optimization**
```yaml
# config/config.yaml - Optimize GPU usage
resources:
  gpu_enabled: true
  gpu_memory_limit_gb: 16  # Adjust based on your GPU
  cpu_limit: 0.8

# Local LLM GPU settings
code_generator:
  llm:
    type: "http_api"  # Use Ollama for efficient GPU sharing
    # OR for direct GPU access:
    # type: "llama.cpp"
    # n_gpu_layers: 35  # Adjust based on model size
```

---

## 📊 **System Monitoring**

### **Health Checks**
```bash
# API health check
curl http://localhost:8000/health

# Detailed system status
curl http://localhost:8000/api/v1/status | jq

# Redis connection check
redis-cli ping

# GPU status
nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv
```

### **Log Monitoring**
```bash
# View API logs
tail -f logs/sonnet.log

# View Docker logs
docker-compose logs -f api

# View Redis logs
docker-compose logs -f redis
```

### **Performance Monitoring**
```bash
# System resource usage
htop

# Network connections
netstat -tulpn | grep :8000

# Disk usage
df -h
du -sh data/
```

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **1. Port Already in Use**
```bash
# Find process using port 8000
lsof -i :8000

# Kill process
kill -9 <PID>

# Or use different port
export API_PORT=8001
python -m api.app
```

#### **2. GPU Out of Memory**
```bash
# Free GPU memory
./scripts/free_gpu_memory.sh

# Reduce model size in config
# Edit config/config.yaml:
# model: "deepseek-coder-v2:7b"  # Smaller model
```

#### **3. Redis Connection Failed**
```bash
# Start Redis
redis-server

# Check Redis status
redis-cli ping

# Reset Redis data
redis-cli flushall
```

#### **4. API Not Responding**
```bash
# Check if API is running
curl http://localhost:8000/health

# Restart API
docker-compose restart api

# Check logs for errors
docker-compose logs api
```

### **Emergency Recovery**
```bash
# Complete system reset
./scripts/emergency_reset.sh

# This will:
# 1. Stop all services
# 2. Clear all data
# 3. Free GPU memory
# 4. Restart from clean state
```

---

## 📋 **System Status Dashboard**

### **Quick Status Check**
```bash
# Run comprehensive status check
./scripts/system_status.sh
```

This will show:
- ✅ API Server Status
- ✅ Redis Connection
- ✅ GPU Availability
- ✅ LLM Model Status
- ✅ Active Workers
- ✅ Memory Usage
- ✅ Disk Space

### **Automated Monitoring**
```bash
# Set up monitoring cron job
crontab -e

# Add this line for 5-minute checks:
# */5 * * * * /path/to/sonnet_model/scripts/health_check.sh
```

---

## 🎯 **Production Deployment**

### **Production Startup**
```bash
# Production environment
export ENVIRONMENT=production
export API_WORKERS=4
export USE_REDIS=true
export REDIS_URL="redis://redis-server:6379"

# Start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### **Production Monitoring**
```bash
# Enable Prometheus monitoring
export PROMETHEUS_ENABLED=true

# View metrics
curl http://localhost:9090/metrics
```

This comprehensive system management guide ensures you can easily start, stop, monitor, and troubleshoot the Sonnet Model system while efficiently managing GPU resources.
