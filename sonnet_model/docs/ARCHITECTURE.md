# Sonnet Model - Complete System Architecture & Visual Documentation

## 📋 Table of Contents
1. [System Architecture Overview](#system-architecture-overview)
2. [Function Call Graph](#function-call-graph)
3. [Data Flow & Message Passing](#data-flow--message-passing)
4. [Component Architecture & Dependencies](#component-architecture--dependencies)
5. [Implementation Flow & Debugging](#implementation-flow--debugging)
6. [Data Model Relationships](#data-model-relationships)

---

## 🏗️ System Architecture Overview

The Sonnet Model is an autonomous agentic code development system built with a sophisticated layered architecture:

### Core Components
- **AgenticSystem**: Main orchestrator coordinating all system components
- **TaskOrchestrator**: Advanced task management with expert-level critique integration
- **CodeGenerator**: LLM-based code generation with validation and retry mechanisms
- **CritiqueEngine**: Multi-dimensional code analysis (static + LLM + language-specific)
- **MessageBus**: Asynchronous communication backbone (Redis/In-memory)
- **StateManager**: Persistent conversation and task state management

### Architecture Layers
1. **Application Layer**: FastAPI + API routes
2. **Core System Layer**: AgenticSystem orchestrator + AppState
3. **Service Layer**: TaskOrchestrator, CodeGenerator, CritiqueEngine
4. **Infrastructure Layer**: MessageBus, StateManager, Database, Logging

---

## 🔄 Function Call Graph

```mermaid
graph TD
    %% Main Entry Points
    A[main.py] --> B[create_app]
    A --> C[main]

    %% FastAPI Application Setup
    B --> D[load_config]
    B --> E[configure_logging]
    B --> F[AgenticSystem]
    B --> G[set_system_instance]
    B --> H[FastAPI App Creation]

    %% Core System Integration
    F --> I[TaskOrchestrator]
    F --> J[CritiqueEngine]
    F --> K[CodeGenerator]

    %% API Routes
    H --> L[api/routes.py]
    L --> M[create_project]
    L --> N[get_system_status]
    L --> O[get_project_status]
    L --> P[stop_project]

    %% System Processing Flow
    M --> Q[process_project_background]
    Q --> R[AgenticSystem.process_request]

    %% AgenticSystem Core Methods
    R --> S[initialize_session]
    R --> T[_handle_plan_creation]
    R --> U[_handle_task_creation]
    R --> V[_handle_error_recovery]
    R --> W[_handle_status_request]
    R --> X[_handle_general_request]

    %% TaskOrchestrator Methods
    I --> Y[process_user_request]
    I --> Z[initialize]
    I --> AA[execute_task]
    I --> BB[_extract_and_trace_requirements]
    I --> CC[_create_advanced_development_plan]
    I --> DD[_execute_plan_with_expert_coaching]
    I --> EE[_validate_quality_gates]

    %% Code Generation Flow
    T --> FF[CodeGenerator.generate_code]
    U --> FF
    X --> FF
    FF --> GG[_attempt_generation]
    FF --> HH[_validate_request]
    FF --> II[_process_code]

    %% Critique Engine Flow
    T --> JJ[CritiqueEngine.analyze_code]
    U --> JJ
    X --> JJ
    JJ --> KK[_analyze_python]
    JJ --> LL[StaticAnalyzer.analyze_code]
    JJ --> MM[LLMCritic.critique_code]
    JJ --> NN[_calculate_quality_score]

    %% State Management
    I --> OO[StateManager]
    OO --> PP[initialize]
    OO --> QQ[update_task_status]
    OO --> RR[save_conversation_state]
    OO --> SS[load_conversation_state]

    %% Message Bus Integration
    I --> TT[MessageBus]
    J --> TT
    K --> TT
    TT --> UU[publish]
    TT --> VV[subscribe]
    TT --> WW[_process_all_messages]

    %% Shared Components
    B --> XX[AppState]
    XX --> YY[register_component]
    XX --> ZZ[get_component]

    %% Configuration & Logging
    D --> AAA[utils/config_loader.py]
    E --> BBB[shared/logging_config.py]

    %% Advanced Critique Components
    I --> CCC[AdvancedCriticEngine]
    I --> DDD[RequirementTraceabilityMatrix]
    I --> EEE[AdvancedTestingStrategy]
    I --> FFF[LLMCommandInterface]
    I --> GGG[CommandExecutor]

    %% Data Models
    I --> HHH[Task Model]
    I --> III[Plan Model]
    K --> JJJ[GenerationRequest]
    K --> KKK[GenerationResult]
    J --> LLL[CritiqueRequest]
    J --> MMM[CritiqueResult]

    %% Styling
    classDef entryPoint fill:#e1f5fe
    classDef coreSystem fill:#f3e5f5
    classDef apiLayer fill:#e8f5e8
    classDef dataFlow fill:#fff3e0
    classDef models fill:#fce4ec

    class A,B,C entryPoint
    class F,I,J,K coreSystem
    class L,M,N,O,P apiLayer
    class R,S,T,U,V,W,X dataFlow
    class HHH,III,JJJ,KKK,LLL,MMM models
```

---

## 📊 Data Flow & Message Passing

```mermaid
flowchart TD
    %% External Input
    A[User Request] --> B[FastAPI Endpoint]

    %% API Layer Processing
    B --> C{Request Type}
    C -->|Project Creation| D[create_project]
    C -->|Status Check| E[get_system_status]
    C -->|Project Status| F[get_project_status]

    %% Core System Entry
    D --> G[AgenticSystem.process_request]

    %% Session Management
    G --> H{Session Initialized?}
    H -->|No| I[initialize_session]
    H -->|Yes| J[Process Request]
    I --> J

    %% Request Classification
    J --> K{Request Classification}
    K -->|Plan Creation| L[_handle_plan_creation]
    K -->|Task Creation| M[_handle_task_creation]
    K -->|Error Recovery| N[_handle_error_recovery]
    K -->|Status Request| O[_handle_status_request]
    K -->|General Request| P[_handle_general_request]

    %% TaskOrchestrator Processing
    L --> Q[TaskOrchestrator.process_user_request]
    M --> Q
    N --> Q
    O --> Q
    P --> Q

    %% Requirement Analysis
    Q --> R[_extract_and_trace_requirements]
    R --> S[RequirementTraceabilityMatrix]
    S --> T[Requirement Analysis Result]

    %% Plan Creation
    T --> U[_create_advanced_development_plan]
    U --> V[Plan with Quality Gates]

    %% Code Generation Decision
    V --> W{Requires Code Generation?}
    W -->|Yes| X[CodeGenerator.generate_code]
    W -->|No| Y[Skip Code Generation]

    %% Code Generation Process
    X --> Z[_validate_request]
    Z --> AA[_attempt_generation]
    AA --> BB[LLM Interface]
    BB --> CC[Raw Code Response]
    CC --> DD[_process_code]
    DD --> EE[GenerationResult]

    %% Critique Process
    EE --> FF[CritiqueEngine.analyze_code]
    Y --> FF
    FF --> GG{Analysis Type}
    GG -->|Python| HH[PythonAnalyzer]
    GG -->|Static| II[StaticAnalyzer]
    GG -->|LLM| JJ[LLMCritic]

    %% Analysis Results
    HH --> KK[Python Issues]
    II --> LL[Static Issues]
    JJ --> MM[LLM Issues]
    KK --> NN[Combine Results]
    LL --> NN
    MM --> NN

    %% Quality Assessment
    NN --> OO[_calculate_quality_score]
    OO --> PP[Quality Score & Threshold Check]

    %% Task Execution
    PP --> QQ[_execute_plan_with_expert_coaching]
    QQ --> RR[Execute Individual Steps]
    RR --> SS[StateManager.update_task_status]

    %% Message Bus Communication
    SS --> TT[MessageBus.publish]
    TT --> UU{Message Type}
    UU -->|TASK_UPDATED| VV[Task Status Update]
    UU -->|CODE_GENERATION_RESPONSE| WW[Code Generation Complete]
    UU -->|CRITIQUE_RESPONSE| XX[Critique Complete]

    %% State Persistence
    VV --> YY[StateManager.save_conversation_state]
    WW --> YY
    XX --> YY

    %% Response Generation
    YY --> ZZ[Generate Response]
    ZZ --> AAA{Response Type}
    AAA -->|Plan with Code| BBB[Plan + Code + Critique]
    AAA -->|Task Executed| CCC[Task + Execution Result]
    AAA -->|Error Recovery| DDD[Error Analysis + Fix]
    AAA -->|Status Report| EEE[Comprehensive Status]

    %% Final Response
    BBB --> FFF[JSON Response]
    CCC --> FFF
    DDD --> FFF
    EEE --> FFF
    FFF --> GGG[Return to Client]

    %% Background Processes
    HHH[Background Message Processing] --> III[MessageBus.subscribe]
    III --> JJJ[Process Incoming Messages]
    JJJ --> KKK[Update Component States]

    %% Error Handling
    LLL[Error Occurs] --> MMM[_handle_error_recovery]
    MMM --> NNN[Error Analysis]
    NNN --> OOO[Recovery Actions]
    OOO --> PPP[Auto-retry if enabled]

    %% Coaching System
    QQQ[Coaching Messages] --> RRR[LLMCritic.generate_coaching]
    RRR --> SSS[Motivational Feedback]
    SSS --> TTT[Include in Response]

    %% Styling
    classDef input fill:#e3f2fd
    classDef processing fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef output fill:#e8f5e8
    classDef error fill:#ffebee
    classDef message fill:#f1f8e9

    class A,B input
    class G,Q,X,FF processing
    class C,H,K,W,GG,UU,AAA decision
    class FFF,GGG output
    class LLL,MMM,NNN error
    class TT,UU,VV,WW,XX message
```

---

## 🏛️ Component Architecture & Dependencies

```mermaid
graph TB
    %% Main Application Layer
    subgraph "Application Layer"
        A[main.py]
        B[FastAPI App]
        C[API Routes]
    end
    
    %% Core System Layer
    subgraph "Core System Layer"
        D[AgenticSystem]
        E[AppState]
        F[Configuration]
    end
    
    %% Service Layer
    subgraph "Task Management Services"
        G[TaskOrchestrator]
        H[StateManager]
        I[PriorityEngine]
        J[LLMCommandInterface]
        K[CommandExecutor]
    end
    
    subgraph "Code Generation Services"
        L[CodeGenerator]
        M[LLMInterface]
        N[PromptBuilder]
        O[CodeProcessor]
    end
    
    subgraph "Critique Services"
        P[CritiqueEngine]
        Q[AdvancedCriticEngine]
        R[LLMCritic]
        S[StaticAnalyzer]
        T[PythonAnalyzer]
        U[ProjectCritic]
    end
    
    subgraph "Advanced Analysis Services"
        V[RequirementTraceabilityMatrix]
        W[AdvancedTestingStrategy]
        X[QualityGateValidator]
    end
    
    %% Data Layer
    subgraph "Data Models"
        Y[Task Model]
        Z[Plan Model]
        AA[GenerationRequest]
        BB[GenerationResult]
        CC[CritiqueRequest]
        DD[CritiqueResult]
        EE[CodeIssue]
        FF[Message Model]
    end
    
    %% Infrastructure Layer
    subgraph "Infrastructure"
        GG[MessageBus]
        HH[RedisMessageBus]
        II[InMemoryMessageBus]
        JJ[Database]
        KK[Logging]
        LL[Configuration Loader]
    end
    
    %% External Services
    subgraph "External Services"
        MM[OpenAI API]
        NN[Local LLM]
        OO[Static Analysis Tools]
        PP[Testing Frameworks]
    end
    
    %% Dependencies and Relationships
    A --> B
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G
    D --> L
    D --> P
    
    %% Task Management Dependencies
    G --> H
    G --> I
    G --> J
    G --> K
    G --> Q
    G --> R
    G --> U
    G --> V
    G --> W
    
    %% Code Generation Dependencies
    L --> M
    L --> N
    L --> O
    M --> MM
    M --> NN
    
    %% Critique Engine Dependencies
    P --> Q
    P --> R
    P --> S
    P --> T
    P --> U
    Q --> V
    Q --> W
    Q --> X
    S --> OO
    T --> OO
    
    %% Data Model Usage
    G --> Y
    G --> Z
    L --> AA
    L --> BB
    P --> CC
    P --> DD
    P --> EE
    GG --> FF
    
    %% Infrastructure Dependencies
    G --> GG
    L --> GG
    P --> GG
    GG --> HH
    GG --> II
    H --> JJ
    D --> KK
    D --> LL
    
    %% External Service Connections
    R --> MM
    R --> NN
    S --> OO
    W --> PP
    
    %% Message Flow
    GG -.->|TASK_CREATED| G
    GG -.->|CODE_GENERATION_REQUEST| L
    GG -.->|CRITIQUE_REQUEST| P
    L -.->|CODE_GENERATION_RESPONSE| GG
    P -.->|CRITIQUE_RESPONSE| GG
    G -.->|TASK_UPDATED| GG

    %% Configuration Flow
    F --> G
    F --> L
    F --> P
    F --> GG

    %% State Management Flow
    H -.->|State Persistence| JJ
    E -.->|Component Registry| G
    E -.->|Component Registry| L
    E -.->|Component Registry| P

    %% Styling
    classDef appLayer fill:#e1f5fe,stroke:#01579b
    classDef coreLayer fill:#f3e5f5,stroke:#4a148c
    classDef serviceLayer fill:#e8f5e8,stroke:#1b5e20
    classDef dataLayer fill:#fff3e0,stroke:#e65100
    classDef infraLayer fill:#fce4ec,stroke:#880e4f
    classDef externalLayer fill:#f1f8e9,stroke:#33691e

    class A,B,C appLayer
    class D,E,F coreLayer
    class G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X serviceLayer
    class Y,Z,AA,BB,CC,DD,EE,FF dataLayer
    class GG,HH,II,JJ,KK,LL infraLayer
    class MM,NN,OO,PP externalLayer
```

---

## 🔄 Implementation Flow & Debugging

```mermaid
sequenceDiagram
    participant User
    participant API as FastAPI Routes
    participant AS as AgenticSystem
    participant TO as TaskOrchestrator
    participant CG as CodeGenerator
    participant CE as CritiqueEngine
    participant SM as StateManager
    participant MB as MessageBus
    participant LLM as LLM Services

    Note over User,LLM: System Initialization & Request Processing

    User->>API: POST /api/v1/projects
    API->>AS: process_request user_input

    Note over AS: Session Management
    AS->>AS: initialize_session if needed
    AS->>TO: orchestrator.initialize_session
    TO->>SM: state_manager.initialize
    SM-->>TO: session_info
    TO-->>AS: session_result

    Note over AS,TO: Request Classification & Processing
    AS->>AS: classify_request_type
    AS->>TO: process_user_request user_input

    Note over TO: Requirement Analysis
    TO->>TO: _extract_and_trace_requirements
    TO->>TO: RequirementTraceabilityMatrix.analyze
    TO->>TO: _create_advanced_development_plan

    Note over TO,MB: Plan Execution with Quality Gates
    TO->>TO: _execute_plan_with_expert_coaching
    TO->>MB: publish TASK_CREATED

    Note over AS,CG: Code Generation Flow
    AS->>AS: _requires_code_generation?
    AS->>CG: generate_code request
    CG->>CG: _validate_request
    CG->>CG: _attempt_generation
    CG->>LLM: generate prompt
    LLM-->>CG: raw_response
    CG->>CG: _process_code
    CG-->>AS: GenerationResult

    Note over AS,CE: Code Critique Flow
    AS->>CE: analyze_code code
    CE->>CE: _analyze_python if Python
    CE->>CE: StaticAnalyzer.analyze_code
    CE->>CE: LLMCritic.critique_code
    CE->>LLM: critique_request
    LLM-->>CE: critique_response
    CE->>CE: _calculate_quality_score
    CE-->>AS: CritiqueResult

    Note over AS,TO: Task Execution & State Updates
    AS->>TO: execute_task task_id
    TO->>SM: update_task_status IN_PROGRESS
    TO->>MB: publish TASK_UPDATED
    TO->>TO: _validate_quality_gates
    TO->>SM: update_task_status COMPLETED
    TO->>MB: publish TASK_COMPLETED

    Note over AS: Response Generation
    AS->>AS: _handle_plan_creation or _handle_task_creation
    AS->>AS: generate_comprehensive_response
    AS-->>API: response_dict
    API-->>User: JSON Response

    Note over MB: Background Message Processing
    MB->>MB: _process_all_messages
    MB->>TO: callback message for subscribers
    MB->>CG: callback message for subscribers
    MB->>CE: callback message for subscribers

    Note over SM: State Persistence
    SM->>SM: save_conversation_state
    SM->>SM: Database.save state

    Note over AS,TO: Error Handling & Recovery
    alt Error Occurs
        AS->>AS: _handle_error_recovery
        AS->>CE: analyze_error
        AS->>CG: generate_fix if code error
        AS->>TO: recovery_actions
        TO->>TO: auto_retry if enabled
    end

    Note over AS,TO: Coaching System
    TO->>TO: LLMCritic.generate_coaching
    TO->>TO: _generate_motivational_feedback
    TO->>AS: coaching_messages
    AS->>AS: include_coaching_in_response

    Note over User,LLM: Debugging Checkpoints
    Note right of API: 🔍 Debug Point 1: Request validation
    Note right of AS: 🔍 Debug Point 2: Session state
    Note right of TO: 🔍 Debug Point 3: Plan creation
    Note right of CG: 🔍 Debug Point 4: Code generation
    Note right of CE: 🔍 Debug Point 5: Code critique
    Note right of SM: 🔍 Debug Point 6: State persistence
    Note right of MB: 🔍 Debug Point 7: Message flow
```

---

## 📊 Data Model Relationships & State Management

```mermaid
erDiagram
    %% Core Data Models
    Task {
        string id PK
        string project_id FK
        string title
        string description
        TaskStatus status
        TaskPriority priority
        TaskType task_type
        datetime created_at
        datetime updated_at
        datetime completed_at
        string assigned_to
        list dependencies
        dict metadata
        int iteration
        int max_iterations
        float progress
        string generated_code
        list feedback_history
        float quality_score
        list tags
    }

    Plan {
        string id PK
        string project_id FK
        string title
        string description
        list steps
        PlanStatus status
        datetime created_at
        datetime updated_at
        dict metadata
        float completion_percentage
        list quality_gates
    }

    PlanStep {
        string id PK
        string plan_id FK
        string title
        string description
        StepStatus status
        int order
        list dependencies
        dict metadata
        string task_id FK
    }

    GenerationRequest {
        string task_id FK
        ProgrammingLanguage language
        Framework framework
        string description
        list requirements
        string context
        list dependencies
        list examples
        dict constraints
        dict metadata
        int iteration
        int max_tokens
        float temperature
        datetime created_at
    }

    GenerationResult {
        string task_id FK
        string code
        string language
        bool success
        string error_message
        dict metadata
        float generation_time
        int iteration
        string model_used
        int tokens_used
    }

    CritiqueRequest {
        string task_id FK
        string code
        string language
        list categories
        dict context
        int iteration
        datetime created_at
    }

    CritiqueResult {
        string task_id FK
        string request_id
        list issues
        float quality_score
        bool meets_threshold
        list suggestions
        float analysis_time
        int iteration
        bool success
    }

    CodeIssue {
        string id PK
        IssueSeverity severity
        IssueCategory category
        string message
        string file_path
        int line_number
        int column_number
        string rule_id
        string suggestion
        dict metadata
    }

    Message {
        string id PK
        MessageType type
        datetime timestamp
        dict payload
        dict metadata
    }

    ConversationState {
        string session_id PK
        string user_id
        dict context
        list message_history
        dict current_plan
        list active_tasks
        dict system_state
        datetime last_updated
        bool is_active
    }

    Project {
        string id PK
        string name
        string description
        string user_input
        string language
        ProjectStatus status
        datetime created_at
        datetime updated_at
        dict metadata
    }

    %% Relationships
    Project ||--o{ Task : "has many"
    Project ||--o{ Plan : "has many"
    Plan ||--o{ PlanStep : "contains"
    Task ||--o{ GenerationRequest : "generates"
    Task ||--o{ GenerationResult : "produces"
    Task ||--o{ CritiqueRequest : "critiques"
    Task ||--o{ CritiqueResult : "results in"
    CritiqueResult ||--o{ CodeIssue : "contains"
    PlanStep ||--o| Task : "maps to"
    ConversationState ||--o{ Task : "tracks"
    ConversationState ||--o{ Plan : "manages"

    %% State Transitions
    Task ||--o{ Message : "triggers"
    Plan ||--o{ Message : "triggers"
    GenerationResult ||--o{ Message : "triggers"
    CritiqueResult ||--o{ Message : "triggers"
```

---

## 🎯 Key Architecture Insights

### **Design Patterns Used**
- **Orchestrator Pattern**: AgenticSystem coordinates all components
- **Observer Pattern**: MessageBus for async communication
- **Strategy Pattern**: Multiple analysis strategies in CritiqueEngine
- **Factory Pattern**: Component creation and configuration
- **State Pattern**: Task and plan state management

### **Scalability Features**
- **Async Processing**: All major operations are asynchronous
- **Message Queue**: Redis-based for distributed processing
- **State Persistence**: Database-backed conversation state
- **Component Isolation**: Loosely coupled services
- **Configuration-Driven**: External config for all components

### **Quality Assurance**
- **Multi-layered Critique**: Static + LLM + Language-specific analysis
- **Quality Gates**: Threshold-based validation at each step
- **Requirement Traceability**: Full requirement-to-implementation tracking
- **Coaching System**: Continuous feedback and motivation
- **Error Recovery**: Automatic retry and recovery mechanisms

### **Debugging Strategy**
- **Comprehensive Logging**: Structured logging throughout
- **State Inspection**: Full state visibility at each checkpoint
- **Message Tracing**: Complete message flow tracking
- **Performance Monitoring**: Timing and resource usage tracking
- **Visual Documentation**: This architecture guide for reference

---

## 📚 Related Documentation

- **[DEBUGGING_GUIDE.md](./DEBUGGING_GUIDE.md)**: Detailed debugging procedures and common issues
- **[README.md](./README.md)**: System overview and getting started guide
- **[config/config.yaml](./config/config.yaml)**: Complete system configuration reference

---

*This architecture documentation provides a complete visual understanding of the Sonnet Model system. Use these diagrams as reference during development, debugging, and system maintenance.*
