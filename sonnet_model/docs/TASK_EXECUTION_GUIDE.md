# Task Execution Guide

## 🎯 **How to Start and Execute Real Tasks**

This guide shows you how to use the Sonnet Model system for actual development tasks, from simple code generation to complex project completion.

---

## 🚀 **Quick Start: Your First Task**

### **1. Start the System**
```bash
# Navigate to sonnet_model directory
cd sonnet_model

# Start all services
./scripts/start_system.sh

# Verify system is ready
./scripts/system_status.sh
```

### **2. Execute Your First Task**

**Option A: Using the API (Recommended)**
```bash
# Create a simple function
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my_first_task",
    "user_input": "Create a Python function that calculates the factorial of a number with error handling and documentation"
  }'
```

**Option B: Using Python Interface**
```python
import asyncio
from system_integration import AgenticSystem

async def main():
    # Initialize system
    system = AgenticSystem()
    await system.initialize_session()
    
    # Execute task
    result = await system.process_request(
        "Create a Python function that calculates the factorial of a number with error handling and documentation"
    )
    
    print("Generated code:", result)

asyncio.run(main())
```

---

## 📋 **Task Types and Examples**

### **1. Code Generation Tasks**

#### **Simple Function Creation**
```json
{
  "task_type": "code_generation",
  "description": "Create a function to validate email addresses using regex",
  "language": "python",
  "requirements": [
    "Use regex for validation",
    "Return boolean result",
    "Include docstring",
    "Handle edge cases"
  ]
}
```

#### **Class Implementation**
```json
{
  "task_type": "code_generation", 
  "description": "Create a REST API client class for GitHub API",
  "language": "python",
  "requirements": [
    "Use requests library",
    "Handle authentication",
    "Implement rate limiting",
    "Add error handling",
    "Include async methods"
  ]
}
```

### **2. Code Review and Improvement Tasks**

```json
{
  "task_type": "code_review",
  "description": "Review and improve this code for performance and security",
  "code": "def process_data(data):\n    result = []\n    for item in data:\n        if item > 0:\n            result.append(item * 2)\n    return result",
  "focus_areas": ["performance", "security", "readability"]
}
```

### **3. Bug Fix Tasks**

```json
{
  "task_type": "bug_fix",
  "description": "Fix the memory leak in this data processing function",
  "code": "# Your buggy code here",
  "error_description": "Memory usage keeps increasing during batch processing",
  "expected_behavior": "Memory should be released after each batch"
}
```

### **4. Feature Implementation Tasks**

```json
{
  "task_type": "feature_implementation",
  "description": "Add user authentication to existing Flask app",
  "existing_code": "# Your existing Flask app code",
  "requirements": [
    "JWT-based authentication",
    "User registration and login",
    "Password hashing",
    "Protected routes",
    "Session management"
  ]
}
```

---

## 🔄 **Task Execution Workflow**

### **Step 1: Task Submission**
```bash
# Submit task via API
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "user_auth_system",
    "user_input": "Create a complete user authentication system with JWT tokens, password hashing, and role-based access control"
  }'
```

### **Step 2: Monitor Progress**
```bash
# Check project status
curl "http://localhost:8000/api/v1/projects/user_auth_system/status"

# Get system status
curl "http://localhost:8000/api/v1/status"
```

### **Step 3: Review Results**
```bash
# Get project results (when completed)
curl "http://localhost:8000/api/v1/projects/user_auth_system/results"
```

### **Step 4: Iterate and Improve**
```bash
# Submit follow-up improvements
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "user_auth_system_v2",
    "user_input": "Improve the previous authentication system by adding OAuth2 support and two-factor authentication"
  }'
```

---

## 🎨 **Advanced Task Patterns**

### **1. Multi-Step Project Development**

```python
# Example: Building a complete web application
tasks = [
    {
        "step": 1,
        "description": "Create database models for a blog application",
        "requirements": ["User model", "Post model", "Comment model", "SQLAlchemy ORM"]
    },
    {
        "step": 2, 
        "description": "Implement REST API endpoints for the blog",
        "requirements": ["CRUD operations", "Authentication", "Pagination", "Error handling"]
    },
    {
        "step": 3,
        "description": "Add frontend components with React",
        "requirements": ["Post list", "Post detail", "User dashboard", "Responsive design"]
    },
    {
        "step": 4,
        "description": "Implement testing suite",
        "requirements": ["Unit tests", "Integration tests", "API tests", "Frontend tests"]
    }
]

# Execute each step sequentially
for task in tasks:
    result = await system.process_request(task["description"])
    print(f"Step {task['step']} completed")
```

### **2. Code Refactoring Projects**

```python
# Large-scale refactoring task
refactoring_task = {
    "type": "refactoring",
    "description": "Refactor legacy monolithic application into microservices",
    "scope": "entire_codebase",
    "goals": [
        "Extract user service",
        "Extract payment service", 
        "Extract notification service",
        "Implement API gateway",
        "Add service discovery",
        "Implement distributed logging"
    ],
    "constraints": [
        "Maintain backward compatibility",
        "Zero downtime deployment",
        "Preserve existing data"
    ]
}
```

### **3. Performance Optimization Tasks**

```python
# Performance optimization task
optimization_task = {
    "type": "optimization",
    "description": "Optimize database queries and API response times",
    "current_performance": {
        "avg_response_time": "2.5s",
        "database_queries_per_request": 15,
        "memory_usage": "512MB"
    },
    "target_performance": {
        "avg_response_time": "< 500ms",
        "database_queries_per_request": "< 5",
        "memory_usage": "< 256MB"
    },
    "optimization_areas": [
        "Database indexing",
        "Query optimization", 
        "Caching implementation",
        "Code profiling",
        "Memory management"
    ]
}
```

---

## 📊 **Task Monitoring and Management**

### **Real-Time Monitoring**

```bash
# Monitor system status in real-time
watch -n 5 './scripts/system_status.sh'

# Monitor GPU usage during tasks
watch -n 1 nvidia-smi

# Monitor API logs
tail -f logs/sonnet.log
```

### **Task Progress Tracking**

```python
# Python script to track task progress
import asyncio
import time
from system_integration import AgenticSystem

async def track_task_progress(task_id):
    system = AgenticSystem()
    
    while True:
        status = await system.get_task_status(task_id)
        print(f"Task {task_id}: {status['state']} - {status['progress']}%")
        
        if status['state'] in ['completed', 'failed']:
            break
            
        await asyncio.sleep(10)  # Check every 10 seconds

# Usage
asyncio.run(track_task_progress("my_task_id"))
```

---

## 🔧 **Troubleshooting Task Execution**

### **Common Issues and Solutions**

#### **1. Task Stuck or Not Progressing**
```bash
# Check system status
./scripts/system_status.sh

# Check for conversation length issues
curl "http://localhost:8000/api/v1/status" | jq '.conversation_stats'

# Restart if needed
./scripts/restart_system.sh
```

#### **2. GPU Memory Issues**
```bash
# Free GPU memory
./scripts/free_gpu_memory.sh

# Check GPU usage
nvidia-smi

# Reduce model size in config if needed
# Edit config/config.yaml: model: "deepseek-coder-v2:7b"
```

#### **3. API Timeouts**
```bash
# Increase timeout in config
# Edit config/config.yaml:
# api:
#   timeout: 600  # 10 minutes

# Or use async task submission
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{"name": "long_task", "user_input": "...", "async": true}'
```

---

## 🎯 **Best Practices for Task Execution**

### **1. Task Definition**
- **Be Specific**: Provide clear, detailed requirements
- **Include Context**: Mention existing code, frameworks, constraints
- **Set Expectations**: Define success criteria and acceptance tests
- **Provide Examples**: Include input/output examples when relevant

### **2. Task Management**
- **Break Down Large Tasks**: Split complex projects into smaller, manageable tasks
- **Use Iterative Approach**: Build incrementally and test frequently
- **Monitor Progress**: Check status regularly and adjust as needed
- **Save Intermediate Results**: Preserve working code at each milestone

### **3. System Optimization**
- **Configure Appropriately**: Set conversation limits and LLM parameters
- **Monitor Resources**: Keep an eye on GPU/CPU/memory usage
- **Use Cloud LLMs**: For complex tasks, consider using GPT-4 or Claude
- **Enable Caching**: Reuse results for similar tasks

---

## 📈 **Scaling Task Execution**

### **Multi-Worker Setup**
```bash
# Start multiple workers for parallel task processing
export API_WORKERS=4
export USE_REDIS=true
./scripts/start_system.sh
```

### **Batch Task Processing**
```python
# Process multiple tasks in parallel
tasks = [
    "Create user authentication module",
    "Implement payment processing",
    "Add email notification system",
    "Create admin dashboard"
]

async def process_batch(tasks):
    system = AgenticSystem()
    results = await asyncio.gather(*[
        system.process_request(task) for task in tasks
    ])
    return results

results = asyncio.run(process_batch(tasks))
```

### **Continuous Integration**
```yaml
# .github/workflows/sonnet-tasks.yml
name: Automated Code Generation
on:
  push:
    branches: [main]
jobs:
  generate-code:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Sonnet Model
        run: ./scripts/start_system.sh
      - name: Execute Tasks
        run: python scripts/execute_pending_tasks.py
```

This comprehensive guide provides everything you need to start executing real development tasks with the Sonnet Model system, from simple code generation to complex project completion!
