version: '3.8'

# Production overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  agentic-system:
    environment:
      # Production-specific settings
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - API_WORKERS=4
      - USE_REDIS=true
      
      # Security settings
      - DEBUG=false
      - RELOAD=false
      
      # Performance settings
      - WORKER_TIMEOUT=300
      - MAX_REQUESTS=1000
      - MAX_REQUESTS_JITTER=100
    
    # Production resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    # Production Redis configuration
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  postgres:
    # Production PostgreSQL configuration
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-agentic_system}
      POSTGRES_USER: ${POSTGRES_USER:-agentic_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-agentic_password}
      
      # Performance settings
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: 0.9
      POSTGRES_WAL_BUFFERS: 16MB
      POSTGRES_DEFAULT_STATISTICS_TARGET: 100
    
    # Health check
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-agentic_user}"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  llm-server:
    # Production Ollama configuration
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_ORIGINS=*
      - OLLAMA_NUM_PARALLEL=2
      - OLLAMA_MAX_LOADED_MODELS=2
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Production-specific volumes with better performance
volumes:
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/redis
  
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/postgres
  
  ollama_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/ollama
