"""
Configuration loader utility for Sonnet Model
"""
import os
from pathlib import Path
from typing import Dict, Any, Optional

import yaml


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load configuration from YAML file
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dict containing the configuration
        
    Raises:
        FileNotFoundError: If the configuration file is not found
    """
    if not config_path:
        # Default configuration path
        config_path = os.environ.get(
            "SONNET_CONFIG", 
            str(Path(__file__).parent.parent / "config" / "config.yaml")
        )
    
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_file, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    
    # Override with environment variables
    _override_with_env(config)
    
    return config


def _override_with_env(config: Dict[str, Any], prefix: str = "SONNET") -> None:
    """
    Override configuration values with environment variables
    
    Environment variables should be in the format:
    PREFIX_SECTION_KEY=value
    
    For example:
    SONNET_API_PORT=8080
    
    Args:
        config: Configuration dictionary to update
        prefix: Prefix for environment variables
    """
    for env_name, env_value in os.environ.items():
        if not env_name.startswith(f"{prefix}_"):
            continue
        
        # Remove prefix and split by underscore
        parts = env_name[len(prefix) + 1:].lower().split("_")
        
        if len(parts) < 2:
            continue
        
        # Navigate through the config dictionary
        current = config
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        
        # Set the value, converting to appropriate type
        key = parts[-1]
        if key in current and isinstance(current[key], bool):
            current[key] = env_value.lower() in ("true", "1", "yes")
        elif key in current and isinstance(current[key], int):
            current[key] = int(env_value)
        elif key in current and isinstance(current[key], float):
            current[key] = float(env_value)
        elif key in current and isinstance(current[key], list):
            current[key] = env_value.split(",")
        else:
            current[key] = env_value
