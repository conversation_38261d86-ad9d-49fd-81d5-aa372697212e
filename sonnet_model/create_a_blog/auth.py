import os
from flask import Flask, request, jsonify, session, redirect, url_for
from werkzeug.security import generate_password_hash, check_password_hash
from flask_bcrypt import Bcrypt

app = Flask(__name__)
app.config['SECRET_KEY'] = os.urandom(24)
app.config['SESSION_TYPE'] = 'filesystem'
bcrypt = Bcrypt(app)

# In-memory user storage for demonstration purposes
users = {}

def login_required(f):
    """Decorator to require login for accessing a route."""
    def wrapper(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    wrapper.__name__ = f.__name__
    return wrapper

@app.route('/register', methods=['POST'])
def register():
    """User registration endpoint."""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': 'Username and password are required'}), 400
    
    if username in users:
        return jsonify({'error': 'Username already exists'}), 409
    
    hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')
    users[username] = {'password': hashed_password}
    
    session['user_id'] = username
    return jsonify({'message': 'User registered successfully'}), 201

@app.route('/login', methods=['POST'])
def login():
    """User login endpoint."""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': 'Username and password are required'}), 400
    
    if username not in users:
        return jsonify({'error': 'Invalid username'}), 401
    
    user = users[username]
    if check_password_hash(user['password'], password):
        session['user_id'] = username
        return jsonify({'message': 'Logged in successfully'}), 200
    
    return jsonify({'error': 'Invalid password'}), 401

@app.route('/logout')
def logout():
    """User logout endpoint."""
    session.pop('user_id', None)
    return redirect(url_for('login'))

@app.route('/protected')
@login_required
def protected():
    """A protected route that requires authentication."""
    return jsonify({'message': 'This is a protected route'}), 200

if __name__ == '__main__':
    app.run(debug=True)