from flask import Flask, render_template, request, jsonify

# Initialize the Flask application
app = Flask(__name__)

# Define a route for the root URL ("/") with GET method
@app.route('/', methods=['GET'])
def home():
    """
    Route to display the home page.
    
    Returns:
        A rendered HTML template named 'index.html'.
    """
    return render_template('index.html')

# Define a route for "/error" that raises an error for demonstration purposes
@app.route('/error', methods=['GET'])
def raise_error():
    """
    Route to demonstrate error handling. This will intentionally cause a 500 Internal Server Error.
    
    Raises:
        A ValueError with a message.
    """
    raise ValueError("This is a test error.")

# Define an error handler for HTTP errors (e.g., 404, 500)
@app.errorhandler(404)
def page_not_found(error):
    """
    Error handler for 404 Not Found error.
    
    Args:
        error: The error object.
        
    Returns:
        A JSON response with a 404 status code and an error message.
    """
    return jsonify({'error': 'Not Found', 'message': 'The requested URL was not found on the server.'}), 404

@app.errorhandler(500)
def internal_server_error(error):
    """
    Error handler for 500 Internal Server Error.
    
    Args:
        error: The error object.
        
    Returns:
        A JSON response with a 500 status code and an error message.
    """
    return jsonify({'error': 'Internal Server Error', 'message': 'The server encountered an internal error.'}), 500

# Run the Flask app if this script is executed directly
if __name__ == '__main__':
    app.run(debug=True)