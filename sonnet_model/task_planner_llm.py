"""
TASK PLANNER LLM

This component takes high-level project descriptions and breaks them down into 
detailed task lists that can be fed to the code generation and critique system.

This is exactly what you wanted - an LLM that understands project requirements
and creates comprehensive task breakdowns automatically.
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class TaskPlannerLLM:
    """LLM-based task planner for project breakdown"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
    async def create_project_plan(self, project_description: str, 
                                project_type: str = "web_app") -> Dict[str, Any]:
        """Create a comprehensive project plan from high-level description"""
        
        print(f"🎯 CREATING PROJECT PLAN")
        print(f"📝 Description: {project_description}")
        print(f"🏗️ Type: {project_type}")
        print("-" * 60)
        
        # Generate project structure and task breakdown
        plan = await self._generate_project_structure(project_description, project_type)
        
        if not plan:
            print("❌ Project planning failed")
            return {}
        
        # Enhance with detailed requirements
        enhanced_plan = await self._enhance_with_requirements(plan, project_description)
        
        # Add implementation order
        ordered_plan = await self._add_implementation_order(enhanced_plan)
        
        print(f"✅ Project plan created with {len(ordered_plan.get('tasks', []))} tasks")
        return ordered_plan
    
    async def _generate_project_structure(self, description: str, project_type: str) -> Dict[str, Any]:
        """Generate the basic project structure and file breakdown"""
        
        prompt = f"""You are an expert software architect. Create a comprehensive project plan for this request:

PROJECT DESCRIPTION: {description}
PROJECT TYPE: {project_type}

Create a detailed project breakdown in JSON format:

{{
    "project_name": "suggested_project_name",
    "project_description": "detailed description",
    "technology_stack": [
        "technology1",
        "technology2"
    ],
    "project_structure": {{
        "backend": ["file1.py", "file2.py"],
        "frontend": ["file1.html", "file2.js"],
        "database": ["schema.sql"],
        "tests": ["test_file1.py"],
        "config": ["config.yaml"],
        "docs": ["README.md"]
    }},
    "tasks": [
        {{
            "id": 1,
            "name": "Task Name",
            "description": "Detailed task description",
            "file_path": "path/to/file.py",
            "dependencies": [],
            "estimated_complexity": "low/medium/high",
            "requirements": [
                "Specific requirement 1",
                "Specific requirement 2"
            ]
        }}
    ]
}}

IMPORTANT: 
- Include ALL necessary files for a complete project
- Break down into specific, implementable tasks
- Each task should create ONE file
- Include proper dependencies between tasks
- Add comprehensive requirements for each task

PROJECT PLAN:"""
        
        response = await self._send_llm_request(prompt)
        return self._parse_json_response(response)
    
    async def _enhance_with_requirements(self, plan: Dict[str, Any], description: str) -> Dict[str, Any]:
        """Enhance the plan with detailed requirements"""
        
        if not plan or 'tasks' not in plan:
            return plan
        
        print("🔧 Enhancing tasks with detailed requirements...")
        
        enhanced_tasks = []
        
        for task in plan['tasks']:
            enhanced_task = await self._enhance_single_task(task, description, plan)
            enhanced_tasks.append(enhanced_task)
        
        plan['tasks'] = enhanced_tasks
        return plan
    
    async def _enhance_single_task(self, task: Dict[str, Any], 
                                 project_description: str, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance a single task with detailed requirements"""
        
        prompt = f"""You are an expert developer. Enhance this task with comprehensive, specific requirements:

PROJECT: {project_description}
TECHNOLOGY STACK: {', '.join(plan.get('technology_stack', []))}

TASK TO ENHANCE:
Name: {task.get('name', '')}
Description: {task.get('description', '')}
File: {task.get('file_path', '')}

Provide enhanced task in JSON format:
{{
    "id": {task.get('id', 1)},
    "name": "{task.get('name', '')}",
    "description": "Enhanced detailed description",
    "file_path": "{task.get('file_path', '')}",
    "dependencies": {json.dumps(task.get('dependencies', []))},
    "estimated_complexity": "{task.get('estimated_complexity', 'medium')}",
    "requirements": [
        "Very specific requirement 1 with implementation details",
        "Very specific requirement 2 with implementation details",
        "Very specific requirement 3 with implementation details"
    ],
    "acceptance_criteria": [
        "Specific criteria 1",
        "Specific criteria 2"
    ],
    "technical_specifications": {{
        "functions_to_implement": ["function1", "function2"],
        "classes_to_create": ["Class1", "Class2"],
        "apis_to_create": ["endpoint1", "endpoint2"],
        "error_handling": ["error_type1", "error_type2"]
    }}
}}

ENHANCED TASK:"""
        
        response = await self._send_llm_request(prompt)
        enhanced = self._parse_json_response(response)
        
        # Fallback to original task if enhancement fails
        if not enhanced or 'name' not in enhanced:
            return task
        
        return enhanced
    
    async def _add_implementation_order(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Add proper implementation order based on dependencies"""
        
        if not plan or 'tasks' not in plan:
            return plan
        
        print("📋 Determining implementation order...")
        
        prompt = f"""You are a project manager. Analyze these tasks and determine the optimal implementation order:

TASKS:
{json.dumps(plan['tasks'], indent=2)}

Provide the tasks in optimal implementation order in JSON format:
{{
    "implementation_phases": [
        {{
            "phase": 1,
            "name": "Foundation Phase",
            "description": "Core infrastructure and models",
            "tasks": [1, 2, 3]
        }},
        {{
            "phase": 2,
            "name": "Core Features Phase", 
            "description": "Main functionality",
            "tasks": [4, 5, 6]
        }}
    ],
    "ordered_tasks": [
        {{
            "implementation_order": 1,
            "task_id": 1,
            "rationale": "Why this task should be implemented first"
        }}
    ]
}}

IMPLEMENTATION ORDER:"""
        
        response = await self._send_llm_request(prompt)
        order_info = self._parse_json_response(response)
        
        if order_info:
            plan['implementation_order'] = order_info
        
        return plan
    
    async def create_task_list_for_description(self, description: str) -> List[Dict[str, Any]]:
        """Create a simple task list from description (for immediate use)"""
        
        print(f"📋 Creating task list for: {description}")
        
        prompt = f"""Create a detailed task breakdown for this project:

PROJECT: {description}

Create a JSON array of tasks:
[
    {{
        "path": "file/path.py",
        "description": "What this file does",
        "requirements": [
            "Specific requirement 1",
            "Specific requirement 2",
            "Specific requirement 3"
        ]
    }}
]

Focus on:
- Complete project structure
- Specific, implementable requirements
- Proper file organization
- All necessary components

TASK LIST:"""
        
        response = await self._send_llm_request(prompt)
        task_list = self._parse_json_response(response)
        
        if isinstance(task_list, list):
            print(f"✅ Created {len(task_list)} tasks")
            return task_list
        else:
            print("❌ Failed to create task list")
            return []
    
    async def _send_llm_request(self, prompt: str) -> str:
        """Send request to LLM"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.3,
                            "top_p": 0.9,
                            "num_predict": 4096
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "")
                else:
                    self.logger.error(f"LLM request failed: {response.status_code}")
                    return ""
                    
        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return ""
    
    def _parse_json_response(self, response: str) -> Any:
        """Parse JSON response from LLM"""
        try:
            # Find JSON block
            start = response.find('{')
            if start == -1:
                start = response.find('[')
            
            if start != -1:
                # Find matching closing bracket
                bracket_count = 0
                end = start
                is_array = response[start] == '['
                
                for i in range(start, len(response)):
                    if response[i] in ['{', '[']:
                        bracket_count += 1
                    elif response[i] in ['}', ']']:
                        bracket_count -= 1
                        if bracket_count == 0:
                            end = i + 1
                            break
                
                if end > start:
                    json_str = response[start:end]
                    return json.loads(json_str)
        
        except Exception as e:
            self.logger.error(f"JSON parsing failed: {e}")
        
        return {}


async def test_task_planner():
    """Test the task planner with various project types"""
    
    print("🚀 TESTING TASK PLANNER LLM")
    print("=" * 60)
    
    planner = TaskPlannerLLM()
    
    # Test projects
    test_projects = [
        {
            "description": "Create a personal expense tracker web application",
            "type": "web_app"
        },
        {
            "description": "Build a simple blog platform with user authentication",
            "type": "web_app"
        },
        {
            "description": "Create a REST API for a library management system",
            "type": "api"
        }
    ]
    
    for i, project in enumerate(test_projects, 1):
        print(f"\n🎯 TEST PROJECT {i}")
        print("-" * 40)
        
        # Test simple task list creation
        task_list = await planner.create_task_list_for_description(project['description'])
        
        print(f"📋 Generated {len(task_list)} tasks:")
        for j, task in enumerate(task_list[:3], 1):  # Show first 3
            print(f"   {j}. {task.get('path', 'N/A')}: {task.get('description', 'N/A')}")
        
        if len(task_list) > 3:
            print(f"   ... and {len(task_list) - 3} more tasks")
        
        # Save the task list
        output_file = f"task_plan_{i}.json"
        with open(output_file, 'w') as f:
            json.dump(task_list, f, indent=2)
        print(f"💾 Saved to: {output_file}")
    
    print(f"\n✅ Task planner testing completed!")


async def main():
    """Run the task planner test"""
    
    await test_task_planner()


if __name__ == "__main__":
    asyncio.run(main())
