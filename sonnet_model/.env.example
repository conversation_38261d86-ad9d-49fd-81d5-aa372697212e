# Environment Configuration Template
# Copy this file to .env and update the values

# Application Settings
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=false

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Database Configuration
DATABASE_URL=postgresql://agentic_user:agentic_password@localhost:5432/agentic_system
REDIS_URL=redis://localhost:6379/0

# LLM Configuration
LLM_ENDPOINT=http://localhost:11434
LLM_MODEL=deepseek-coder-v2:16b
CRITIQUE_LLM_MODEL=deepseek-coder-v2:16b

# Cloud LLM Provider API Keys (Optional)
# OPENAI_API_KEY=your-openai-api-key-here
# ANTHROPIC_API_KEY=your-anthropic-api-key-here
# AZURE_OPENAI_API_KEY=your-azure-openai-api-key-here
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
# AZURE_DEPLOYMENT_NAME=your-deployment-name-here
# GOOGLE_PALM_API_KEY=your-google-palm-api-key-here

# State Management
USE_REDIS=true

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# File Storage
STORAGE_PATH=./data
TEMP_PATH=./temp
LOG_PATH=./logs

# Performance Settings
MAX_CONCURRENT_TASKS=5
REQUEST_TIMEOUT=300
MAX_ITERATIONS=5

# Feature Flags
ENABLE_STATIC_ANALYSIS=true
ENABLE_SECURITY_SCAN=true
ENABLE_PERFORMANCE_ANALYSIS=true
