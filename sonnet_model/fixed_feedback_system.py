"""
FIXED ITERATIVE IMPROVEMENT SYSTEM

This fixes the broken feedback loop by:
1. Better critique prompts that give specific, actionable feedback
2. Explicit feedback tracking and incorporation
3. Quality-based decisions with real improvement detection
4. Proper change tracking between iterations

This is the REAL working version!
"""

import asyncio
import json
import logging
import hashlib
from typing import Dict, Any, List, Tuple
import httpx
from pathlib import Path

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class FixedIterativeSystem:
    """Real iterative improvement system that actually works"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Quality thresholds
        self.quality_threshold = 8.5  # Higher threshold
        self.max_iterations = 8
        self.min_improvement = 0.5  # Minimum quality improvement required
        
    async def create_file_with_real_improvement(self, file_info: Dict) -> Tuple[bool, int, str]:
        """Create a file with REAL iterative improvement"""
        
        print(f"\n🎯 CREATING: {file_info['path']}")
        print(f"📝 {file_info['description']}")
        print("-" * 60)
        
        iteration = 1
        current_code = ""
        previous_quality = 0
        improvement_history = []
        
        while iteration <= self.max_iterations:
            print(f"\n🔄 ITERATION {iteration}")
            print("-" * 30)
            
            # Generate code with explicit feedback incorporation
            print("🤖 CODE GENERATOR: Working...")
            new_code = await self._generate_with_explicit_feedback(
                file_info, current_code, improvement_history, iteration
            )
            
            if not new_code:
                print("❌ Code generation failed")
                return False, iteration, current_code
            
            # Check for actual changes
            code_hash = hashlib.md5(new_code.encode()).hexdigest()
            if current_code:
                prev_hash = hashlib.md5(current_code.encode()).hexdigest()
                if code_hash == prev_hash:
                    print("⚠️  No changes detected - forcing improvement")
                    new_code = await self._force_improvement(file_info, current_code, improvement_history)
            
            print(f"📄 Generated: {len(new_code)} characters")
            
            # Get detailed critique with specific scoring
            print("🔍 CRITIQUE ENGINE: Analyzing...")
            critique = await self._get_detailed_critique(new_code, file_info, iteration)
            
            if not critique:
                print("❌ Critique failed")
                return False, iteration, current_code
            
            current_quality = critique.get('quality_score', 0)
            issues = critique.get('issues', [])
            improvements = critique.get('improvements_made', [])
            
            print(f"📊 Quality Score: {current_quality}/10")
            print(f"🔍 Issues Found: {len(issues)}")
            print(f"✅ Improvements Made: {len(improvements)}")
            
            # Show specific feedback
            if issues:
                print("⚠️  Specific Issues:")
                for i, issue in enumerate(issues[:3], 1):
                    print(f"   {i}. {issue}")
            
            if improvements:
                print("🎯 Improvements Made:")
                for i, improvement in enumerate(improvements[:3], 1):
                    print(f"   {i}. {improvement}")
            
            # Track improvement
            quality_improvement = current_quality - previous_quality
            if iteration > 1:
                print(f"📈 Quality Change: {quality_improvement:+.1f}")
            
            # Store improvement history for next iteration
            improvement_history.append({
                'iteration': iteration,
                'quality': current_quality,
                'issues': issues,
                'improvements': improvements,
                'code_length': len(new_code)
            })
            
            # Decision logic
            if current_quality >= self.quality_threshold:
                print(f"✅ QUALITY THRESHOLD REACHED! ({current_quality:.1f}/{self.quality_threshold})")
                return True, iteration, new_code
            
            if iteration > 2 and quality_improvement < self.min_improvement:
                print(f"⚠️  Insufficient improvement ({quality_improvement:.1f} < {self.min_improvement})")
                print("🔧 Trying different approach...")
            
            current_code = new_code
            previous_quality = current_quality
            iteration += 1
            
            await asyncio.sleep(1)  # Brief pause
        
        print(f"⚠️  Reached maximum iterations ({self.max_iterations})")
        print(f"📊 Final quality: {current_quality:.1f}/{self.quality_threshold}")
        return current_quality >= (self.quality_threshold - 1), iteration - 1, current_code
    
    async def _generate_with_explicit_feedback(self, file_info: Dict, current_code: str, 
                                             history: List[Dict], iteration: int) -> str:
        """Generate code with explicit feedback incorporation"""
        
        if iteration == 1:
            prompt = f"""You are an expert developer. Create a high-quality {file_info['path']} file.

TASK: {file_info['description']}

REQUIREMENTS:
{chr(10).join(f"- {req}" for req in file_info['requirements'])}

Create a complete, production-ready implementation with:
- Comprehensive error handling
- Detailed documentation
- Type hints (for Python)
- Best practices
- Security considerations

File content:"""
        else:
            # Build specific feedback from history
            last_critique = history[-1] if history else {}
            specific_issues = last_critique.get('issues', [])
            
            feedback_text = ""
            if specific_issues:
                feedback_text = f"""
SPECIFIC ISSUES TO FIX:
{chr(10).join(f"- {issue}" for issue in specific_issues)}

CRITICAL: Address EVERY issue listed above in your improved version.
"""
            
            prompt = f"""You are an expert developer. IMPROVE this {file_info['path']} file.

TASK: {file_info['description']}

REQUIREMENTS:
{chr(10).join(f"- {req}" for req in file_info['requirements'])}

CURRENT CODE:
```
{current_code}
```
{feedback_text}
IMPROVEMENT INSTRUCTIONS:
1. Keep all existing functionality that works
2. Fix every specific issue mentioned above
3. Enhance code quality, documentation, and error handling
4. Make meaningful improvements, not just cosmetic changes

IMPROVED FILE CONTENT:"""
        
        response = await self._send_llm_request(prompt, temperature=0.2)
        return self._extract_code_from_response(response, file_info['path'])
    
    async def _get_detailed_critique(self, code: str, file_info: Dict, iteration: int) -> Dict[str, Any]:
        """Get detailed, specific critique"""
        
        prompt = f"""You are a senior code reviewer with very high standards. Analyze this {file_info['path']} file.

TASK: {file_info['description']}

REQUIREMENTS TO CHECK:
{chr(10).join(f"- {req}" for req in file_info['requirements'])}

CODE TO REVIEW:
```
{code}
```

REVIEW INSTRUCTIONS:
1. Be SPECIFIC and ACTIONABLE in your feedback
2. Check each requirement individually
3. Look for code quality, security, performance issues
4. Rate quality strictly (9+ only for excellent code)
5. Identify specific improvements made (if this is iteration {iteration})

Provide analysis in JSON format:
{{
    "quality_score": <number 1-10, be strict>,
    "issues": [
        "Specific issue 1 with exact location/fix needed",
        "Specific issue 2 with exact location/fix needed"
    ],
    "suggestions": [
        "Specific actionable suggestion 1",
        "Specific actionable suggestion 2"
    ],
    "requirements_analysis": {{
        "requirement_1": "met/partially_met/not_met",
        "requirement_2": "met/partially_met/not_met"
    }},
    "improvements_made": [
        "Specific improvement 1 (if any)",
        "Specific improvement 2 (if any)"
    ],
    "code_quality_breakdown": {{
        "error_handling": <1-10>,
        "documentation": <1-10>,
        "best_practices": <1-10>,
        "security": <1-10>,
        "maintainability": <1-10>
    }}
}}

CRITICAL: Be specific and actionable. Vague feedback like "add error handling" is not helpful.

ANALYSIS:"""
        
        response = await self._send_llm_request(prompt, temperature=0.05)
        return self._parse_json_response(response)
    
    async def _force_improvement(self, file_info: Dict, current_code: str, history: List[Dict]) -> str:
        """Force improvement when code isn't changing"""
        
        prompt = f"""The code is not improving. Force significant improvements to this {file_info['path']} file.

CURRENT CODE:
```
{current_code}
```

FORCE THESE IMPROVEMENTS:
1. Add comprehensive error handling for ALL edge cases
2. Enhance documentation with detailed examples
3. Add input validation and type checking
4. Improve performance and efficiency
5. Add logging and debugging support
6. Enhance security measures

Make SIGNIFICANT changes, not minor tweaks.

SIGNIFICANTLY IMPROVED CODE:"""
        
        response = await self._send_llm_request(prompt, temperature=0.4)
        return self._extract_code_from_response(response, file_info['path'])
    
    async def _send_llm_request(self, prompt: str, temperature: float = 0.2) -> str:
        """Send request to LLM with proper configuration"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": temperature,
                            "top_p": 0.9,
                            "num_predict": 4096
                        }
                    },
                    timeout=180.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "")
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return ""
    
    def _extract_code_from_response(self, response: str, file_path: str) -> str:
        """Extract code from LLM response based on file type"""
        
        # Determine language from file extension
        if file_path.endswith('.py'):
            patterns = ["```python", "```"]
        elif file_path.endswith('.js'):
            patterns = ["```javascript", "```js", "```"]
        elif file_path.endswith('.html'):
            patterns = ["```html", "```"]
        elif file_path.endswith('.css'):
            patterns = ["```css", "```"]
        else:
            patterns = ["```"]
        
        # Try each pattern
        for pattern in patterns:
            if pattern in response:
                start = response.find(pattern) + len(pattern)
                if pattern != "```":
                    start = response.find("\n", start) + 1
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        
        # Fallback: return whole response
        return response.strip()
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response with better error handling"""
        try:
            # Find JSON block
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                data = json.loads(json_str)
                
                # Validate required fields
                if 'quality_score' not in data:
                    data['quality_score'] = 5
                if 'issues' not in data:
                    data['issues'] = ["Could not parse issues"]
                if 'suggestions' not in data:
                    data['suggestions'] = ["Manual review needed"]
                
                return data
        except Exception as e:
            self.logger.error(f"JSON parsing failed: {e}")
        
        # Fallback
        return {
            "quality_score": 5,
            "issues": ["Could not parse critique response"],
            "suggestions": ["Manual review needed"],
            "improvements_made": [],
            "code_quality_breakdown": {}
        }


async def test_fixed_system():
    """Test the fixed iterative system"""
    
    print("🚀 TESTING FIXED ITERATIVE IMPROVEMENT SYSTEM")
    print("=" * 70)
    
    system = FixedIterativeSystem()
    
    # Test with a complex task
    test_file = {
        "path": "advanced_calculator.py",
        "description": "Create an advanced calculator with scientific functions",
        "requirements": [
            "Basic arithmetic operations (+, -, *, /)",
            "Scientific functions (sin, cos, tan, log, sqrt)",
            "Memory operations (store, recall, clear)",
            "Expression parsing and evaluation",
            "Comprehensive error handling",
            "Unit tests included",
            "Type hints and documentation",
            "History tracking of calculations"
        ]
    }
    
    print(f"🎯 Testing with: {test_file['description']}")
    print(f"📋 Requirements: {len(test_file['requirements'])}")
    
    # Run the fixed system
    success, iterations, final_code = await system.create_file_with_real_improvement(test_file)
    
    print(f"\n📊 RESULTS:")
    print(f"✅ Success: {success}")
    print(f"🔄 Iterations: {iterations}")
    print(f"📄 Final Code Length: {len(final_code)} characters")
    
    # Save the result
    output_path = Path("fixed_system_output.py")
    with open(output_path, 'w') as f:
        f.write(f"# Generated by Fixed Iterative System\n")
        f.write(f"# Iterations: {iterations}\n")
        f.write(f"# Success: {success}\n\n")
        f.write(final_code)
    
    print(f"💾 Saved result to: {output_path}")
    
    return success, iterations


async def main():
    """Run the fixed system test"""
    
    success, iterations = await test_fixed_system()
    
    print(f"\n🎉 FIXED SYSTEM TEST COMPLETED!")
    print(f"✅ Success: {success}")
    print(f"🔄 Total Iterations: {iterations}")
    
    if success:
        print("🎯 The fixed system works! Real iterative improvement achieved!")
    else:
        print("⚠️  System needs further refinement")


if __name__ == "__main__":
    asyncio.run(main())
