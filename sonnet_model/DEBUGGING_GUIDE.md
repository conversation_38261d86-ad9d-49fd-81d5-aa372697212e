# Sonnet Model - Ultra Detailed Debugging & Implementation Guide

## 🔍 System Overview

The Sonnet Model is an agentic code development system with three core components:
- **TaskOrchestrator**: Manages tasks, plans, and workflow coordination
- **CodeGenerator**: Handles LLM-based code generation with validation
- **CritiqueEngine**: Provides multi-layered code analysis and quality assessment

## 🚀 Function Call Graph & Data Flow

### Entry Points & Initialization
```
main.py → create_app() → AgenticSystem() → [TaskOrchestrator, CritiqueEngine, CodeGenerator]
```

### Request Processing Flow
```
API Request → AgenticSystem.process_request() → TaskOrchestrator.process_user_request()
├── _extract_and_trace_requirements()
├── _create_advanced_development_plan()
├── _execute_plan_with_expert_coaching()
└── _validate_quality_gates()
```

### Code Generation Pipeline
```
CodeGenerator.generate_code()
├── _validate_request()
├── _attempt_generation() → LLM Interface
├── _process_code()
└── GenerationResult
```

### Critique Analysis Pipeline
```
CritiqueEngine.analyze_code()
├── _analyze_python() (if Python)
├── StaticAnalyzer.analyze_code()
├── LLMCritic.critique_code()
├── _calculate_quality_score()
└── CritiqueResult
```

## 🔧 Key Debugging Checkpoints

### 1. Request Validation (API Layer)
**Location**: `api/routes.py:58-81`
**Debug Points**:
- Check request payload structure
- Validate system instance availability
- Monitor background task creation

```python
# Debug logging
logger.info(f"Request received: {request.dict()}")
logger.info(f"System state: {system.state}")
```

### 2. Session State Management
**Location**: `system_integration.py:45-74`
**Debug Points**:
- Session initialization status
- Configuration loading
- Component initialization

```python
# Debug session state
logger.info(f"Session ID: {self.current_session_id}")
logger.info(f"Initialized: {self.is_initialized}")
logger.info(f"Coaching enabled: {self.coaching_enabled}")
```

### 3. Plan Creation & Requirements Analysis
**Location**: `task_manager/services/orchestrator.py:117-132`
**Debug Points**:
- Requirement extraction accuracy
- Plan step generation
- Quality gate setup

```python
# Debug plan creation
logger.info(f"Requirements extracted: {len(requirements)}")
logger.info(f"Plan steps created: {len(plan.steps)}")
logger.info(f"Quality gates: {plan.quality_gates}")
```

### 4. Code Generation Process
**Location**: `code_generator/services/code_generator.py:34-102`
**Debug Points**:
- Request validation
- LLM response processing
- Code extraction and validation

```python
# Debug code generation
logger.info(f"Generation attempt {attempt + 1}")
logger.info(f"Temperature: {temperature}")
logger.info(f"Generated code length: {len(extracted_code)}")
```

### 5. Code Critique Analysis
**Location**: `critique_engine/services/critique_engine.py:37-103`
**Debug Points**:
- Analysis type selection
- Issue detection and categorization
- Quality score calculation

```python
# Debug critique process
logger.info(f"Analysis type: {language}")
logger.info(f"Issues found: {len(issues)}")
logger.info(f"Quality score: {quality_score:.2f}")
```

### 6. State Persistence
**Location**: `task_manager/services/state_manager.py`
**Debug Points**:
- Database connection status
- State save/load operations
- Conversation context management

```python
# Debug state management
logger.info(f"Saving state for session: {session_id}")
logger.info(f"Active tasks: {len(active_tasks)}")
logger.info(f"Conversation context: {context}")
```

### 7. Message Bus Communication
**Location**: `shared/message_bus.py`
**Debug Points**:
- Message publishing/subscribing
- Redis connection status
- Message processing callbacks

```python
# Debug message flow
logger.info(f"Publishing message: {message.type}")
logger.info(f"Subscribers: {len(self.subscriptions.get(channel, []))}")
logger.info(f"Message processed: {message.id}")
```

## 🐛 Common Issues & Solutions

### Issue 1: Session Not Initializing
**Symptoms**: `System not initialized` error
**Debug Steps**:
1. Check `system_instance` in `api/routes.py`
2. Verify `AgenticSystem` initialization in `main.py`
3. Check configuration loading

**Solution**:
```python
# In api/routes.py
if system_instance is None:
    logger.error("System instance is None - check main.py initialization")
```

### Issue 2: Code Generation Failures
**Symptoms**: Empty code responses, LLM timeouts
**Debug Steps**:
1. Check LLM configuration in `config.yaml`
2. Verify API keys and endpoints
3. Monitor token usage and limits

**Solution**:
```python
# In code_generator/services/code_generator.py
if not response.get("success", False):
    logger.error(f"LLM generation failed: {response.get('error')}")
    # Implement retry logic or fallback
```

### Issue 3: Message Bus Connection Issues
**Symptoms**: Messages not being processed, Redis errors
**Debug Steps**:
1. Check Redis server status
2. Verify connection configuration
3. Monitor message queue status

**Solution**:
```python
# In shared/message_bus.py
try:
    await self.redis.ping()
    logger.info("Redis connection healthy")
except Exception as e:
    logger.error(f"Redis connection failed: {e}")
    # Fallback to in-memory bus
```

## 📊 Performance Monitoring

### Key Metrics to Track
1. **Request Processing Time**: End-to-end request duration
2. **Code Generation Time**: LLM response time
3. **Critique Analysis Time**: Static + LLM analysis duration
4. **Message Queue Depth**: Pending messages count
5. **Memory Usage**: Component memory consumption

### Monitoring Code Examples
```python
# Performance tracking
import time
from functools import wraps

def track_performance(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        duration = time.time() - start_time
        logger.info(f"{func.__name__} took {duration:.2f}s")
        return result
    return wrapper
```

## 🔄 State Transitions & Lifecycle

### Task State Transitions
```
PENDING → IN_PROGRESS → COMPLETED
    ↓         ↓            ↓
  FAILED ← RETRY ←── VALIDATION_FAILED
```

### Plan Execution Lifecycle
```
CREATED → ANALYZING → EXECUTING → VALIDATING → COMPLETED
    ↓         ↓           ↓           ↓           ↓
  FAILED ← BLOCKED ← PAUSED ← REVIEW_NEEDED ← SUCCESS
```

## 🛠️ Development & Testing Tips

### Unit Testing Key Components
```python
# Test TaskOrchestrator
async def test_task_orchestrator():
    config = load_test_config()
    orchestrator = TaskOrchestrator(config)
    await orchestrator.initialize()
    
    result = await orchestrator.process_user_request("Create a Python function")
    assert result["type"] in ["plan_created", "task_created"]

# Test CodeGenerator
async def test_code_generator():
    generator = CodeGenerator(config)
    request = GenerationRequest(
        task_id="test",
        language=ProgrammingLanguage.PYTHON,
        description="Create a hello world function"
    )
    
    result = await generator.generate_code(request)
    assert result.success
    assert "def" in result.code
```

### Integration Testing
```python
# Test full system integration
async def test_full_system():
    system = AgenticSystem(config)
    await system.initialize_session()
    
    response = await system.process_request("Build a REST API")
    assert response["type"] in ["plan_with_code", "task_executed"]
    assert "code_generated" in response
    assert "critique" in response
```

This guide provides comprehensive debugging information for the Sonnet Model system. Use the visual diagrams above along with these debugging checkpoints to effectively troubleshoot and understand the system's behavior.
