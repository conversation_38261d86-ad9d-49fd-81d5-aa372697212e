# app.py - Main Flask Application
# Description: A simple Flask application with routes and templates

from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

# Home route
@app.route('/')
def home():
    """Renders the home page."""
    return render_template('index.html')

# Example of a parameterized route
@app.route('/user/<username>')
def user(username):
    """Renders a personalized greeting based on the username."""
    return f'Hello, {username}!'

# Error handling for 404 - Page Not Found
@app.errorhandler(404)
def page_not_found(e):
    """Handles 404 errors and renders a custom error template."""
    return render_template('404.html'), 404

# Example of an API route with JSON response
@app.route('/api/data', methods=['GET'])
def api_data():
    """Returns some data in JSON format."""
    data = {
        'message': 'Hello, this is your data!',
        'status': 'success'
    }
    return jsonify(data)

# Main block to run the app
if __name__ == '__main__':
    app.run(debug=True)