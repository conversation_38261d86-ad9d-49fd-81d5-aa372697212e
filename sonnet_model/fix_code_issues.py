#!/usr/bin/env python3
"""
Script to fix CodeIssue constructor calls in Python analyzer
"""
import re

def fix_code_issue_calls(file_path: str):
    """Fix CodeIssue constructor calls to use the helper function"""
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Pattern to match old CodeIssue constructor calls
    # This matches: CodeIssue(line=..., column=..., message=..., severity=..., category=..., filename=...)
    pattern = r'CodeIssue\(\s*line=([^,]+),\s*column=([^,]+),\s*message=([^,]+),\s*severity=([^,]+),\s*category=([^,]+),\s*filename=([^)]+)\s*\)'
    
    def replace_match(match):
        line, column, message, severity, category, filename = match.groups()
        return f'create_code_issue(\n                    title="Code Issue",\n                    message={message},\n                    severity={severity},\n                    category={category},\n                    filename={filename},\n                    line={line},\n                    column={column}\n                )'
    
    # Replace all matches
    new_content = re.sub(pattern, replace_match, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write back the fixed content
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print(f"Fixed CodeIssue calls in {file_path}")

if __name__ == "__main__":
    fix_code_issue_calls("critique_engine/analyzers/python_analyzer.py")
    fix_code_issue_calls("critique_engine/services/llm_critic.py")
