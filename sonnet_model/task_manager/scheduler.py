"""
Task Scheduler for Sonnet Model
Handles scheduling and execution of tasks
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set

from models.task import Task, TaskStatus, TaskType
from models.code_generation import GenerationRequest
from models.critique import CritiqueRequest
from shared.message_bus import Message, MessageType


class TaskScheduler:
    """
    Task Scheduler
    
    Handles scheduling and execution of tasks
    """
    
    def __init__(self, orchestrator, message_bus, config: Dict[str, Any]):
        """
        Initialize Task Scheduler
        
        Args:
            orchestrator: Task Orchestrator instance
            message_bus: Message Bus instance
            config: Scheduler configuration
        """
        self.orchestrator = orchestrator
        self.message_bus = message_bus
        self.config = config
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = config.get("max_concurrent_tasks", 3)
        self.task_timeout_seconds = config.get("task_timeout_seconds", 600)
        self.retry_attempts = config.get("retry_attempts", 3)
        self.retry_delay_seconds = config.get("retry_delay_seconds", 30)
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.scheduler_task = None
    
    async def start(self) -> None:
        """Start the scheduler"""
        self.logger.info("Starting Task Scheduler")
        self.running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
    
    async def stop(self) -> None:
        """Stop the scheduler"""
        self.logger.info("Stopping Task Scheduler")
        self.running = False
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        # Cancel all active tasks
        for task_id, task in list(self.active_tasks.items()):
            self.logger.info(f"Cancelling task {task_id}")
            task.cancel()
        
        if self.active_tasks:
            await asyncio.gather(*self.active_tasks.values(), return_exceptions=True)
    
    async def _scheduler_loop(self) -> None:
        """
        Main scheduler loop
        
        Periodically checks for tasks that are ready to be executed
        """
        self.logger.info("Starting scheduler loop")
        
        try:
            while self.running:
                try:
                    # Get all projects
                    projects = await self.orchestrator.state_manager.get_all_projects()
                    
                    for project in projects:
                        # Skip completed projects
                        if project.is_completed():
                            continue
                        
                        # Get ready tasks
                        ready_tasks = await self._get_ready_tasks(project.id)
                        
                        # Schedule tasks
                        for task in ready_tasks:
                            if len(self.active_tasks) < self.max_concurrent_tasks:
                                await self._schedule_task(project.id, task)
                    
                    # Check for timed out tasks
                    await self._check_timeouts()
                    
                except Exception as e:
                    self.logger.error(f"Error in scheduler loop: {e}", exc_info=True)
                
                # Sleep for a short time
                await asyncio.sleep(1)
        
        except asyncio.CancelledError:
            self.logger.info("Scheduler loop cancelled")
    
    async def _get_ready_tasks(self, project_id: str) -> List[Task]:
        """
        Get tasks that are ready to be executed
        
        Args:
            project_id: Project ID
            
        Returns:
            List of ready tasks
        """
        project = await self.orchestrator.state_manager.get_project(project_id)
        if not project:
            return []
        
        ready_tasks = []
        
        for task in project.get_active_tasks():
            # Skip tasks that are already running
            if task.id in self.active_tasks:
                continue
            
            # Skip tasks that are not pending
            if task.status != TaskStatus.PENDING:
                continue
            
            # Check if all dependencies are completed
            dependencies_completed = True
            for dep_id in task.dependencies:
                dep_task = project.get_task(dep_id)
                if not dep_task or not dep_task.is_completed():
                    dependencies_completed = False
                    break
            
            if dependencies_completed:
                ready_tasks.append(task)
        
        # Sort by priority (higher first)
        ready_tasks.sort(key=lambda t: t.priority.value, reverse=True)
        
        return ready_tasks
    
    async def _schedule_task(self, project_id: str, task: Task) -> None:
        """
        Schedule a task for execution
        
        Args:
            project_id: Project ID
            task: Task to schedule
        """
        self.logger.info(f"Scheduling task {task.id} ({task.title})")
        
        # Update task status
        await self.orchestrator.update_task(
            project_id, 
            task.id, 
            status=TaskStatus.IN_PROGRESS
        )
        
        # Create asyncio task
        asyncio_task = asyncio.create_task(
            self._execute_task(project_id, task)
        )
        
        # Store in active tasks
        self.active_tasks[task.id] = asyncio_task
        
        # Add callback to remove from active tasks when done
        asyncio_task.add_done_callback(
            lambda _: self.active_tasks.pop(task.id, None)
        )
    
    async def _execute_task(self, project_id: str, task: Task) -> None:
        """
        Execute a task
        
        Args:
            project_id: Project ID
            task: Task to execute
        """
        start_time = time.time()
        self.logger.info(f"Executing task {task.id} ({task.title})")
        
        try:
            # Increment iteration counter
            task.increment_iteration()
            
            # Check if max iterations reached
            if task.has_reached_max_iterations():
                self.logger.warning(
                    f"Task {task.id} reached max iterations ({task.max_iterations})"
                )
                await self.orchestrator.update_task(
                    project_id,
                    task.id,
                    status=TaskStatus.FAILED,
                    metadata={
                        **task.metadata,
                        "failure_reason": "Max iterations reached"
                    }
                )
                return
            
            # Route task to appropriate handler
            if task.task_type == TaskType.CODE_GENERATION:
                await self._handle_code_generation_task(project_id, task)
            elif task.task_type == TaskType.CODE_REVIEW:
                await self._handle_code_review_task(project_id, task)
            else:
                self.logger.warning(f"Unsupported task type: {task.task_type}")
                await self.orchestrator.update_task(
                    project_id,
                    task.id,
                    status=TaskStatus.FAILED,
                    metadata={
                        **task.metadata,
                        "failure_reason": f"Unsupported task type: {task.task_type}"
                    }
                )
        
        except Exception as e:
            self.logger.error(
                f"Error executing task {task.id}: {e}", 
                exc_info=True
            )
            
            # Update task status
            await self.orchestrator.update_task(
                project_id,
                task.id,
                status=TaskStatus.FAILED,
                metadata={
                    **task.metadata,
                    "failure_reason": str(e),
                    "traceback": str(e.__traceback__)
                }
            )
        
        finally:
            # Log execution time
            execution_time = time.time() - start_time
            self.logger.info(
                f"Task {task.id} executed in {execution_time:.2f} seconds"
            )
    
    async def _handle_code_generation_task(self, project_id: str, task: Task) -> None:
        """
        Handle code generation task
        
        Args:
            project_id: Project ID
            task: Task to handle
        """
        self.logger.info(f"Handling code generation task {task.id}")
        
        # Create generation request
        generation_request = GenerationRequest(
            task_id=task.id,
            language=task.metadata.get("language", "python"),
            framework=task.metadata.get("framework"),
            description=task.description,
            requirements=task.metadata.get("requirements", []),
            context=task.metadata.get("context"),
            dependencies=task.dependencies,
            examples=task.metadata.get("examples", []),
            constraints=task.metadata.get("constraints", {}),
            metadata=task.metadata,
            iteration=task.iteration
        )
        
        # Publish code generation request
        await self.message_bus.publish(
            "code_generation",
            Message(
                type=MessageType.CODE_GENERATION_REQUEST,
                payload=generation_request.model_dump()
            )
        )
        
        # Update task status
        await self.orchestrator.update_task(
            project_id,
            task.id,
            status=TaskStatus.WAITING_FOR_REVIEW
        )
    
    async def _handle_code_review_task(self, project_id: str, task: Task) -> None:
        """
        Handle code review task
        
        Args:
            project_id: Project ID
            task: Task to handle
        """
        self.logger.info(f"Handling code review task {task.id}")
        
        # Create critique request
        critique_request = CritiqueRequest(
            task_id=task.id,
            code_files=task.metadata.get("code_files", []),
            language=task.metadata.get("language", "python"),
            framework=task.metadata.get("framework"),
            requirements=task.metadata.get("requirements", []),
            context=task.metadata.get("context"),
            iteration=task.iteration,
            metadata=task.metadata
        )
        
        # Publish critique request
        await self.message_bus.publish(
            "critique",
            Message(
                type=MessageType.CRITIQUE_REQUEST,
                payload=critique_request.model_dump()
            )
        )
        
        # Update task status
        await self.orchestrator.update_task(
            project_id,
            task.id,
            status=TaskStatus.WAITING_FOR_REVIEW
        )
    
    async def _check_timeouts(self) -> None:
        """Check for timed out tasks"""
        now = datetime.utcnow()
        
        for project in await self.orchestrator.state_manager.get_all_projects():
            for task in project.tasks:
                # Skip tasks that are not in progress
                if task.status != TaskStatus.IN_PROGRESS:
                    continue
                
                # Skip tasks that are not active
                if task.id not in self.active_tasks:
                    continue
                
                # Check if task has timed out
                last_updated = task.updated_at
                timeout = timedelta(seconds=self.task_timeout_seconds)
                
                if now - last_updated > timeout:
                    self.logger.warning(
                        f"Task {task.id} timed out after {self.task_timeout_seconds} seconds"
                    )
                    
                    # Cancel the task
                    self.active_tasks[task.id].cancel()
                    
                    # Update task status
                    await self.orchestrator.update_task(
                        project.id,
                        task.id,
                        status=TaskStatus.FAILED,
                        metadata={
                            **task.metadata,
                            "failure_reason": "Task timed out"
                        }
                    )
