"""
Task Model - Represents individual tasks in the system
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """Task priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Task(BaseModel):
    """Task model representing a single work item"""
    
    id: str = Field(..., description="Unique task identifier")
    title: str = Field(..., description="Task title")
    description: str = Field(..., description="Detailed task description")
    project_id: str = Field(..., description="Associated project ID")
    
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="Current task status")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    
    # Dependencies and relationships
    dependencies: List[str] = Field(default_factory=list, description="List of task IDs this task depends on")
    blocks: List[str] = Field(default_factory=list, description="List of task IDs this task blocks")
    
    # Requirements and specifications
    requirements: List[str] = Field(default_factory=list, description="List of requirements")
    acceptance_criteria: List[str] = Field(default_factory=list, description="Acceptance criteria")
    
    # Code generation specifics
    language: str = Field(default="python", description="Programming language")
    framework: Optional[str] = Field(default=None, description="Framework to use")
    file_path: Optional[str] = Field(default=None, description="Target file path")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    started_at: Optional[datetime] = Field(default=None, description="Start timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")
    
    # Progress tracking
    iteration_count: int = Field(default=0, description="Number of iterations attempted")
    max_iterations: int = Field(default=5, description="Maximum iterations allowed")
    
    # Results and feedback
    generated_code: Optional[str] = Field(default=None, description="Generated code")
    feedback_history: List[str] = Field(default_factory=list, description="Feedback from iterations")
    quality_score: float = Field(default=0.0, description="Quality score (0.0 to 1.0)")
    
    # Additional metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    tags: List[str] = Field(default_factory=list, description="Task tags")
    
    def is_ready(self) -> bool:
        """Check if task is ready to be processed"""
        return (
            self.status == TaskStatus.PENDING and
            len(self.dependencies) == 0  # All dependencies resolved
        )
    
    def is_blocked(self) -> bool:
        """Check if task is blocked by dependencies"""
        return len(self.dependencies) > 0
    
    def can_start(self, completed_task_ids: List[str]) -> bool:
        """Check if task can start given completed tasks"""
        return all(dep_id in completed_task_ids for dep_id in self.dependencies)
    
    def mark_started(self):
        """Mark task as started"""
        self.status = TaskStatus.IN_PROGRESS
        self.started_at = datetime.now()
        self.updated_at = datetime.now()
    
    def mark_completed(self, code: str = None, quality_score: float = 0.0):
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
        if code:
            self.generated_code = code
        self.quality_score = quality_score
    
    def mark_failed(self, reason: str = None):
        """Mark task as failed"""
        self.status = TaskStatus.FAILED
        self.updated_at = datetime.now()
        if reason:
            self.metadata["failure_reason"] = reason
    
    def add_feedback(self, feedback: str):
        """Add feedback to history"""
        self.feedback_history.append(feedback)
        self.updated_at = datetime.now()
    
    def increment_iteration(self):
        """Increment iteration count"""
        self.iteration_count += 1
        self.updated_at = datetime.now()
    
    def get_duration(self) -> Optional[float]:
        """Get task duration in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """Create from dictionary"""
        return cls(**data)
