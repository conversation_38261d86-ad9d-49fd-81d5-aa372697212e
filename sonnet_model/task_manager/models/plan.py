"""
Plan Model - Represents project plans and their structure
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class PlanStatus(str, Enum):
    """Plan status enumeration"""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class StepStatus(str, Enum):
    """Step status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class PlanStep(BaseModel):
    """Individual step within a plan"""
    
    id: str = Field(..., description="Unique step identifier")
    name: str = Field(..., description="Step name")
    description: str = Field(..., description="Step description")
    status: StepStatus = Field(default=StepStatus.PENDING, description="Step status")
    
    # Dependencies and ordering
    dependencies: List[str] = Field(default_factory=list, description="Dependent step IDs")
    order: int = Field(default=0, description="Execution order")
    
    # Execution details
    estimated_duration: Optional[int] = Field(default=None, description="Estimated duration in minutes")
    actual_duration: Optional[int] = Field(default=None, description="Actual duration in minutes")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    started_at: Optional[datetime] = Field(default=None, description="Start timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")
    
    # Results
    output: Optional[str] = Field(default=None, description="Step output or results")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")


class Plan(BaseModel):
    """Plan model representing a project plan"""
    
    id: str = Field(..., description="Unique plan identifier")
    name: str = Field(..., description="Plan name")
    description: str = Field(..., description="Plan description")
    user_input: str = Field(..., description="Original user input")
    
    status: PlanStatus = Field(default=PlanStatus.DRAFT, description="Plan status")
    
    # Plan structure
    goals: List[str] = Field(default_factory=list, description="High-level goals")
    requirements: List[str] = Field(default_factory=list, description="Requirements")
    constraints: List[str] = Field(default_factory=list, description="Constraints")
    steps: List[PlanStep] = Field(default_factory=list, description="Plan execution steps")
    
    # Technical specifications
    language: str = Field(default="python", description="Primary programming language")
    frameworks: List[str] = Field(default_factory=list, description="Frameworks to use")
    architecture: Optional[str] = Field(default=None, description="Architecture pattern")
    
    # Task management
    task_ids: List[str] = Field(default_factory=list, description="Associated task IDs")
    total_tasks: int = Field(default=0, description="Total number of tasks")
    completed_tasks: int = Field(default=0, description="Number of completed tasks")
    
    # Timeline
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    started_at: Optional[datetime] = Field(default=None, description="Start timestamp")
    completed_at: Optional[datetime] = Field(default=None, description="Completion timestamp")
    estimated_duration: Optional[int] = Field(default=None, description="Estimated duration in hours")
    
    # Quality metrics
    overall_quality_score: float = Field(default=0.0, description="Overall quality score")
    success_rate: float = Field(default=0.0, description="Task success rate")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    tags: List[str] = Field(default_factory=list, description="Plan tags")
    
    def get_progress(self) -> float:
        """Get completion progress as percentage"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100.0
    
    def is_completed(self) -> bool:
        """Check if plan is completed"""
        return self.status == PlanStatus.COMPLETED or (
            self.total_tasks > 0 and self.completed_tasks >= self.total_tasks
        )
    
    def add_task(self, task_id: str):
        """Add a task to the plan"""
        if task_id not in self.task_ids:
            self.task_ids.append(task_id)
            self.total_tasks = len(self.task_ids)
            self.updated_at = datetime.now()
    
    def add_step(self, step: PlanStep):
        """Add a step to the plan"""
        if step not in self.steps:
            self.steps.append(step)
            self.updated_at = datetime.now()
    
    def mark_task_completed(self):
        """Mark a task as completed"""
        self.completed_tasks += 1
        self.updated_at = datetime.now()
        
        if self.completed_tasks >= self.total_tasks:
            self.mark_completed()
    
    def mark_started(self):
        """Mark plan as started"""
        self.status = PlanStatus.ACTIVE
        self.started_at = datetime.now()
        self.updated_at = datetime.now()
    
    def mark_completed(self):
        """Mark plan as completed"""
        self.status = PlanStatus.COMPLETED
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
    
    def get_duration(self) -> Optional[float]:
        """Get plan duration in hours"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds() / 3600
        return None
    
    def update_quality_score(self, task_quality_scores: List[float]):
        """Update overall quality score based on task scores"""
        if task_quality_scores:
            self.overall_quality_score = sum(task_quality_scores) / len(task_quality_scores)
            self.updated_at = datetime.now()
    
    def calculate_success_rate(self, successful_tasks: int):
        """Calculate success rate"""
        if self.total_tasks > 0:
            self.success_rate = (successful_tasks / self.total_tasks) * 100.0
        else:
            self.success_rate = 0.0
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return self.dict()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Plan":
        """Create from dictionary"""
        return cls(**data)
