"""
Task Orchestrator for Sonnet Model
Manages the execution of tasks and coordinates between components
"""
import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set

from models.task import Task, TaskStatus, Project
from shared.message_bus import MessageBus, Message, MessageType


class TaskOrchestrator:
    """
    Task Orchestrator
    
    Manages the execution of tasks and coordinates between components
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Task Orchestrator
        
        Args:
            config: Orchestrator configuration
        """
        self.config = config
        self.state_manager = None  # Will be set during initialization
        self.message_bus = None
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = config.get("max_concurrent_tasks", 3)
        self.task_timeout_seconds = config.get("task_timeout_seconds", 600)
        self.retry_attempts = config.get("retry_attempts", 3)
        self.retry_delay_seconds = config.get("retry_delay_seconds", 30)
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.scheduler_task = None
    
    async def initialize(self) -> None:
        """Initialize the orchestrator"""
        from task_manager.state_manager import StateManager
        
        self.logger.info("Initializing Task Orchestrator")
        
        # Initialize state manager
        self.state_manager = StateManager(self.config)
        await self.state_manager.initialize()
        
        # Start the scheduler
        self.running = True
        self.scheduler_task = asyncio.create_task(self._scheduler())
        
        self.logger.info("Task Orchestrator initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the orchestrator"""
        self.logger.info("Shutting down Task Orchestrator")
        
        # Stop the scheduler
        self.running = False
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        # Cancel all active tasks
        for task_id, task in list(self.active_tasks.items()):
            self.logger.info(f"Cancelling task {task_id}")
            task.cancel()
        
        if self.active_tasks:
            await asyncio.gather(*self.active_tasks.values(), return_exceptions=True)
        
        # Shutdown state manager
        if self.state_manager:
            await self.state_manager.shutdown()
        
        self.logger.info("Task Orchestrator shut down")
    
    def register_message_bus(self, message_bus: MessageBus) -> None:
        """
        Register message bus
        
        Args:
            message_bus: Message bus instance
        """
        self.message_bus = message_bus
    
    async def create_project(self, name: str, description: str) -> Project:
        """
        Create a new project
        
        Args:
            name: Project name
            description: Project description
            
        Returns:
            Created project
        """
        project = Project(name=name, description=description)
        await self.state_manager.save_project(project)
        return project
    
    async def get_project(self, project_id: str) -> Optional[Project]:
        """
        Get a project by ID
        
        Args:
            project_id: Project ID
            
        Returns:
            Project if found, None otherwise
        """
        return await self.state_manager.get_project(project_id)
    
    async def create_task(
        self, 
        project_id: str, 
        task: Task
    ) -> Task:
        """
        Create a new task
        
        Args:
            project_id: Project ID
            task: Task to create
            
        Returns:
            Created task
            
        Raises:
            ValueError: If the project does not exist
        """
        project = await self.state_manager.get_project(project_id)
        if not project:
            raise ValueError(f"Project not found: {project_id}")
        
        # Set project ID if not already set
        if not task.project_id:
            task.project_id = project_id
        
        # Add task to project
        project.add_task(task)
        
        # Save project
        await self.state_manager.save_project(project)
        
        # Publish task created message
        if self.message_bus:
            await self.message_bus.publish(
                "tasks",
                Message(
                    type=MessageType.TASK_CREATED,
                    payload={"task_id": task.id, "project_id": project_id}
                )
            )
        
        return task
    
    async def update_task(
        self, 
        project_id: str, 
        task_id: str, 
        **kwargs
    ) -> bool:
        """
        Update a task
        
        Args:
            project_id: Project ID
            task_id: Task ID
            **kwargs: Task attributes to update
            
        Returns:
            True if the task was updated, False otherwise
        """
        project = await self.state_manager.get_project(project_id)
        if not project:
            return False
        
        # Update task
        updated = project.update_task(task_id, **kwargs)
        if not updated:
            return False
        
        # Save project
        await self.state_manager.save_project(project)
        
        # Publish task updated message
        if self.message_bus:
            await self.message_bus.publish(
                "tasks",
                Message(
                    type=MessageType.TASK_UPDATED,
                    payload={"task_id": task_id, "project_id": project_id}
                )
            )
        
        return True
    
    async def get_task(
        self, 
        project_id: str, 
        task_id: str
    ) -> Optional[Task]:
        """
        Get a task by ID
        
        Args:
            project_id: Project ID
            task_id: Task ID
            
        Returns:
            Task if found, None otherwise
        """
        project = await self.state_manager.get_project(project_id)
        if not project:
            return None
        
        return project.get_task(task_id)
