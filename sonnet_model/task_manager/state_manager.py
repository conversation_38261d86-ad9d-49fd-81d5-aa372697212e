"""
State Manager for Task Manager component
Handles persistence of projects and tasks
"""
import asyncio
import json
import logging
import os
import sqlite3
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

import aiosqlite

from models.task import Project, Task


class StateManager:
    """
    State Manager
    
    Handles persistence of projects and tasks using SQLite
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize State Manager
        
        Args:
            config: State Manager configuration
        """
        self.config = config
        self.db_config = config.get("database", {})
        self.db_type = self.db_config.get("type", "sqlite")
        
        if self.db_type == "sqlite":
            self.db_path = self.db_config.get("path", "data/sonnet.db")
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        else:
            raise ValueError(f"Unsupported database type: {self.db_type}")
        
        self.connection = None
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> None:
        """Initialize the state manager"""
        self.logger.info("Initializing State Manager")
        
        if self.db_type == "sqlite":
            # Connect to SQLite database
            self.connection = await aiosqlite.connect(self.db_path)
            
            # Create tables if they don't exist
            await self._create_tables()
        
        self.logger.info("State Manager initialized")
    
    async def shutdown(self) -> None:
        """Shutdown the state manager"""
        self.logger.info("Shutting down State Manager")
        
        if self.connection:
            await self.connection.close()
        
        self.logger.info("State Manager shut down")
    
    async def _create_tables(self) -> None:
        """Create database tables if they don't exist"""
        if not self.connection:
            raise RuntimeError("Database connection not initialized")
        
        # Create projects table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                data TEXT NOT NULL
            )
        """)
        
        # Commit changes
        await self.connection.commit()
    
    async def save_project(self, project: Project) -> None:
        """
        Save a project to the database
        
        Args:
            project: Project to save
        """
        if not self.connection:
            raise RuntimeError("Database connection not initialized")
        
        # Convert project to JSON
        project_json = project.model_dump_json()
        
        # Insert or update project
        await self.connection.execute(
            "INSERT OR REPLACE INTO projects (id, data) VALUES (?, ?)",
            (project.id, project_json)
        )
        
        # Commit changes
        await self.connection.commit()
    
    async def get_project(self, project_id: str) -> Optional[Project]:
        """
        Get a project by ID
        
        Args:
            project_id: Project ID
            
        Returns:
            Project if found, None otherwise
        """
        if not self.connection:
            raise RuntimeError("Database connection not initialized")
        
        # Query project
        async with self.connection.execute(
            "SELECT data FROM projects WHERE id = ?",
            (project_id,)
        ) as cursor:
            row = await cursor.fetchone()
            
            if not row:
                return None
            
            # Parse project JSON
            project_data = json.loads(row[0])
            return Project.model_validate(project_data)
    
    async def delete_project(self, project_id: str) -> bool:
        """
        Delete a project by ID
        
        Args:
            project_id: Project ID
            
        Returns:
            True if the project was deleted, False otherwise
        """
        if not self.connection:
            raise RuntimeError("Database connection not initialized")
        
        # Delete project
        cursor = await self.connection.execute(
            "DELETE FROM projects WHERE id = ?",
            (project_id,)
        )
        
        # Commit changes
        await self.connection.commit()
        
        # Check if any rows were affected
        return cursor.rowcount > 0
    
    async def get_all_projects(self) -> List[Project]:
        """
        Get all projects
        
        Returns:
            List of all projects
        """
        if not self.connection:
            raise RuntimeError("Database connection not initialized")
        
        projects = []
        
        # Query all projects
        async with self.connection.execute("SELECT data FROM projects") as cursor:
            async for row in cursor:
                # Parse project JSON
                project_data = json.loads(row[0])
                projects.append(Project.model_validate(project_data))
        
        return projects
    
    async def get_project_tasks(self, project_id: str) -> List[Task]:
        """
        Get all tasks for a project
        
        Args:
            project_id: Project ID
            
        Returns:
            List of tasks for the project
        """
        project = await self.get_project(project_id)
        if not project:
            return []
        
        return project.tasks
