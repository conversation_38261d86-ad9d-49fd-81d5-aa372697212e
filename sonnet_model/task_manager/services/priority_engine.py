"""
Priority Engine - Manages task prioritization and scheduling
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from ..models.task import Task, TaskPriority, TaskStatus


class PriorityEngine:
    """Manages task prioritization and scheduling logic"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Priority weights
        self.priority_weights = {
            TaskPriority.CRITICAL: 100,
            TaskPriority.HIGH: 75,
            TaskPriority.MEDIUM: 50,
            TaskPriority.LOW: 25
        }
        
        # Configuration
        self.consider_dependencies = config.get("consider_dependencies", True)
        self.age_factor_weight = config.get("age_factor_weight", 0.1)
        self.iteration_penalty = config.get("iteration_penalty", 5)
    
    def sort_tasks(self, tasks: List[Task]) -> List[Task]:
        """Sort tasks by priority score (highest first)"""
        tasks.sort(key=self.calculate_priority_score, reverse=True)
        return tasks
    
    def calculate_priority_score(self, task: Task) -> float:
        """Calculate priority score for a task"""
        # Base priority score
        base_score = self.priority_weights.get(task.priority, 50)
        
        # Age factor - older tasks get slight boost
        age_hours = self._get_task_age_hours(task)
        age_bonus = age_hours * self.age_factor_weight
        
        # Iteration penalty - tasks that have failed multiple times get lower priority
        iteration_penalty = task.iteration_count * self.iteration_penalty
        
        # Dependency bonus - tasks with no dependencies get slight boost
        dependency_bonus = 10 if len(task.dependencies) == 0 else 0
        
        # Quality factor - tasks with higher quality requirements get boost
        quality_bonus = self._calculate_quality_bonus(task)
        
        total_score = base_score + age_bonus + dependency_bonus + quality_bonus - iteration_penalty
        
        self.logger.debug(f"Task {task.id} priority score: {total_score} "
                         f"(base: {base_score}, age: {age_bonus}, deps: {dependency_bonus}, "
                         f"quality: {quality_bonus}, iterations: -{iteration_penalty})")
        
        return total_score
    
    def get_next_executable_task(self, tasks: List[Task], completed_task_ids: List[str]) -> Task:
        """Get the next task that can be executed"""
        executable_tasks = []
        
        for task in tasks:
            if task.status == TaskStatus.PENDING and task.can_start(completed_task_ids):
                executable_tasks.append(task)
        
        if not executable_tasks:
            return None
        
        # Sort by priority and return highest
        self.sort_tasks(executable_tasks)
        return executable_tasks[0]
    
    def reorder_tasks_by_dependencies(self, tasks: List[Task]) -> List[Task]:
        """Reorder tasks considering dependencies (topological sort)"""
        if not self.consider_dependencies:
            return self.sort_tasks(tasks)
        
        # Create dependency graph
        task_map = {task.id: task for task in tasks}
        in_degree = {task.id: 0 for task in tasks}
        
        # Calculate in-degrees
        for task in tasks:
            for dep_id in task.dependencies:
                if dep_id in in_degree:
                    in_degree[task.id] += 1
        
        # Topological sort with priority consideration
        result = []
        available = [task for task in tasks if in_degree[task.id] == 0]
        
        while available:
            # Sort available tasks by priority
            self.sort_tasks(available)
            current = available.pop(0)
            result.append(current)
            
            # Update in-degrees for tasks that depend on current task
            for task in tasks:
                if current.id in task.dependencies:
                    in_degree[task.id] -= 1
                    if in_degree[task.id] == 0 and task not in result:
                        available.append(task)
        
        return result
    
    def estimate_task_duration(self, task: Task) -> int:
        """Estimate task duration in minutes"""
        # Base duration by priority
        base_durations = {
            TaskPriority.CRITICAL: 120,  # 2 hours
            TaskPriority.HIGH: 90,       # 1.5 hours
            TaskPriority.MEDIUM: 60,     # 1 hour
            TaskPriority.LOW: 30         # 30 minutes
        }
        
        base_duration = base_durations.get(task.priority, 60)
        
        # Adjust based on complexity indicators
        complexity_multiplier = 1.0
        
        # More requirements = more complex
        if len(task.requirements) > 3:
            complexity_multiplier += 0.3
        
        # More dependencies = more complex
        if len(task.dependencies) > 2:
            complexity_multiplier += 0.2
        
        # Previous iterations indicate complexity
        if task.iteration_count > 0:
            complexity_multiplier += task.iteration_count * 0.2
        
        return int(base_duration * complexity_multiplier)
    
    def get_critical_path_tasks(self, tasks: List[Task]) -> List[Task]:
        """Identify tasks on the critical path"""
        # Simple critical path: tasks with the most dependents
        dependents_count = {task.id: 0 for task in tasks}
        
        for task in tasks:
            for dep_id in task.dependencies:
                if dep_id in dependents_count:
                    dependents_count[dep_id] += 1
        
        # Tasks with most dependents are on critical path
        critical_tasks = []
        max_dependents = max(dependents_count.values()) if dependents_count else 0
        
        for task in tasks:
            if dependents_count[task.id] >= max_dependents * 0.7:  # Top 30%
                critical_tasks.append(task)
        
        return critical_tasks
    
    def _get_task_age_hours(self, task: Task) -> float:
        """Get task age in hours"""
        if task.created_at:
            return (datetime.now() - task.created_at).total_seconds() / 3600
        return 0.0
    
    def _calculate_quality_bonus(self, task: Task) -> float:
        """Calculate quality bonus based on task characteristics"""
        bonus = 0.0
        
        # Tasks with acceptance criteria get bonus
        if task.acceptance_criteria:
            bonus += 5
        
        # Tasks with specific file paths get bonus (more concrete)
        if task.file_path:
            bonus += 3
        
        # Tasks with framework specification get bonus
        if task.framework:
            bonus += 2
        
        return bonus
