"""
Command Execution Service
Handles intelligent execution of bash/shell commands from the main LLM
"""

import asyncio
import subprocess
import shlex
import os
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from task_manager.models.task import Task


class CommandType(str, Enum):
    """Command type classification"""
    SAFE = "safe"
    POTENTIALLY_UNSAFE = "potentially_unsafe" 
    UNSAFE = "unsafe"
    SYSTEM_CRITICAL = "system_critical"


class CommandStatus(str, Enum):
    """Command execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    BLOCKED = "blocked"


@dataclass
class CommandRequest:
    """Command execution request"""
    command: str
    working_directory: str = "."
    timeout: int = 30
    capture_output: bool = True
    shell: bool = True
    environment: Optional[Dict[str, str]] = None
    task_id: Optional[str] = None
    llm_context: Optional[str] = None


@dataclass
class CommandResult:
    """Command execution result"""
    command: str
    status: CommandStatus
    exit_code: int
    stdout: str
    stderr: str
    execution_time: float
    working_directory: str
    error_message: Optional[str] = None
    safety_assessment: Optional[str] = None


class CommandExecutor:
    """Intelligent command executor for LLM requests"""
    
    def __init__(self):
        """Initialize command executor"""
        self.logger = logging.getLogger(__name__)
        self.running_commands: Dict[str, asyncio.Task] = {}
        
        # Safety patterns for command classification
        self.safe_commands = {
            'ls', 'pwd', 'cat', 'head', 'tail', 'grep', 'find', 'wc', 'sort',
            'echo', 'date', 'whoami', 'which', 'type', 'file', 'stat', 'du',
            'df', 'ps', 'top', 'htop', 'free', 'uname', 'lscpu', 'lsblk'
        }
        
        self.potentially_unsafe_commands = {
            'mkdir', 'touch', 'cp', 'mv', 'chmod', 'chown', 'ln', 'git',
            'npm', 'pip', 'python', 'node', 'java', 'gcc', 'make', 'cmake',
            'docker', 'kubectl', 'curl', 'wget', 'ssh', 'scp', 'rsync'
        }
        
        self.unsafe_commands = {
            'rm', 'rmdir', 'dd', 'mkfs', 'fdisk', 'mount', 'umount',
            'kill', 'killall', 'pkill', 'sudo', 'su', 'passwd', 'useradd',
            'userdel', 'groupadd', 'groupdel', 'crontab', 'systemctl',
            'service', 'iptables', 'firewall-cmd', 'reboot', 'shutdown'
        }
    
    async def execute_command(self, request: CommandRequest) -> CommandResult:
        """
        Execute command with intelligent safety assessment
        
        Args:
            request: Command execution request
            
        Returns:
            Command execution result
        """
        start_time = asyncio.get_event_loop().time()
        
        # Assess command safety
        safety_assessment = self._assess_command_safety(request.command)
        command_type = self._classify_command(request.command)
        
        self.logger.info(f"Executing command: {request.command}")
        self.logger.info(f"Safety assessment: {safety_assessment}")
        self.logger.info(f"Command type: {command_type}")
        
        # Handle unsafe commands
        if command_type == CommandType.UNSAFE:
            return CommandResult(
                command=request.command,
                status=CommandStatus.BLOCKED,
                exit_code=-1,
                stdout="",
                stderr="Command blocked for safety reasons",
                execution_time=0.0,
                working_directory=request.working_directory,
                error_message="Unsafe command blocked by safety filter",
                safety_assessment=safety_assessment
            )
        
        # Execute command
        return await self._execute_command_internal(request, safety_assessment, start_time)
    
    async def _execute_command_internal(
        self, 
        request: CommandRequest, 
        safety_assessment: str,
        start_time: float
    ) -> CommandResult:
        """Internal command execution"""
        
        # Prepare environment
        env = os.environ.copy()
        if request.environment:
            env.update(request.environment)
        
        # Prepare command
        if request.shell:
            cmd = request.command
            shell = True
        else:
            cmd = shlex.split(request.command)
            shell = False
        
        # Execute command
        process = None
        stdout = ""
        stderr = ""
        exit_code = 0
        
        try:
            process = await asyncio.create_subprocess_shell(
                cmd if shell else ' '.join(cmd),
                stdout=asyncio.subprocess.PIPE if request.capture_output else None,
                stderr=asyncio.subprocess.PIPE if request.capture_output else None,
                cwd=request.working_directory,
                env=env,
                shell=shell
            )
            
            # Wait for completion with timeout
            stdout_data, stderr_data = await asyncio.wait_for(
                process.communicate(),
                timeout=request.timeout
            )
            
            if request.capture_output:
                stdout = stdout_data.decode('utf-8', errors='replace') if stdout_data else ""
                stderr = stderr_data.decode('utf-8', errors='replace') if stderr_data else ""
            
            exit_code = process.returncode
            status = CommandStatus.COMPLETED if exit_code == 0 else CommandStatus.FAILED
            
        except asyncio.TimeoutError:
            if process:
                process.terminate()
                await process.wait()
            status = CommandStatus.CANCELLED
            exit_code = -1
            stderr = f"Command timed out after {request.timeout} seconds"
            
        except Exception as e:
            status = CommandStatus.FAILED
            exit_code = -1
            stderr = f"Execution error: {str(e)}"
        
        execution_time = asyncio.get_event_loop().time() - start_time
        
        return CommandResult(
            command=request.command,
            status=status,
            exit_code=exit_code,
            stdout=stdout,
            stderr=stderr,
            execution_time=execution_time,
            working_directory=request.working_directory,
            safety_assessment=safety_assessment
        )
    
    def _classify_command(self, command: str) -> CommandType:
        """Classify command safety level"""
        # Extract base command
        parts = shlex.split(command)
        if not parts:
            return CommandType.SAFE
        
        base_cmd = parts[0].split('/')[-1]  # Handle full paths
        
        if base_cmd in self.unsafe_commands:
            return CommandType.UNSAFE
        elif base_cmd in self.potentially_unsafe_commands:
            return CommandType.POTENTIALLY_UNSAFE
        elif base_cmd in self.safe_commands:
            return CommandType.SAFE
        else:
            # Unknown command - treat as potentially unsafe
            return CommandType.POTENTIALLY_UNSAFE
    
    def _assess_command_safety(self, command: str) -> str:
        """Provide detailed safety assessment"""
        command_type = self._classify_command(command)
        
        if command_type == CommandType.SAFE:
            return "Safe: Read-only operation with no system modification risk"
        elif command_type == CommandType.POTENTIALLY_UNSAFE:
            return "Potentially unsafe: May modify files or system state - requires caution"
        elif command_type == CommandType.UNSAFE:
            return "Unsafe: High risk of system damage or security compromise - blocked"
        else:
            return "Unknown: Command safety cannot be determined - treating as potentially unsafe"
    
    async def execute_command_sequence(
        self, 
        commands: List[CommandRequest]
    ) -> List[CommandResult]:
        """Execute a sequence of commands"""
        results = []
        
        for request in commands:
            result = await self.execute_command(request)
            results.append(result)
            
            # Stop on failure if command was critical
            if result.status == CommandStatus.FAILED and result.exit_code != 0:
                self.logger.warning(f"Command failed: {request.command}")
                # Continue with remaining commands but log the failure
        
        return results
    
    def format_result_for_llm(self, result: CommandResult) -> str:
        """Format command result for LLM consumption"""
        output = []
        
        output.append(f"Command: {result.command}")
        output.append(f"Status: {result.status.value}")
        output.append(f"Exit Code: {result.exit_code}")
        output.append(f"Execution Time: {result.execution_time:.2f}s")
        
        if result.safety_assessment:
            output.append(f"Safety Assessment: {result.safety_assessment}")
        
        if result.stdout:
            output.append(f"\nSTDOUT:\n{result.stdout}")
        
        if result.stderr:
            output.append(f"\nSTDERR:\n{result.stderr}")
        
        if result.error_message:
            output.append(f"\nError: {result.error_message}")
        
        return "\n".join(output)
    
    def suggest_safe_alternatives(self, unsafe_command: str) -> List[str]:
        """Suggest safe alternatives for unsafe commands"""
        suggestions = []
        
        if 'rm -rf' in unsafe_command:
            suggestions.extend([
                "Use 'ls' to list files first",
                "Use 'rm' without -rf for individual files",
                "Consider moving files to trash instead"
            ])
        
        if 'sudo' in unsafe_command:
            suggestions.extend([
                "Try the command without sudo first",
                "Use virtual environments instead of system-wide installation",
                "Check if you have necessary permissions"
            ])
        
        if 'chmod 777' in unsafe_command:
            suggestions.extend([
                "Use more restrictive permissions like 755 or 644",
                "Only grant necessary permissions",
                "Consider using specific user/group permissions"
            ])
        
        return suggestions
