"""
LLM Command Interface
Provides intelligent command execution interface for the main LLM
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

from task_manager.services.command_executor import (
    CommandExecutor, CommandRequest, CommandResult, CommandStatus
)
from task_manager.services.state_manager import StateManager


@dataclass
class LLMCommandRequest:
    """Command request from LLM with context"""
    command: str
    context: str
    task_id: str
    reasoning: Optional[str] = None
    expected_outcome: Optional[str] = None
    working_directory: Optional[str] = None
    timeout: int = 30


@dataclass
class LLMCommandResponse:
    """Response to LLM with command results and guidance"""
    success: bool
    command: str
    output: str
    guidance: str
    suggestions: List[str]
    next_steps: List[str]
    execution_time: float
    safety_notes: Optional[str] = None


class LLMCommandInterface:
    """Interface for LLM command execution with intelligent coaching"""
    
    def __init__(self, state_manager: StateManager):
        """Initialize LLM command interface"""
        self.logger = logging.getLogger(__name__)
        self.command_executor = CommandExecutor()
        self.state_manager = state_manager
        
        # Command patterns and coaching responses
        self.common_patterns = {
            'file_operations': ['ls', 'cat', 'find', 'grep', 'head', 'tail'],
            'development': ['git', 'npm', 'pip', 'python', 'node', 'make'],
            'system_info': ['ps', 'top', 'df', 'free', 'uname', 'which'],
            'testing': ['pytest', 'npm test', 'python -m unittest', 'coverage'],
            'building': ['make', 'cmake', 'gcc', 'javac', 'tsc', 'webpack']
        }
    
    async def execute_llm_command(self, llm_request: LLMCommandRequest) -> LLMCommandResponse:
        """
        Execute command from LLM with intelligent coaching
        
        Args:
            llm_request: Command request from LLM
            
        Returns:
            Response with results and coaching guidance
        """
        self.logger.info(f"LLM command request: {llm_request.command}")
        self.logger.info(f"Context: {llm_request.context}")
        
        # Create command request
        cmd_request = CommandRequest(
            command=llm_request.command,
            working_directory=llm_request.working_directory or ".",
            timeout=llm_request.timeout,
            task_id=llm_request.task_id,
            llm_context=llm_request.context
        )
        
        # Execute command
        result = await self.command_executor.execute_command(cmd_request)
        
        # Generate intelligent response
        response = await self._generate_llm_response(llm_request, result)
        
        # Update state with command execution
        await self._update_command_state(llm_request, result, response)
        
        return response
    
    async def _generate_llm_response(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> LLMCommandResponse:
        """Generate intelligent response for LLM"""
        
        success = result.status == CommandStatus.COMPLETED and result.exit_code == 0
        
        # Format output for LLM
        if success:
            output = self._format_successful_output(result)
            guidance = self._generate_success_guidance(llm_request, result)
            suggestions = self._generate_success_suggestions(llm_request, result)
            next_steps = self._generate_next_steps(llm_request, result)
        else:
            output = self._format_error_output(result)
            guidance = self._generate_error_guidance(llm_request, result)
            suggestions = self._generate_error_suggestions(llm_request, result)
            next_steps = self._generate_recovery_steps(llm_request, result)
        
        return LLMCommandResponse(
            success=success,
            command=result.command,
            output=output,
            guidance=guidance,
            suggestions=suggestions,
            next_steps=next_steps,
            execution_time=result.execution_time,
            safety_notes=result.safety_assessment
        )
    
    def _format_successful_output(self, result: CommandResult) -> str:
        """Format successful command output for LLM"""
        output_parts = []
        
        output_parts.append(f"✅ Command executed successfully")
        output_parts.append(f"Exit code: {result.exit_code}")
        output_parts.append(f"Execution time: {result.execution_time:.2f}s")
        
        if result.stdout:
            output_parts.append(f"\nOutput:\n{result.stdout}")
        
        if result.stderr and result.stderr.strip():
            output_parts.append(f"\nWarnings/Info:\n{result.stderr}")
        
        return "\n".join(output_parts)
    
    def _format_error_output(self, result: CommandResult) -> str:
        """Format error output for LLM"""
        output_parts = []
        
        if result.status == CommandStatus.BLOCKED:
            output_parts.append(f"🚫 Command blocked for safety reasons")
            output_parts.append(f"Safety assessment: {result.safety_assessment}")
        else:
            output_parts.append(f"❌ Command failed")
            output_parts.append(f"Exit code: {result.exit_code}")
            output_parts.append(f"Status: {result.status.value}")
        
        if result.stdout:
            output_parts.append(f"\nOutput:\n{result.stdout}")
        
        if result.stderr:
            output_parts.append(f"\nError details:\n{result.stderr}")
        
        if result.error_message:
            output_parts.append(f"\nError message: {result.error_message}")
        
        return "\n".join(output_parts)
    
    def _generate_success_guidance(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> str:
        """Generate guidance for successful command execution"""
        
        command_type = self._identify_command_type(result.command)
        
        if command_type == 'file_operations':
            return "Great! File operation completed successfully. You can now proceed with analyzing or processing the retrieved information."
        
        elif command_type == 'development':
            return "Development command executed successfully. Check the output for any important information about dependencies, builds, or configurations."
        
        elif command_type == 'testing':
            if 'passed' in result.stdout.lower() or 'ok' in result.stdout.lower():
                return "Excellent! Tests are passing. This indicates your code is working correctly. Continue with confidence."
            else:
                return "Command executed but check test results carefully. Look for any test failures or warnings that need attention."
        
        elif command_type == 'building':
            return "Build command completed successfully. Your code has been compiled/built without errors. Ready for the next step."
        
        else:
            return "Command executed successfully. Review the output and continue with your development workflow."
    
    def _generate_error_guidance(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> str:
        """Generate guidance for failed command execution"""
        
        if result.status == CommandStatus.BLOCKED:
            return "This command was blocked for safety reasons. I'll suggest safer alternatives to achieve your goal."
        
        elif result.status == CommandStatus.CANCELLED:
            return "Command timed out. Consider breaking it into smaller steps or increasing the timeout for long-running operations."
        
        elif 'permission denied' in result.stderr.lower():
            return "Permission denied error. Check file permissions or consider using appropriate access methods."
        
        elif 'command not found' in result.stderr.lower():
            return "Command not found. Verify the command is installed and available in your PATH."
        
        elif 'no such file or directory' in result.stderr.lower():
            return "File or directory not found. Check the path and ensure the target exists."
        
        else:
            return "Command failed. Review the error details and consider alternative approaches or fixing the underlying issue."
    
    def _generate_success_suggestions(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> List[str]:
        """Generate suggestions for successful commands"""
        
        suggestions = []
        command_type = self._identify_command_type(result.command)
        
        if command_type == 'file_operations':
            suggestions.extend([
                "Consider using additional filters or formatting for better output",
                "You can pipe results to other commands for further processing",
                "Save important output to files for later reference"
            ])
        
        elif command_type == 'development':
            suggestions.extend([
                "Check for any warnings in the output that should be addressed",
                "Consider running tests after development commands",
                "Document any important configuration changes"
            ])
        
        elif command_type == 'testing':
            suggestions.extend([
                "Run tests regularly during development",
                "Add more test cases for better coverage",
                "Consider running performance or integration tests"
            ])
        
        return suggestions
    
    def _generate_error_suggestions(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> List[str]:
        """Generate suggestions for failed commands"""
        
        if result.status == CommandStatus.BLOCKED:
            return self.command_executor.suggest_safe_alternatives(result.command)
        
        suggestions = []
        
        if 'permission denied' in result.stderr.lower():
            suggestions.extend([
                "Check file/directory permissions with 'ls -la'",
                "Ensure you have the necessary access rights",
                "Consider using relative paths or changing working directory"
            ])
        
        elif 'command not found' in result.stderr.lower():
            suggestions.extend([
                "Check if the command is installed: 'which <command>'",
                "Install missing dependencies if needed",
                "Verify the command name and spelling"
            ])
        
        elif 'no such file or directory' in result.stderr.lower():
            suggestions.extend([
                "List directory contents with 'ls' to verify paths",
                "Check if you're in the correct working directory",
                "Use absolute paths if relative paths are causing issues"
            ])
        
        return suggestions
    
    def _generate_next_steps(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> List[str]:
        """Generate next steps based on command results"""
        
        next_steps = []
        
        if result.status == CommandStatus.COMPLETED:
            next_steps.extend([
                "Analyze the command output for relevant information",
                "Continue with the next logical step in your workflow",
                "Consider running related commands if needed"
            ])
        
        return next_steps
    
    def _generate_recovery_steps(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult
    ) -> List[str]:
        """Generate recovery steps for failed commands"""
        
        recovery_steps = []
        
        if result.status == CommandStatus.BLOCKED:
            recovery_steps.extend([
                "Try a safer alternative command",
                "Break the operation into smaller, safer steps",
                "Use read-only commands to gather information first"
            ])
        
        elif result.status == CommandStatus.FAILED:
            recovery_steps.extend([
                "Check the error message for specific issues",
                "Verify prerequisites and dependencies",
                "Try a simpler version of the command first",
                "Use diagnostic commands to understand the problem"
            ])
        
        return recovery_steps
    
    def _identify_command_type(self, command: str) -> str:
        """Identify the type of command for appropriate guidance"""
        
        for cmd_type, patterns in self.common_patterns.items():
            for pattern in patterns:
                if pattern in command:
                    return cmd_type
        
        return 'general'
    
    async def _update_command_state(
        self, 
        llm_request: LLMCommandRequest, 
        result: CommandResult,
        response: LLMCommandResponse
    ) -> None:
        """Update state with command execution information"""
        
        command_info = {
            'command': result.command,
            'status': result.status.value,
            'exit_code': result.exit_code,
            'execution_time': result.execution_time,
            'success': response.success,
            'context': llm_request.context,
            'task_id': llm_request.task_id
        }
        
        # Add to command history
        state = await self.state_manager.get_current_state()
        if 'command_history' not in state:
            state['command_history'] = []
        
        state['command_history'].append(command_info)
        
        # Keep only last 50 commands to prevent state bloat
        if len(state['command_history']) > 50:
            state['command_history'] = state['command_history'][-50:]
        
        await self.state_manager.save_state(state)
    
    def format_response_for_llm(self, response: LLMCommandResponse) -> str:
        """Format response for LLM consumption"""
        
        output_parts = []
        
        # Command execution result
        output_parts.append(f"Command: {response.command}")
        output_parts.append(f"Success: {'✅ Yes' if response.success else '❌ No'}")
        output_parts.append(f"Execution time: {response.execution_time:.2f}s")
        
        # Output
        output_parts.append(f"\n{response.output}")
        
        # Guidance
        output_parts.append(f"\n🎯 Guidance: {response.guidance}")
        
        # Suggestions
        if response.suggestions:
            output_parts.append(f"\n💡 Suggestions:")
            for suggestion in response.suggestions:
                output_parts.append(f"  • {suggestion}")
        
        # Next steps
        if response.next_steps:
            output_parts.append(f"\n🚀 Next Steps:")
            for step in response.next_steps:
                output_parts.append(f"  • {step}")
        
        # Safety notes
        if response.safety_notes:
            output_parts.append(f"\n🔒 Safety: {response.safety_notes}")
        
        return "\n".join(output_parts)
