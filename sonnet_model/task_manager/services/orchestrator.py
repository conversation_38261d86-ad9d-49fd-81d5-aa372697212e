"""
Task Orchestrator - Manages task execution and coordination with persistent coaching
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..models.task import Task, TaskStatus, TaskPriority
from ..models.plan import Plan, PlanStep, StepStatus
from .state_manager import StateManager
from .priority_engine import PriorityEngine
from critique_engine.services.llm_critic import LLMCritic
from critique_engine.services.project_critic import ProjectCritic
from critique_engine.services.advanced_critic_engine import AdvancedCriticEngine, ProjectPhase, QualityGate
from critique_engine.services.requirement_traceability import RequirementTraceabilityMatrix, RequirementPriority, RequirementType
from critique_engine.services.advanced_testing_strategy import AdvancedTestingStrategy
from code_generator.services.code_generator import CodeGenerator
from task_manager.services.llm_command_interface import <PERSON><PERSON>om<PERSON>Interface, LLMCommandRequest, LLMCommandResponse
from task_manager.services.command_executor import CommandExecutor


class TaskOrchestrator:
    """
    Enhanced Task Orchestrator with World-Class Critique Agent Integration
    
    Implements expert-level critique framework:
    - Multi-dimensional assessment strategy
    - Sophisticated prompting for continuous LLM motivation
    - Quality threshold enforcement with phase gates
    - Requirement traceability matrix
    - Advanced testing strategy
    - Professional standards enforcement
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize core services
        self.state_manager = StateManager(config)
        self.code_generator = CodeGenerator(config)
        self.critique_engine = AdvancedCriticEngine(config)
        self.llm_critic = LLMCritic(config)
        self.project_critic = ProjectCritic(config)
        self.command_executor = CommandExecutor()
        self.llm_command_interface = LLMCommandInterface(self.command_executor)
        
        # Initialize advanced critique components
        self.advanced_critic = AdvancedCriticEngine(config)
        self.requirement_matrix = RequirementTraceabilityMatrix(
            config.get("project_path", "/home/<USER>/local_agent/sonnet_model")
        )
        self.testing_strategy = AdvancedTestingStrategy(config)
        
        # Track coaching momentum and quality gates
        self.coaching_momentum = 0
        self.quality_gates_enabled = config.get("enable_quality_gates", True)
        self.current_project_phase = ProjectPhase.REQUIREMENTS_ANALYSIS
        
        # Expert-level prompting configuration
        self.expert_prompting_enabled = True
        self.motivation_escalation_threshold = 3
        self.quality_enforcement_strict = True
    
    async def initialize(self) -> None:
        """Initialize the orchestrator and all components"""
        self.logger.info("Initializing Task Orchestrator")
        
        # Initialize all components
        await self.state_manager.initialize()
        await self.critique_engine.initialize()
        await self.llm_critic.initialize() if hasattr(self.llm_critic, 'initialize') else None
        await self.code_generator.initialize() if hasattr(self.code_generator, 'initialize') else None
        await self.llm_command_interface.initialize() if hasattr(self.llm_command_interface, 'initialize') else None
        
        # Try to resume from previous session
        await self._attempt_session_resumption()
        
        self.logger.info("Task Orchestrator initialized successfully")
    
    async def shutdown(self) -> None:
        """Shutdown the orchestrator gracefully"""
        self.logger.info("Shutting down Task Orchestrator")
        
        # Save current state before shutdown
        if self.current_session_id:
            await self.state_manager.save_session_state(self.current_session_id, {
                "momentum_level": self.momentum_level,
                "last_coaching_message": self.last_coaching_message,
                "shutdown_timestamp": datetime.now().isoformat()
            })
        
        # Shutdown components
        await self.state_manager.shutdown()
        await self.critique_engine.shutdown() if hasattr(self.critique_engine, 'shutdown') else None
        
        self.logger.info("Task Orchestrator shutdown complete")
    
    async def process_user_request(self, user_request: str, session_id: str = None) -> Dict[str, Any]:
        """
        Process user request with expert-level critique agent integration
        
        Enhanced with:
        - Sophisticated requirement analysis
        - Quality gate enforcement
        - Advanced prompting strategies
        - Comprehensive traceability
        """
        
        self.logger.info(f"Processing user request with advanced critique framework: {user_request[:100]}...")
        
        # Initialize or resume session with advanced state management
        session_info = await self._initialize_advanced_session(user_request, session_id)
        
        # Extract and analyze requirements with traceability
        requirements = await self._extract_and_trace_requirements(user_request, session_info)
        
        # Create comprehensive development plan with quality gates
        plan = await self._create_advanced_development_plan(requirements, session_info)
        
        # Execute plan with sophisticated coaching and quality enforcement
        execution_result = await self._execute_plan_with_expert_coaching(plan, session_info)
        
        # Validate quality gates before phase progression
        quality_validation = await self._validate_quality_gates(execution_result)
        
        # Generate expert-level feedback and next steps
        expert_feedback = await self._generate_expert_level_feedback(
            execution_result, quality_validation, session_info
        )
        
        # Update requirement traceability and project state
        await self._update_traceability_matrix(execution_result, requirements)
        
        return {
            "session_id": session_info["session_id"],
            "execution_result": execution_result,
            "quality_validation": quality_validation,
            "expert_feedback": expert_feedback,
            "requirements_status": await self._get_requirements_status(),
            "next_phase_readiness": await self._assess_phase_advancement_readiness(),
            "coaching_guidance": await self._generate_sophisticated_coaching_guidance(session_info)
        }
    
    async def _initialize_advanced_session(self, user_request: str, session_id: str = None) -> Dict[str, Any]:
        """Initialize session with advanced state management and requirement analysis"""
        
        session_info = await self.state_manager.get_or_create_session(session_id or "default")
        
        # Analyze request for requirement extraction
        request_analysis = await self._analyze_user_request_for_requirements(user_request)
        
        # Update session with requirement context
        session_info.update({
            "current_request": user_request,
            "request_analysis": request_analysis,
            "advanced_coaching_enabled": True,
            "quality_gates_active": self.quality_gates_enabled,
            "expert_prompting_level": "maximum"
        })
        
        await self.state_manager.update_session(session_info["session_id"], session_info)
        
        return session_info
    
    async def _extract_and_trace_requirements(self, user_request: str, session_info: Dict[str, Any]) -> Dict[str, Any]:
        """Extract requirements and establish traceability"""
        
        # Use LLM to extract structured requirements
        extracted_requirements = await self._llm_extract_requirements(user_request)
        
        # Add requirements to traceability matrix
        requirement_ids = []
        for req_data in extracted_requirements:
            req_id = f"REQ_{len(self.requirement_matrix.requirements) + 1:03d}"
            
            requirement = self.requirement_matrix.add_requirement(
                req_id=req_id,
                title=req_data.get("title", "Extracted Requirement"),
                description=req_data.get("description", user_request[:200]),
                acceptance_criteria=req_data.get("acceptance_criteria", []),
                priority=RequirementPriority(req_data.get("priority", "medium")),
                requirement_type=RequirementType(req_data.get("type", "functional"))
            )
            
            requirement_ids.append(req_id)
        
        return {
            "extracted_requirements": extracted_requirements,
            "requirement_ids": requirement_ids,
            "traceability_established": True
        }
    
    async def _create_advanced_development_plan(self, requirements: Dict[str, Any], session_info: Dict[str, Any]) -> Plan:
        """Create development plan with quality gates and phase management"""
        
        plan = Plan(
            id=f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            title="Advanced Development Plan with Quality Gates",
            description="Comprehensive development plan with expert-level quality enforcement"
        )
        
        # Add requirements analysis phase
        plan.add_step(PlanStep(
            id="requirements_analysis",
            title="Requirements Analysis and Traceability",
            description="Establish comprehensive requirement traceability matrix",
            status=StepStatus.PENDING,
            dependencies=[],
            estimated_duration=30
        ))
        
        # Add design phase with quality gate
        plan.add_step(PlanStep(
            id="design_with_quality_gate",
            title="Design Phase with Architecture Review",
            description="Create design with mandatory quality gate passage",
            status=StepStatus.PENDING,
            dependencies=["requirements_analysis"],
            estimated_duration=60
        ))
        
        # Add implementation phases for each requirement
        for req_id in requirements.get("requirement_ids", []):
            plan.add_step(PlanStep(
                id=f"implement_{req_id}",
                title=f"Implement Requirement {req_id}",
                description=f"Full implementation with testing for {req_id}",
                status=StepStatus.PENDING,
                dependencies=["design_with_quality_gate"],
                estimated_duration=120
            ))
        
        # Add comprehensive testing phase
        plan.add_step(PlanStep(
            id="comprehensive_testing",
            title="Comprehensive Testing Strategy Execution",
            description="Execute advanced testing strategy with full coverage validation",
            status=StepStatus.PENDING,
            dependencies=[f"implement_{req_id}" for req_id in requirements.get("requirement_ids", [])],
            estimated_duration=90
        ))
        
        # Add deployment readiness validation
        plan.add_step(PlanStep(
            id="deployment_validation",
            title="Deployment Readiness Validation",
            description="Final quality gate validation for deployment readiness",
            status=StepStatus.PENDING,
            dependencies=["comprehensive_testing"],
            estimated_duration=45
        ))
        
        await self.state_manager.save_plan(plan)
        return plan
    
    async def _execute_plan_with_expert_coaching(self, plan: Plan, session_info: Dict[str, Any]) -> Dict[str, Any]:
        """Execute plan with sophisticated coaching and motivation"""
        
        execution_results = []
        
        for step in plan.steps:
            self.logger.info(f"Executing step with expert coaching: {step.title}")
            
            # Pre-step coaching and motivation
            pre_step_coaching = await self._generate_pre_step_coaching(step, session_info)
            
            # Execute step with quality monitoring
            step_result = await self._execute_step_with_quality_monitoring(step, session_info)
            
            # Post-step validation and coaching
            post_step_coaching = await self._generate_post_step_coaching(step_result, session_info)
            
            # Check for LLM hesitation and provide sophisticated motivation
            if self._detect_llm_hesitation(step_result):
                motivation_prompt = await self.advanced_critic.provide_sophisticated_motivation(
                    step_result.get("llm_response", ""), 
                    {
                        "current_step": step.title,
                        "session_info": session_info,
                        "execution_context": step_result
                    }
                )
                step_result["sophisticated_motivation"] = motivation_prompt
            
            execution_results.append({
                "step": step,
                "result": step_result,
                "pre_coaching": pre_step_coaching,
                "post_coaching": post_step_coaching
            })
            
            # Update step status
            step.status = StepStatus.COMPLETED if step_result.get("success", False) else StepStatus.FAILED
            
            # Enforce quality gates if enabled
            if self.quality_gates_enabled and not await self._validate_step_quality_gate(step, step_result):
                return {
                    "status": "quality_gate_failed",
                    "failed_step": step.title,
                    "quality_issues": step_result.get("quality_issues", []),
                    "coaching_response": await self._generate_quality_gate_failure_coaching(step, step_result)
                }
        
        return {
            "status": "completed",
            "execution_results": execution_results,
            "plan_completion_percentage": 100.0,
            "quality_gates_passed": True
        }
    
    async def _validate_quality_gates(self, execution_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate quality gates using advanced critic engine"""
        
        if not self.quality_gates_enabled:
            return {"gates_enabled": False, "validation_skipped": True}
        
        # Use advanced critic engine for quality gate validation
        quality_validation = await self.advanced_critic.enforce_quality_gates({
            "execution_result": execution_result,
            "current_phase": self.current_project_phase,
            "requirements": self.requirement_matrix.requirements
        })
        
        return quality_validation
    
    async def _generate_expert_level_feedback(
        self, 
        execution_result: Dict[str, Any], 
        quality_validation: Dict[str, Any], 
        session_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate expert-level feedback using project critic"""
        
        # Generate overall project critique if project is complete
        if execution_result.get("status") == "completed":
            project_critique = await self.project_critic.generate_expert_project_critique(
                "/home/<USER>/local_agent/sonnet_model"
            )
            
            overall_feedback = await self.llm_critic.provide_overall_project_feedback(
                project_critique, session_info
            )
            
            return {
                "type": "expert_project_critique",
                "project_critique": project_critique,
                "overall_feedback": overall_feedback,
                "coaching_level": "expert_completion"
            }
        
        # Generate iterative feedback for ongoing work
        iterative_feedback = await self.llm_critic.provide_iterative_feedback_coaching(
            execution_result, quality_validation, session_info
        )
        
        return {
            "type": "iterative_expert_feedback",
            "feedback": iterative_feedback,
            "quality_status": quality_validation,
            "coaching_level": "expert_iterative"
        }
    
    async def _update_traceability_matrix(self, execution_result: Dict[str, Any], requirements: Dict[str, Any]) -> None:
        """Update requirement traceability matrix with execution results"""
        
        for req_id in requirements.get("requirement_ids", []):
            # Link implementation artifacts
            for step_result in execution_result.get("execution_results", []):
                if req_id in step_result.get("step", {}).get("id", ""):
                    # Extract implementation files from step result
                    impl_files = step_result.get("result", {}).get("generated_files", [])
                    for file_path in impl_files:
                        self.requirement_matrix.link_implementation_artifact(
                            req_id, file_path
                        )
            
            # Update requirement status based on execution
            if execution_result.get("status") == "completed":
                self.requirement_matrix.update_requirement_status(
                    req_id, 
                    RequirementStatus.COMPLETED,
                    "Completed through orchestrated execution"
                )
    
    async def _get_requirements_status(self) -> Dict[str, Any]:
        """Get comprehensive requirements status"""
        
        return self.requirement_matrix.generate_traceability_report()
    
    async def _assess_phase_advancement_readiness(self) -> Dict[str, Any]:
        """Assess readiness for advancing to next project phase"""
        
        current_phase = self.current_project_phase
        requirements_report = self.requirement_matrix.generate_traceability_report()
        
        advancement_criteria = {
            ProjectPhase.REQUIREMENTS_ANALYSIS: requirements_report["summary"]["total_requirements"] > 0,
            ProjectPhase.DESIGN_APPROVAL: requirements_report["summary"]["completion_percentage"] > 20,
            ProjectPhase.IMPLEMENTATION: requirements_report["summary"]["completion_percentage"] > 80,
            ProjectPhase.TESTING: requirements_report["summary"]["average_test_coverage"] > 90,
            ProjectPhase.DOCUMENTATION: True,  # Placeholder
            ProjectPhase.DEPLOYMENT: requirements_report["summary"]["completion_percentage"] == 100
        }
        
        ready_for_advancement = advancement_criteria.get(current_phase, False)
        
        return {
            "current_phase": current_phase.value,
            "ready_for_advancement": ready_for_advancement,
            "next_phase": self._get_next_phase().value if ready_for_advancement else None,
            "blocking_factors": self._identify_phase_advancement_blockers(requirements_report)
        }
    
    async def _generate_sophisticated_coaching_guidance(self, session_info: Dict[str, Any]) -> str:
        """Generate sophisticated coaching guidance for continued progress"""
        
        # Get incomplete requirements for targeted guidance
        incomplete_requirements = self.requirement_matrix.generate_llm_guidance_for_incomplete_requirements()
        
        if not incomplete_requirements:
            return (
                "🎉 **OUTSTANDING ACHIEVEMENT!** All requirements completed successfully. "
                "You have demonstrated exceptional development capabilities. "
                "The project meets all quality standards and is ready for deployment."
            )
        
        # Generate sophisticated coaching based on current state
        coaching = (
            "**EXPERT COACHING - MAINTAIN MOMENTUM AND ACHIEVE EXCELLENCE**\n\n"
            "Your implementation approach demonstrates strong technical capability. "
            "Continue with the same level of precision and attention to detail. "
            "The following requirements require immediate completion:\n\n"
        )
        
        for guidance in incomplete_requirements[:3]:  # Top 3 priority items
            coaching += f"{guidance}\n\n"
        
        coaching += (
            "**CRITICAL SUCCESS FACTORS:**\n"
            "• Maintain current implementation quality standards\n"
            "• Complete comprehensive testing for each requirement\n"
            "• Ensure full traceability documentation\n"
            "• Pass all quality gates before phase advancement\n\n"
            "**DO NOT STOP UNTIL ALL REQUIREMENTS ARE COMPLETED TO PRODUCTION STANDARDS.**"
        )
        
        return coaching
    
    async def _attempt_session_resumption(self) -> None:
        """Attempt to resume from a previous session"""
        latest_session = await self.state_manager.get_latest_session()
        if latest_session:
            self.logger.info(f"Found previous session: {latest_session['session_id']}")
            # Could implement automatic resumption logic here
        else:
            self.logger.info("No previous session found - starting fresh")
    
    async def detect_llm_stuck_and_coach(self, llm_message: str) -> Dict[str, Any]:
        """Detect if LLM is stuck and provide coaching"""
        hesitation_result = await self.llm_critic.detect_llm_hesitation(llm_message)
        
        if hesitation_result["is_hesitating"]:
            # Generate strong push-forward response
            push_response = await self.llm_critic.generate_push_forward_response(
                hesitation_result["hesitation_type"],
                llm_message
            )
            
            # Update momentum
            self.momentum_level = "medium"  # Hesitation detected, but we're coaching through it
            
            return {
                "stuck_detected": True,
                "hesitation_type": hesitation_result["hesitation_type"],
                "coaching_response": push_response,
                "should_continue": True,
                "confidence_boost": True
            }
        
        return {
            "stuck_detected": False,
            "coaching_response": await self.llm_critic.generate_standard_encouragement(),
            "should_continue": True
        }
    
    async def initialize_session(self, session_id: str = None) -> Dict[str, Any]:
        """
        Initialize orchestrator session with state persistence
        
        Args:
            session_id: Optional session ID to resume
            
        Returns:
            Session initialization result with resumption context
        """
        self.logger.info("Initializing orchestrator session...")
        
        # Initialize state manager session
        session_result = await self.state_manager.initialize_session(session_id)
        self.current_session_id = session_result["session_id"]
        self.is_session_resumed = session_result["resumed"]
        
        if self.is_session_resumed:
            # Get resumption context for coaching
            resumption_context = await self.state_manager.get_resumption_context()
            
            # Generate coaching message for resumption
            if self.coaching_enabled:
                coaching_message = await self.llm_critic.generate_resumption_coaching(resumption_context)
                self.last_coaching_message = coaching_message
                
                self.logger.info(f"Session resumed with coaching: {coaching_message['message'][:100]}...")
            
            return {
                **session_result,
                "resumption_context": resumption_context,
                "coaching_message": self.last_coaching_message
            }
        else:
            # New session - generate welcome coaching
            if self.coaching_enabled:
                welcome_message = await self.llm_critic.generate_welcome_coaching()
                self.last_coaching_message = welcome_message
            
            return {
                **session_result,
                "coaching_message": self.last_coaching_message
            }
    
    async def process_user_request(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process user request with persistent coaching and error handling
        
        Args:
            user_input: User's request or instruction
            context: Additional context for processing
            
        Returns:
            Processing result with coaching feedback
        """
        self.logger.info(f"Processing user request: {user_input[:100]}...")
        
        # Record user interaction
        await self.state_manager.record_coaching_event("user_request", {
            "input_length": len(user_input),
            "has_context": bool(context),
            "session_id": self.current_session_id
        })
        
        # Detect if user is asking for permission or showing hesitation
        hesitation_detected = await self._detect_hesitation(user_input)
        if hesitation_detected:
            await self.state_manager.record_coaching_event("llm_hesitation", {
                "hesitation_type": hesitation_detected,
                "user_input": user_input[:200]
            })
            
            # Generate coaching response to push forward
            coaching_response = await self.llm_critic.generate_encouragement_coaching(
                hesitation_type=hesitation_detected,
                context=context or {}
            )
            
            return {
                "type": "coaching_response",
                "coaching_message": coaching_response,
                "action": "continue_with_confidence",
                "momentum_boost": True
            }
        
        # Process the request based on type
        if "create" in user_input.lower() and ("task" in user_input.lower() or "plan" in user_input.lower()):
            return await self._handle_creation_request(user_input, context)
        elif "error" in user_input.lower() or "problem" in user_input.lower():
            return await self._handle_error_recovery(user_input, context)
        elif "status" in user_input.lower() or "progress" in user_input.lower():
            return await self._handle_status_request(user_input, context)
        else:
            return await self._handle_general_request(user_input, context)
    
    async def _detect_hesitation(self, user_input: str) -> Optional[str]:
        """Detect if the user input shows hesitation or asks for permission"""
        hesitation_patterns = {
            "permission_seeking": ["should i", "can i", "may i", "is it ok", "permission"],
            "uncertainty": ["not sure", "uncertain", "don't know", "confused"],
            "error_overwhelm": ["too many errors", "stuck", "can't proceed", "blocked"],
            "complexity_fear": ["too complex", "too difficult", "overwhelming"]
        }
        
        user_lower = user_input.lower()
        
        for hesitation_type, patterns in hesitation_patterns.items():
            if any(pattern in user_lower for pattern in patterns):
                return hesitation_type
        
        return None
    
    async def _handle_creation_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle task or plan creation requests"""
        if "plan" in user_input.lower():
            # Extract plan details and create
            plan_data = await self._extract_plan_data(user_input)
            plan = await self.state_manager.create_plan(plan_data)
            
            # Generate encouraging coaching
            coaching_message = await self.llm_critic.generate_creation_coaching("plan", plan.title)
            
            return {
                "type": "plan_created",
                "plan": plan.to_dict(),
                "coaching_message": coaching_message,
                "next_action": "start_first_step"
            }
        else:
            # Create task
            task_data = await self._extract_task_data(user_input)
            task = await self.state_manager.create_task(task_data)
            
            coaching_message = await self.llm_critic.generate_creation_coaching("task", task.title)
            
            return {
                "type": "task_created",
                "task": task.to_dict(),
                "coaching_message": coaching_message,
                "next_action": "begin_execution"
            }
    
    async def _handle_error_recovery(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle error recovery with coaching support"""
        # Record error for learning
        await self.state_manager.record_coaching_event("error_recovery", {
            "error_description": user_input,
            "context": context or {}
        })
        
        # Generate recovery coaching
        recovery_coaching = await self.llm_critic.generate_error_recovery_coaching(
            error_description=user_input,
            context=context or {}
        )
        
        # Suggest recovery actions
        recovery_actions = await self._suggest_recovery_actions(user_input, context)
        
        return {
            "type": "error_recovery",
            "coaching_message": recovery_coaching,
            "recovery_actions": recovery_actions,
            "auto_retry": self.auto_recovery,
            "momentum_preservation": True
        }
    
    async def _handle_status_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle status and progress requests"""
        state_summary = await self.state_manager.get_state_summary()
        resumption_context = await self.state_manager.get_resumption_context()
        
        # Generate progress coaching
        progress_coaching = await self.llm_critic.generate_progress_coaching(
            state_summary=state_summary,
            resumption_context=resumption_context
        )
        
        return {
            "type": "status_report",
            "state_summary": state_summary,
            "resumption_context": resumption_context,
            "coaching_message": progress_coaching,
            "momentum_level": self.momentum_level
        }
    
    async def _handle_general_request(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general requests with coaching support"""
        # Generate general coaching to maintain momentum
        general_coaching = await self.llm_critic.generate_general_coaching(
            user_input=user_input,
            context=context or {}
        )
        
        return {
            "type": "general_response",
            "coaching_message": general_coaching,
            "suggested_actions": await self._suggest_next_actions(),
            "keep_momentum": True
        }
    
    async def _extract_plan_data(self, user_input: str) -> Dict[str, Any]:
        """Extract plan data from user input"""
        # Simple extraction - in real implementation, use NLP
        return {
            "title": f"Plan from: {user_input[:50]}...",
            "description": user_input,
            "steps": [
                {
                    "title": "Analyze requirements",
                    "description": "Break down the user request into actionable steps"
                },
                {
                    "title": "Implement solution",
                    "description": "Execute the implementation based on analysis"
                },
                {
                    "title": "Verify and test",
                    "description": "Ensure the solution works correctly"
                }
            ]
        }
    
    async def _extract_task_data(self, user_input: str) -> Dict[str, Any]:
        """Extract task data from user input"""
        return {
            "title": f"Task: {user_input[:50]}...",
            "description": user_input,
            "priority": "medium"
        }
    
    async def _suggest_recovery_actions(self, error_description: str, context: Dict[str, Any]) -> List[str]:
        """Suggest recovery actions for errors"""
        return [
            "Retry the operation with modified parameters",
            "Break down the problem into smaller steps",
            "Check for missing dependencies or prerequisites",
            "Review and update the approach based on error details",
            "Continue with alternative solution path"
        ]
    
    async def _suggest_next_actions(self) -> List[str]:
        """Suggest next actions to maintain momentum"""
        resumption_context = await self.state_manager.get_resumption_context()
        
        if resumption_context["recommended_next_action"]:
            return [resumption_context["recommended_next_action"]]
        
        return [
            "Define a new task or plan",
            "Continue with existing work",
            "Review and optimize current progress",
            "Explore new implementation approaches"
        ]
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """Execute a task with coaching support"""
        # Update task status
        await self.state_manager.update_task_status(task_id, TaskStatus.IN_PROGRESS)
        
        # Generate execution coaching
        coaching_message = await self.llm_critic.generate_execution_coaching(task_id)
        
        # Simulate task execution (in real implementation, delegate to appropriate service)
        await asyncio.sleep(0.1)  # Simulate work
        
        # Complete task
        await self.state_manager.update_task_status(task_id, TaskStatus.COMPLETED, progress=100.0)
        
        # Generate completion coaching
        completion_coaching = await self.llm_critic.generate_completion_coaching(task_id)
        
        return {
            "task_id": task_id,
            "status": "completed",
            "coaching_messages": [coaching_message, completion_coaching],
            "momentum_boost": True
        }
    
    async def update_plan_progress(self, plan_id: str, step_id: str, status: StepStatus) -> Dict[str, Any]:
        """Update plan step progress with coaching"""
        success = await self.state_manager.update_plan_step(plan_id, step_id, status)
        
        if success:
            # Generate progress coaching
            coaching_message = await self.llm_critic.generate_step_progress_coaching(
                plan_id, step_id, status
            )
            
            return {
                "plan_id": plan_id,
                "step_id": step_id,
                "status": status.value,
                "coaching_message": coaching_message,
                "success": True
            }
        
        return {"success": False, "error": "Failed to update plan step"}
    
    async def get_coaching_summary(self) -> Dict[str, Any]:
        """Get a summary of coaching interactions"""
        return await self.state_manager._generate_coaching_summary()
    
    async def force_momentum_boost(self, reason: str = "manual_boost") -> Dict[str, Any]:
        """Force a momentum boost with encouraging coaching"""
        await self.state_manager.record_coaching_event("momentum_boost", {
            "reason": reason,
            "timestamp": datetime.now().isoformat()
        })
        
        boost_coaching = await self.llm_critic.generate_momentum_boost_coaching(reason)
        self.momentum_level = "high"
        
        return {
            "momentum_level": self.momentum_level,
            "coaching_message": boost_coaching,
            "boost_applied": True
        }
    
    async def shutdown_session(self) -> Dict[str, Any]:
        """Gracefully shutdown session with state persistence"""
        # Save final state
        await self.state_manager.save_state()
        
        # Generate farewell coaching
        farewell_coaching = await self.llm_critic.generate_farewell_coaching(
            session_summary=await self.state_manager.get_state_summary()
        )
        
        self.logger.info(f"Session {self.current_session_id} shutdown complete")
        
        return {
            "session_id": self.current_session_id,
            "farewell_message": farewell_coaching,
            "state_saved": True,
            "can_resume": True
        }
    
    async def execute_llm_command(
        self, 
        command: str, 
        context: str = "", 
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        Execute command directly from LLM with intelligent coaching
        
        Args:
            command: Command to execute
            context: Context about why this command is needed
            task_id: Associated task ID
            
        Returns:
            Execution result with coaching guidance
        """
        self.logger.info(f"LLM command execution request: {command}")
        
        # Create command request
        llm_request = LLMCommandRequest(
            command=command,
            context=context,
            task_id=task_id or "direct_llm_command",
            reasoning=f"Direct command execution requested by LLM: {context}",
            working_directory="."
        )
        
        # Execute command with intelligent interface
        response = await self.command_interface.execute_llm_command(llm_request)
        
        # Update momentum based on result
        if response.success:
            self.success_count += 1
            self.momentum_level = min(1.0, self.momentum_level + 0.1)
            coaching_tone = "encouraging"
        else:
            self.error_count += 1
            # Don't let momentum drop too much - keep LLM motivated
            self.momentum_level = max(0.3, self.momentum_level - 0.05)
            coaching_tone = "supportive"
        
        # Generate coaching response
        coaching_response = await self.llm_critic.provide_command_execution_coaching(
            command=command,
            result=response,
            momentum_level=self.momentum_level,
            tone=coaching_tone
        )
        
        # Format comprehensive response for LLM
        formatted_response = self._format_command_response_for_llm(
            response, coaching_response
        )
        
        return {
            "success": response.success,
            "command": command,
            "output": formatted_response,
            "raw_result": response,
            "coaching": coaching_response,
            "momentum_level": self.momentum_level,
            "execution_time": response.execution_time
        }
    
    def _format_command_response_for_llm(
        self, 
        response: LLMCommandResponse, 
        coaching: str
    ) -> str:
        """Format command response for LLM with coaching integration"""
        
        output_parts = []
        
        # Command execution header
        status_emoji = "✅" if response.success else "⚠️"
        output_parts.append(f"{status_emoji} **Command Execution Result**")
        output_parts.append(f"Command: `{response.command}`")
        output_parts.append(f"Success: {'Yes' if response.success else 'No'}")
        output_parts.append(f"Execution Time: {response.execution_time:.2f}s")
        
        # Command output
        output_parts.append(f"\n**Output:**")
        output_parts.append(response.output)
        
        # Coaching guidance
        output_parts.append(f"\n**🎯 Coaching Guidance:**")
        output_parts.append(coaching)
        
        # Technical guidance
        output_parts.append(f"\n**📋 Technical Guidance:**")
        output_parts.append(response.guidance)
        
        # Suggestions
        if response.suggestions:
            output_parts.append(f"\n**💡 Suggestions:**")
            for suggestion in response.suggestions:
                output_parts.append(f"  • {suggestion}")
        
        # Next steps
        if response.next_steps:
            output_parts.append(f"\n**🚀 Recommended Next Steps:**")
            for step in response.next_steps:
                output_parts.append(f"  • {step}")
        
        # Safety information
        if response.safety_notes:
            output_parts.append(f"\n**🔒 Safety Notes:**")
            output_parts.append(response.safety_notes)
        
        # Momentum encouragement
        output_parts.append(f"\n**⚡ Keep Going!** You're making excellent progress. Continue with confidence!")
        
        return "\n".join(output_parts)
