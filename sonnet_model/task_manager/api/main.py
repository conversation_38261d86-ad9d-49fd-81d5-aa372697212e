"""
Task Manager API Main Entry Point
Defines FastAPI application for the Task Manager
"""

import logging
import os
from typing import Dict, Any

import uvicorn
from fastapi import Fast<PERSON><PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .routes import router as task_manager_router
from ..services.orchestrator import TaskOrchestrator
from ..services.state_manager import StateManager


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Create FastAPI app
app = FastAPI(
    title="Agentic Code System - Task Manager API",
    description="API for managing tasks and plans in the Agentic Code System",
    version="1.0.0"
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global state
state_manager = StateManager()
orchestrator = TaskOrchestrator(state_manager=state_manager)


# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("Starting Task Manager API")
    
    # Initialize state manager
    await state_manager.initialize()
    
    # Initialize orchestrator
    await orchestrator.initialize()
    
    logger.info("Task Manager API started successfully")


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown"""
    logger.info("Shutting down Task Manager API")
    
    # Shutdown orchestrator
    await orchestrator.shutdown()
    
    # Shutdown state manager
    await state_manager.shutdown()
    
    logger.info("Task Manager API shut down successfully")


# Exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error": str(exc)
        }
    )


# Include routers
app.include_router(task_manager_router)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "name": "Agentic Code System - Task Manager API",
        "version": "1.0.0",
        "status": "running"
    }


# Health check endpoint
@app.get("/health")
async def health():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "services": {
            "orchestrator": "running",
            "state_manager": "running"
        }
    }


# Configuration endpoint
@app.get("/config")
async def get_config():
    """Get configuration"""
    # Return non-sensitive configuration
    return {
        "max_concurrent_tasks": orchestrator.max_concurrent_tasks,
        "task_timeout_seconds": orchestrator.task_timeout_seconds,
        "retry_limit": orchestrator.retry_limit
    }


# Run the application
if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=True)
