"""
Task Manager API Routes
Defines FastAPI routes for the Task Manager
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path
from pydantic import BaseModel

from ..models.task import Task, TaskStatus, TaskPriority
from ..models.plan import Plan, PlanStatus
from ..services.orchestrator import TaskOrchestrator
from ..services.state_manager import StateManager


# API Models
class TaskCreate(BaseModel):
    """Task creation request model"""
    title: str
    description: str
    priority: TaskPriority = TaskPriority.MEDIUM
    dependencies: List[str] = []
    metadata: Dict[str, Any] = {}


class TaskUpdate(BaseModel):
    """Task update request model"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatus] = None
    priority: Optional[TaskPriority] = None
    dependencies: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class PlanCreate(BaseModel):
    """Plan creation request model"""
    title: str
    description: str
    tasks: List[TaskCreate] = []
    metadata: Dict[str, Any] = {}


class PlanUpdate(BaseModel):
    """Plan update request model"""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[PlanStatus] = None
    metadata: Optional[Dict[str, Any]] = None


class ApiResponse(BaseModel):
    """Generic API response model"""
    success: bool
    message: str
    data: Optional[Any] = None


# Create router
router = APIRouter(prefix="/api/v1", tags=["Task Manager"])


# Dependency to get orchestrator
def get_orchestrator():
    """Get task orchestrator instance"""
    # Import here to avoid circular imports
    from .main import orchestrator
    return orchestrator


# Health check endpoint
@router.get("/health", response_model=ApiResponse)
async def health_check():
    """Health check endpoint"""
    return ApiResponse(
        success=True,
        message="Task Manager API is running"
    )


# Task endpoints
@router.post("/tasks", response_model=ApiResponse)
async def create_task(
    task: TaskCreate,
    background_tasks: BackgroundTasks,
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Create a new task"""
    try:
        new_task = await orchestrator.create_task(
            title=task.title,
            description=task.description,
            priority=task.priority,
            dependencies=task.dependencies,
            metadata=task.metadata
        )
        
        # Schedule task processing in background
        background_tasks.add_task(orchestrator.process_task, new_task.id)
        
        return ApiResponse(
            success=True,
            message=f"Task '{new_task.title}' created successfully",
            data=new_task
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/tasks", response_model=ApiResponse)
async def list_tasks(
    status: Optional[TaskStatus] = None,
    priority: Optional[TaskPriority] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """List tasks with optional filtering"""
    try:
        tasks = await orchestrator.list_tasks(
            status=status,
            priority=priority,
            limit=limit,
            offset=offset
        )
        
        return ApiResponse(
            success=True,
            message=f"Retrieved {len(tasks)} tasks",
            data=tasks
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/tasks/{task_id}", response_model=ApiResponse)
async def get_task(
    task_id: str = Path(..., title="Task ID"),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Get a specific task by ID"""
    try:
        task = await orchestrator.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        
        return ApiResponse(
            success=True,
            message=f"Retrieved task {task_id}",
            data=task
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/tasks/{task_id}", response_model=ApiResponse)
async def update_task(
    task_update: TaskUpdate,
    task_id: str = Path(..., title="Task ID"),
    background_tasks: BackgroundTasks = None,
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Update a task"""
    try:
        # Get existing task
        existing_task = await orchestrator.get_task(task_id)
        if not existing_task:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        
        # Update task
        updated_task = await orchestrator.update_task(
            task_id=task_id,
            title=task_update.title,
            description=task_update.description,
            status=task_update.status,
            priority=task_update.priority,
            dependencies=task_update.dependencies,
            metadata=task_update.metadata
        )
        
        # If status changed to READY, schedule task processing
        if (task_update.status == TaskStatus.READY and 
            existing_task.status != TaskStatus.READY and
            background_tasks):
            background_tasks.add_task(orchestrator.process_task, task_id)
        
        return ApiResponse(
            success=True,
            message=f"Task {task_id} updated successfully",
            data=updated_task
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/tasks/{task_id}", response_model=ApiResponse)
async def delete_task(
    task_id: str = Path(..., title="Task ID"),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Delete a task"""
    try:
        # Check if task exists
        existing_task = await orchestrator.get_task(task_id)
        if not existing_task:
            raise HTTPException(status_code=404, detail=f"Task {task_id} not found")
        
        # Delete task
        await orchestrator.delete_task(task_id)
        
        return ApiResponse(
            success=True,
            message=f"Task {task_id} deleted successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# Plan endpoints
@router.post("/plans", response_model=ApiResponse)
async def create_plan(
    plan: PlanCreate,
    background_tasks: BackgroundTasks,
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Create a new plan with optional tasks"""
    try:
        # Create plan
        new_plan = await orchestrator.create_plan(
            title=plan.title,
            description=plan.description,
            metadata=plan.metadata
        )
        
        # Create tasks if provided
        tasks = []
        for task_data in plan.tasks:
            task = await orchestrator.create_task(
                title=task_data.title,
                description=task_data.description,
                priority=task_data.priority,
                dependencies=task_data.dependencies,
                metadata=task_data.metadata,
                plan_id=new_plan.id
            )
            tasks.append(task)
        
        # Schedule plan processing in background
        if tasks:
            background_tasks.add_task(orchestrator.process_plan, new_plan.id)
        
        # Add tasks to response
        new_plan.tasks = tasks
        
        return ApiResponse(
            success=True,
            message=f"Plan '{new_plan.title}' created successfully with {len(tasks)} tasks",
            data=new_plan
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/plans", response_model=ApiResponse)
async def list_plans(
    status: Optional[PlanStatus] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """List plans with optional filtering"""
    try:
        plans = await orchestrator.list_plans(
            status=status,
            limit=limit,
            offset=offset
        )
        
        return ApiResponse(
            success=True,
            message=f"Retrieved {len(plans)} plans",
            data=plans
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/plans/{plan_id}", response_model=ApiResponse)
async def get_plan(
    plan_id: str = Path(..., title="Plan ID"),
    include_tasks: bool = Query(True, title="Include tasks"),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Get a specific plan by ID"""
    try:
        plan = await orchestrator.get_plan(plan_id)
        if not plan:
            raise HTTPException(status_code=404, detail=f"Plan {plan_id} not found")
        
        # Include tasks if requested
        if include_tasks:
            tasks = await orchestrator.list_tasks_by_plan(plan_id)
            plan.tasks = tasks
        
        return ApiResponse(
            success=True,
            message=f"Retrieved plan {plan_id}",
            data=plan
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/plans/{plan_id}", response_model=ApiResponse)
async def update_plan(
    plan_update: PlanUpdate,
    plan_id: str = Path(..., title="Plan ID"),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Update a plan"""
    try:
        # Check if plan exists
        existing_plan = await orchestrator.get_plan(plan_id)
        if not existing_plan:
            raise HTTPException(status_code=404, detail=f"Plan {plan_id} not found")
        
        # Update plan
        updated_plan = await orchestrator.update_plan(
            plan_id=plan_id,
            title=plan_update.title,
            description=plan_update.description,
            status=plan_update.status,
            metadata=plan_update.metadata
        )
        
        return ApiResponse(
            success=True,
            message=f"Plan {plan_id} updated successfully",
            data=updated_plan
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/plans/{plan_id}", response_model=ApiResponse)
async def delete_plan(
    plan_id: str = Path(..., title="Plan ID"),
    delete_tasks: bool = Query(False, title="Delete associated tasks"),
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Delete a plan and optionally its tasks"""
    try:
        # Check if plan exists
        existing_plan = await orchestrator.get_plan(plan_id)
        if not existing_plan:
            raise HTTPException(status_code=404, detail=f"Plan {plan_id} not found")
        
        # Delete plan and optionally tasks
        await orchestrator.delete_plan(plan_id, delete_tasks=delete_tasks)
        
        return ApiResponse(
            success=True,
            message=f"Plan {plan_id} deleted successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# System endpoints
@router.post("/system/process-all", response_model=ApiResponse)
async def process_all_tasks(
    background_tasks: BackgroundTasks,
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Process all ready tasks"""
    try:
        # Schedule task processing in background
        background_tasks.add_task(orchestrator.process_all_tasks)
        
        return ApiResponse(
            success=True,
            message="Task processing scheduled"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/system/status", response_model=ApiResponse)
async def system_status(
    orchestrator: TaskOrchestrator = Depends(get_orchestrator)
):
    """Get system status"""
    try:
        # Get counts
        task_count = len(await orchestrator.list_tasks())
        plan_count = len(await orchestrator.list_plans())
        
        # Get active tasks
        active_tasks = len(await orchestrator.list_tasks(status=TaskStatus.IN_PROGRESS))
        
        # Get system stats
        stats = {
            "total_tasks": task_count,
            "total_plans": plan_count,
            "active_tasks": active_tasks,
            "system_ready": True
        }
        
        return ApiResponse(
            success=True,
            message="System status retrieved",
            data=stats
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
