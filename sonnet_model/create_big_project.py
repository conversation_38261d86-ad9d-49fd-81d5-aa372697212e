"""
REAL BIG PROJECT CREATOR

Creates a complete Task Management Web Application with:
- Backend API (FastAPI)
- Database models (SQLAlchemy)
- Frontend (HTML/CSS/JS)
- Authentication system
- Real-time features
- Testing suite

The system will iterate UNTIL IT'S PERFECT - no task limits!
Each file will be improved until it gets 9/10 quality score.
"""

import asyncio
import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class BigProjectCreator:
    """Creates a real, complex project with iterative improvement"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Big project setup
        self.project_name = "task_manager_app"
        self.project_path = Path(self.project_name)
        
        # Quality threshold - must reach 9/10 to be considered complete
        self.quality_threshold = 9
        self.max_iterations_per_file = 10  # Safety limit
        
        # Complete project structure
        self.project_files = [
            {
                "path": "backend/models.py",
                "description": "SQLAlchemy database models for users, tasks, projects, and teams",
                "requirements": [
                    "User model with authentication fields",
                    "Task model with status, priority, due dates",
                    "Project model with team associations",
                    "Team model with member management",
                    "Proper relationships between models",
                    "Database constraints and validations",
                    "Timestamps and soft delete support"
                ]
            },
            {
                "path": "backend/database.py", 
                "description": "Database configuration and session management",
                "requirements": [
                    "SQLAlchemy engine configuration",
                    "Session factory and dependency injection",
                    "Database initialization and migration support",
                    "Connection pooling configuration",
                    "Environment-based database URLs"
                ]
            },
            {
                "path": "backend/auth.py",
                "description": "Authentication and authorization system",
                "requirements": [
                    "JWT token generation and validation",
                    "Password hashing with bcrypt",
                    "User registration and login endpoints",
                    "Role-based access control",
                    "Token refresh mechanism",
                    "Password reset functionality"
                ]
            },
            {
                "path": "backend/api/tasks.py",
                "description": "Task management API endpoints",
                "requirements": [
                    "CRUD operations for tasks",
                    "Task filtering and search",
                    "Task assignment to users",
                    "Status updates and transitions",
                    "Due date management",
                    "Task comments and attachments",
                    "Bulk operations support"
                ]
            },
            {
                "path": "backend/api/projects.py",
                "description": "Project management API endpoints", 
                "requirements": [
                    "Project CRUD operations",
                    "Team member management",
                    "Project statistics and reporting",
                    "Project templates",
                    "Project archiving",
                    "Permission management"
                ]
            },
            {
                "path": "backend/main.py",
                "description": "FastAPI application entry point",
                "requirements": [
                    "FastAPI app initialization",
                    "CORS configuration",
                    "API router registration",
                    "Middleware setup",
                    "Error handling",
                    "Health check endpoints",
                    "API documentation setup"
                ]
            },
            {
                "path": "frontend/index.html",
                "description": "Main HTML page with modern UI",
                "requirements": [
                    "Responsive design with CSS Grid/Flexbox",
                    "Task dashboard with drag-and-drop",
                    "Project overview section",
                    "User authentication forms",
                    "Real-time notifications area",
                    "Modern CSS styling",
                    "Mobile-friendly design"
                ]
            },
            {
                "path": "frontend/app.js",
                "description": "Frontend JavaScript application",
                "requirements": [
                    "API communication with fetch",
                    "JWT token management",
                    "Real-time updates with WebSocket",
                    "Drag-and-drop task management",
                    "Form validation and submission",
                    "Error handling and user feedback",
                    "State management",
                    "Modern ES6+ features"
                ]
            },
            {
                "path": "frontend/styles.css",
                "description": "Modern CSS styling",
                "requirements": [
                    "CSS Grid and Flexbox layouts",
                    "Responsive design breakpoints",
                    "Modern color scheme and typography",
                    "Smooth animations and transitions",
                    "Component-based styling",
                    "Dark/light theme support",
                    "Accessibility considerations"
                ]
            },
            {
                "path": "tests/test_models.py",
                "description": "Comprehensive model tests",
                "requirements": [
                    "Unit tests for all models",
                    "Relationship testing",
                    "Validation testing",
                    "Database constraint testing",
                    "Test fixtures and factories",
                    "Edge case coverage"
                ]
            },
            {
                "path": "tests/test_api.py",
                "description": "API endpoint tests",
                "requirements": [
                    "Integration tests for all endpoints",
                    "Authentication testing",
                    "Permission testing",
                    "Error response testing",
                    "Performance testing",
                    "Test database setup"
                ]
            },
            {
                "path": "requirements.txt",
                "description": "Python dependencies",
                "requirements": [
                    "FastAPI and Uvicorn",
                    "SQLAlchemy and Alembic",
                    "Authentication libraries",
                    "Testing frameworks",
                    "Development tools",
                    "Production dependencies"
                ]
            },
            {
                "path": "docker-compose.yml",
                "description": "Docker development environment",
                "requirements": [
                    "Multi-service setup",
                    "Database service",
                    "Backend service",
                    "Frontend service",
                    "Volume management",
                    "Environment configuration"
                ]
            },
            {
                "path": "README.md",
                "description": "Comprehensive project documentation",
                "requirements": [
                    "Project overview and features",
                    "Installation instructions",
                    "API documentation",
                    "Development setup",
                    "Deployment guide",
                    "Contributing guidelines"
                ]
            }
        ]
        
    async def create_big_project(self):
        """Create the complete big project with iterative improvement"""
        
        print("🚀 CREATING REAL BIG PROJECT")
        print("=" * 70)
        print(f"📁 Project: {self.project_name}")
        print(f"📄 Files to create: {len(self.project_files)}")
        print(f"🎯 Quality threshold: {self.quality_threshold}/10")
        print("🔄 Will iterate until PERFECT quality is achieved!")
        print("=" * 70)
        
        # Test Ollama connection
        if not await self._test_ollama_connection():
            print("❌ Cannot connect to Ollama")
            return
        
        print("✅ Connected to Ollama successfully!")
        
        # Setup project structure
        self._setup_project_structure()
        
        # Create each file with iterative improvement
        total_iterations = 0
        successful_files = 0
        
        for i, file_info in enumerate(self.project_files, 1):
            print(f"\n📋 CREATING FILE {i}/{len(self.project_files)}: {file_info['path']}")
            print(f"📝 Description: {file_info['description']}")
            print("-" * 70)
            
            iterations = await self._create_file_with_iterations(file_info)
            total_iterations += iterations
            
            if iterations > 0:
                successful_files += 1
                print(f"✅ FILE COMPLETED: {file_info['path']} ({iterations} iterations)")
            else:
                print(f"❌ FILE FAILED: {file_info['path']}")
        
        # Show final project summary
        self._show_project_summary(successful_files, total_iterations)
        
        print(f"\n🎉 BIG PROJECT CREATION COMPLETED!")
        print(f"📁 Check '{self.project_name}' folder for the complete application!")
        
    def _setup_project_structure(self):
        """Setup the complete project structure"""
        
        # Remove existing project
        if self.project_path.exists():
            shutil.rmtree(self.project_path)
        
        # Create project directories
        directories = [
            "backend",
            "backend/api", 
            "frontend",
            "tests",
            "docs",
            "scripts"
        ]
        
        for directory in directories:
            (self.project_path / directory).mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Created project structure with {len(directories)} directories")
        
    async def _create_file_with_iterations(self, file_info: Dict) -> int:
        """Create a file with iterative improvement until perfect"""
        
        file_path = self.project_path / file_info['path']
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        iteration = 1
        current_content = ""
        
        while iteration <= self.max_iterations_per_file:
            print(f"\n🔄 ITERATION {iteration} - {file_info['path']}")
            print("-" * 40)
            
            # Generate/improve content
            print("🤖 CODE GENERATOR: Working...")
            new_content = await self._generate_file_content(file_info, current_content, iteration)
            
            if not new_content:
                print("❌ Code generation failed")
                return 0
            
            # Write to file
            with open(file_path, 'w') as f:
                f.write(new_content)
            
            print(f"📄 File written: {len(new_content)} characters")
            
            # Critique the content
            print("🔍 CRITIQUE ENGINE: Analyzing...")
            critique = await self._critique_file_content(new_content, file_info)
            
            if not critique:
                print("❌ Critique failed")
                return 0
            
            quality_score = critique.get('quality_score', 0)
            issues = critique.get('issues', [])
            
            print(f"📊 Quality Score: {quality_score}/10")
            print(f"🔍 Issues Found: {len(issues)}")
            
            if issues:
                print("⚠️  Issues:")
                for issue in issues[:3]:  # Show first 3 issues
                    print(f"   - {issue}")
                if len(issues) > 3:
                    print(f"   ... and {len(issues) - 3} more")
            
            # Check if quality threshold is met
            if quality_score >= self.quality_threshold:
                print(f"✅ QUALITY THRESHOLD REACHED! ({quality_score}/10)")
                return iteration
            
            # Prepare for next iteration
            print(f"🔧 Quality not sufficient ({quality_score}/{self.quality_threshold})")
            print(f"🔄 Preparing iteration {iteration + 1}...")
            
            current_content = new_content
            iteration += 1
            
            await asyncio.sleep(1)  # Brief pause between iterations
        
        print(f"⚠️  Reached maximum iterations ({self.max_iterations_per_file})")
        return self.max_iterations_per_file
    
    async def _test_ollama_connection(self) -> bool:
        """Test Ollama connection"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                return response.status_code == 200
        except:
            return False
    
    async def _generate_file_content(self, file_info: Dict, existing_content: str, iteration: int) -> str:
        """Generate content for a file"""
        
        if iteration == 1:
            prompt = f"""You are an expert full-stack developer. Create a high-quality {file_info['path']} file.

Description: {file_info['description']}

Requirements:
{chr(10).join(f"- {req}" for req in file_info['requirements'])}

Create a complete, production-ready implementation with:
- Proper error handling
- Comprehensive documentation
- Type hints (for Python files)
- Best practices
- Security considerations
- Performance optimizations

File content:"""
        else:
            prompt = f"""You are an expert full-stack developer. Improve this {file_info['path']} file based on the critique feedback.

Description: {file_info['description']}

Requirements:
{chr(10).join(f"- {req}" for req in file_info['requirements'])}

Current file content:
```
{existing_content}
```

Please provide an improved version that addresses all issues and better meets the requirements. Focus on:
- Code quality and best practices
- Error handling and edge cases
- Documentation and comments
- Performance and security
- Maintainability and readability

Improved file content:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.1,  # Lower temperature for more consistent code
                            "top_p": 0.9,
                            "num_predict": 6144  # Larger for complex files
                        }
                    },
                    timeout=300.0  # Longer timeout for complex generation
                )
                
                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get("response", "")
                    return self._extract_content_from_response(generated_text, file_info['path'])
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            return ""
    
    async def _critique_file_content(self, content: str, file_info: Dict) -> Dict[str, Any]:
        """Critique file content with high standards"""
        
        prompt = f"""You are an expert code reviewer with very high standards. Analyze this {file_info['path']} file.

Description: {file_info['description']}

Requirements to check:
{chr(10).join(f"- {req}" for req in file_info['requirements'])}

File content:
```
{content}
```

Provide a thorough analysis in JSON format. Be strict - only give 9+ scores for truly excellent code:

{{
    "quality_score": <number 1-10, be strict>,
    "issues": ["specific issue 1", "specific issue 2"],
    "suggestions": ["specific suggestion 1", "specific suggestion 2"],
    "requirements_met": <number of requirements fully satisfied>,
    "code_quality_aspects": {{
        "error_handling": <1-10>,
        "documentation": <1-10>,
        "best_practices": <1-10>,
        "security": <1-10>,
        "performance": <1-10>
    }}
}}

Be very critical and thorough. Only high-quality, production-ready code should score 9+.

Analysis:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.05,  # Very low for consistent critique
                            "top_p": 0.8,
                            "num_predict": 2048
                        }
                    },
                    timeout=180.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    critique_text = result.get("response", "")
                    return self._parse_critique_response(critique_text)
                else:
                    return None
                    
        except Exception as e:
            self.logger.error(f"Critique failed: {e}")
            return None
    
    def _extract_content_from_response(self, response: str, file_path: str) -> str:
        """Extract file content from LLM response"""
        
        # Determine file type
        if file_path.endswith('.py'):
            if "```python" in response:
                start = response.find("```python") + 9
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        elif file_path.endswith('.html'):
            if "```html" in response:
                start = response.find("```html") + 7
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        elif file_path.endswith('.js'):
            if "```javascript" in response:
                start = response.find("```javascript") + 13
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        elif file_path.endswith('.css'):
            if "```css" in response:
                start = response.find("```css") + 6
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        
        # Generic code block extraction
        if "```" in response:
            start = response.find("```")
            if start != -1:
                # Skip the first ``` and language identifier
                start = response.find("\n", start) + 1
                end = response.find("```", start)
                if end != -1:
                    return response[start:end].strip()
        
        # Return the whole response if no code blocks found
        return response.strip()
    
    def _parse_critique_response(self, response: str) -> Dict[str, Any]:
        """Parse critique JSON response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # Fallback parsing
        return {
            "quality_score": 5,
            "issues": ["Could not parse critique response"],
            "suggestions": ["Manual review needed"],
            "requirements_met": 0
        }
    
    def _show_project_summary(self, successful_files: int, total_iterations: int):
        """Show final project summary"""
        
        print(f"\n📊 PROJECT CREATION SUMMARY:")
        print("=" * 50)
        print(f"📄 Total Files: {len(self.project_files)}")
        print(f"✅ Successfully Created: {successful_files}")
        print(f"🔄 Total Iterations: {total_iterations}")
        print(f"📈 Average Iterations per File: {total_iterations / len(self.project_files):.1f}")
        
        # Show project structure
        print(f"\n📁 PROJECT STRUCTURE:")
        for root, dirs, files in os.walk(self.project_path):
            level = root.replace(str(self.project_path), '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = Path(root) / file
                size = file_path.stat().st_size
                print(f"{subindent}{file} ({size} bytes)")


async def main():
    """Create the big project"""
    
    print("🎯 BIG PROJECT CREATOR")
    print("Creating a complete Task Management Web Application")
    print("Will iterate until each file reaches 9/10 quality!")
    print("=" * 70)
    
    creator = BigProjectCreator()
    await creator.create_big_project()


if __name__ == "__main__":
    asyncio.run(main())
