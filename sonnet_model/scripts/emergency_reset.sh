#!/bin/bash

# Emergency System Reset Script
# Complete system reset for recovery from critical issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Banner
echo -e "${RED}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    EMERGENCY RESET                          ║"
echo "║                  ⚠️  DANGER ZONE ⚠️                          ║"
echo "║              This will reset everything!                    ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Confirmation
echo -e "${YELLOW}This will completely reset the Sonnet Model system:${NC}"
echo "• Stop all services"
echo "• Clear all data and logs"
echo "• Free GPU memory"
echo "• Reset Redis data"
echo "• Remove temporary files"
echo ""
echo -e "${RED}⚠️  ALL CONVERSATION STATE AND PROGRESS WILL BE LOST! ⚠️${NC}"
echo ""
echo -e "${YELLOW}Are you sure you want to proceed? (type 'RESET' to confirm):${NC}"
read -r confirmation

if [ "$confirmation" != "RESET" ]; then
    log "Emergency reset cancelled"
    exit 0
fi

log "Starting emergency reset procedure..."

# Step 1: Force stop all services
log "Force stopping all services..."
export FORCE_KILL=true
./scripts/stop_system.sh 2>/dev/null || true

# Step 2: Kill any remaining processes
log "Killing any remaining processes..."
pkill -f "sonnet" 2>/dev/null || true
pkill -f "api.app" 2>/dev/null || true
pkill -f "ollama" 2>/dev/null || true

# Step 3: Free GPU memory aggressively
log "Freeing GPU memory..."
export FORCE=true
export RESET_GPU=true
./scripts/free_gpu_memory.sh 2>/dev/null || true

# Step 4: Clear Redis data
log "Clearing Redis data..."
if command -v redis-cli >/dev/null 2>&1; then
    redis-cli flushall 2>/dev/null || true
    success "Redis data cleared"
fi

# Step 5: Clear all data and logs
log "Clearing data and logs..."
rm -rf data/temp/* 2>/dev/null || true
rm -rf logs/* 2>/dev/null || true
rm -rf __pycache__ 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
rm -f .api.pid .ollama.pid 2>/dev/null || true

# Step 6: Clear Docker containers and volumes
log "Clearing Docker resources..."
if command -v docker-compose >/dev/null 2>&1; then
    docker-compose down -v 2>/dev/null || true
    docker system prune -f 2>/dev/null || true
fi

# Step 7: Reset configuration to defaults
log "Checking configuration..."
if [ ! -f "config/config.yaml" ]; then
    warning "Configuration file missing - this may cause issues"
fi

# Step 8: Verify cleanup
log "Verifying cleanup..."

# Check processes
remaining_processes=$(pgrep -f "sonnet|api.app|ollama" 2>/dev/null || true)
if [ -z "$remaining_processes" ]; then
    success "All processes stopped"
else
    warning "Some processes may still be running: $remaining_processes"
fi

# Check ports
api_port=${API_PORT:-8000}
if ! nc -z localhost $api_port 2>/dev/null; then
    success "Port $api_port is available"
else
    warning "Port $api_port may still be in use"
fi

# Check GPU
if command -v nvidia-smi >/dev/null 2>&1; then
    gpu_processes=$(nvidia-smi --query-compute-apps=pid --format=csv,noheader,nounits 2>/dev/null || true)
    if [ -z "$gpu_processes" ]; then
        success "No GPU processes running"
    else
        warning "Some GPU processes may still be running"
    fi
fi

# Step 9: Recreate essential directories
log "Recreating essential directories..."
mkdir -p data/temp logs 2>/dev/null || true

echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗"
echo "║                  EMERGENCY RESET COMPLETE                   ║"
echo "╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "\n${BLUE}📊 Reset Summary:${NC}"
echo "🛑 All services: Stopped"
echo "🧹 Data and logs: Cleared"
echo "🎮 GPU memory: Freed"
echo "🔄 Redis data: Cleared"
echo "🐳 Docker resources: Cleaned"

echo -e "\n${BLUE}🚀 Next Steps:${NC}"
echo "1. Check configuration: config/config.yaml"
echo "2. Start fresh system: ./scripts/start_system.sh"
echo "3. Verify operation: ./scripts/system_status.sh"

echo -e "\n${YELLOW}💡 If you continue to have issues:${NC}"
echo "• Check system requirements"
echo "• Verify GPU drivers (nvidia-smi)"
echo "• Check available disk space"
echo "• Review configuration file"

success "Emergency reset completed! System is ready for fresh start."
