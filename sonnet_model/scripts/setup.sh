#!/bin/bash

# Setup script for Agentic Code Development System

set -e

echo "🚀 Setting up Agentic Code Development System..."

# Check if Python 3.11+ is installed
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.11+ is required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed"

# Create virtual environment
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p data logs temp config/templates

# Copy environment file
if [ ! -f ".env" ]; then
    echo "⚙️  Creating environment file..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration"
fi

# Initialize database (if using SQLite)
echo "🗄️  Initializing database..."
python -c "
import asyncio
from task_manager.services.state_manager import StateManager

async def init_db():
    config = {'storage': {'type': 'sqlite', 'path': 'data/agentic_system.db'}}
    state_manager = StateManager(config)
    await state_manager.initialize()
    print('Database initialized successfully')

asyncio.run(init_db())
"

# Run tests
echo "🧪 Running tests..."
python -m pytest tests/ -v

echo "✅ Setup completed successfully!"
echo ""
echo "🎯 Next steps:"
echo "1. Edit .env file with your configuration"
echo "2. Start the system: python main.py"
echo "3. Access API at: http://localhost:8000"
echo "4. View docs at: http://localhost:8000/docs"
echo ""
echo "🐳 For Docker deployment:"
echo "docker-compose up -d"
