#!/usr/bin/env python3
"""
Simple Command Execution Capability Demonstration
Shows the core command execution functionality without complex imports
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from task_manager.services.command_executor import CommandExecutor, CommandRequest, CommandResult


async def demonstrate_core_command_execution():
    """Demonstrate core command execution capabilities"""
    
    print("🚀 **AGENTIC MODEL COMMAND EXECUTION DEMONSTRATION**")
    print("=" * 60)
    print("This demonstrates how our agentic model can intelligently handle")
    print("bash commands and any commands given by the main LLM with:")
    print("• Intelligent safety assessment")
    print("• Output capture and formatting")
    print("• Error handling and recovery")
    print("• Safety filtering for dangerous commands")
    print("=" * 60)
    
    # Initialize command executor
    print("\n🔧 **Initializing Command Executor...**")
    executor = CommandExecutor()
    
    # Test commands that demonstrate different scenarios
    test_commands = [
        {
            "command": "pwd",
            "description": "Safe read-only command - check working directory"
        },
        {
            "command": "ls -la",
            "description": "Safe file listing command"
        },
        {
            "command": "echo 'Hello from LLM command execution!'",
            "description": "Safe echo command"
        },
        {
            "command": "python --version",
            "description": "Safe version check command"
        },
        {
            "command": "find . -name '*.py' | head -3",
            "description": "Safe file search command"
        },
        {
            "command": "nonexistent_command_test",
            "description": "Command that will fail - demonstrates error handling"
        },
        {
            "command": "rm -rf /",
            "description": "Unsafe command - should be blocked by safety filter"
        },
        {
            "command": "sudo rm important_file",
            "description": "Another unsafe command - should be blocked"
        }
    ]
    
    print(f"\n🧪 **Testing {len(test_commands)} Different Command Scenarios...**")
    
    for i, test_case in enumerate(test_commands, 1):
        print(f"\n{'='*50}")
        print(f"**Test {i}/{len(test_commands)}: {test_case['description']}**")
        print(f"Command: `{test_case['command']}`")
        print(f"{'='*50}")
        
        # Create command request
        request = CommandRequest(
            command=test_case['command'],
            working_directory=".",
            timeout=10,
            task_id=f"demo_task_{i}"
        )
        
        # Execute command
        result = await executor.execute_command(request)
        
        # Display results
        print(f"\n**🎯 EXECUTION RESULT:**")
        print(f"Status: {result.status.value}")
        print(f"Success: {'✅ Yes' if result.exit_code == 0 and result.status.value == 'completed' else '❌ No'}")
        print(f"Exit Code: {result.exit_code}")
        print(f"Execution Time: {result.execution_time:.3f}s")
        
        if result.safety_assessment:
            print(f"\n**🔒 SAFETY ASSESSMENT:**")
            print(result.safety_assessment)
        
        if result.stdout:
            print(f"\n**📋 STDOUT:**")
            print(result.stdout[:200] + ("..." if len(result.stdout) > 200 else ""))
        
        if result.stderr:
            print(f"\n**⚠️ STDERR:**")
            print(result.stderr[:200] + ("..." if len(result.stderr) > 200 else ""))
        
        if result.error_message:
            print(f"\n**❌ ERROR MESSAGE:**")
            print(result.error_message)
        
        # Show safety suggestions for unsafe commands
        if result.status.value == "blocked":
            suggestions = executor.suggest_safe_alternatives(test_case['command'])
            if suggestions:
                print(f"\n**💡 SAFETY SUGGESTIONS:**")
                for suggestion in suggestions:
                    print(f"  • {suggestion}")
        
        # Format for LLM consumption
        llm_formatted = executor.format_result_for_llm(result)
        print(f"\n**🤖 LLM-FORMATTED OUTPUT:**")
        print(llm_formatted[:300] + ("..." if len(llm_formatted) > 300 else ""))
        
        # Add delay for readability
        await asyncio.sleep(0.3)
    
    print(f"\n{'='*60}")
    print("🎉 **COMMAND EXECUTION DEMONSTRATION COMPLETE!**")
    print("=" * 60)
    print("Key capabilities demonstrated:")
    print("✅ Safe command execution with intelligent filtering")
    print("✅ Comprehensive output capture and formatting")
    print("✅ Safety assessment for potentially dangerous commands")
    print("✅ Error handling with detailed reporting")
    print("✅ LLM-friendly output formatting")
    print("✅ Alternative suggestions for unsafe commands")
    print("=" * 60)
    
    return True


async def demonstrate_command_classification():
    """Demonstrate command safety classification"""
    
    print("\n🔍 **COMMAND SAFETY CLASSIFICATION DEMONSTRATION**")
    print("=" * 50)
    
    executor = CommandExecutor()
    
    test_commands = [
        "ls -la",           # Safe
        "cat file.txt",     # Safe  
        "git status",       # Potentially unsafe
        "pip install pkg",  # Potentially unsafe
        "rm -rf /",         # Unsafe
        "sudo shutdown",    # Unsafe
        "unknown_cmd"       # Unknown (treated as potentially unsafe)
    ]
    
    for cmd in test_commands:
        cmd_type = executor._classify_command(cmd)
        assessment = executor._assess_command_safety(cmd)
        
        print(f"Command: `{cmd}`")
        print(f"  Classification: {cmd_type.value}")
        print(f"  Assessment: {assessment}")
        print()
    
    print("✅ **Command classification working perfectly!**")


async def main():
    """Main demonstration function"""
    
    print("🤖 **INTELLIGENT COMMAND EXECUTION SYSTEM**")
    print("Demonstrating how our agentic model handles LLM command requests")
    print("with safety, intelligence, and comprehensive output capture.\n")
    
    # Run demonstrations
    success = await demonstrate_core_command_execution()
    await demonstrate_command_classification()
    
    if success:
        print("\n🎯 **SUMMARY: COMMAND EXECUTION CAPABILITY READY!**")
        print("The agentic model can now:")
        print("• Accept any bash/shell command from the main LLM")
        print("• Execute commands safely with intelligent filtering")
        print("• Capture and format output for LLM consumption")
        print("• Handle errors gracefully with detailed reporting")
        print("• Classify command safety levels automatically")
        print("• Provide alternative suggestions for unsafe commands")
        print("• Integrate seamlessly with the coaching system")
        
        print("\n🚀 **Ready for production use with the main LLM!**")
        print("The system provides smooth and intelligent command execution")
        print("that enables the main LLM to interact with the system effectively.")


if __name__ == "__main__":
    asyncio.run(main())
