#!/usr/bin/env python3
"""
Test script for the persistent coaching system integration
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from task_manager.services.orchestrator import TaskOrchestrator
from task_manager.services.state_manager import StateManager
from critique_engine.services.llm_critic import LLMCritic


async def test_coaching_integration():
    """Test the complete persistent coaching integration"""
    print("🚀 Testing Persistent Coaching System Integration")
    print("=" * 60)
    
    # Test configuration
    config = {
        "state": {
            "persistence_enabled": True,
            "state_file_path": "data/test_coaching_state.json",
            "backup_interval_minutes": 1
        },
        "critique": {
            "coaching_enabled": True,
            "hesitation_threshold": 0.7,
            "encouragement_frequency": 3
        },
        "code_generator": {
            "default_language": "python",
            "max_iterations": 3
        },
        "max_concurrent_tasks": 2,
        "coaching_enabled": True,
        "auto_recovery": True
    }
    
    # Initialize orchestrator
    print("📋 Initializing Task Orchestrator...")
    orchestrator = TaskOrchestrator(config)
    await orchestrator.initialize()
    
    # Test 1: Process a user request
    print("\n🎯 Test 1: Processing User Request with Coaching")
    user_request = "Create a simple Python web API with FastAPI that has user authentication"
    project_name = "test_coaching_project"
    
    result = await orchestrator.process_user_request(user_request, project_name)
    
    print(f"✅ Session ID: {result['session_id']}")
    print(f"✅ Plan created with {len(result['plan']['steps'])} steps")
    print(f"✅ Execution completed: {result['execution_result']['completed_steps']}/{result['execution_result']['total_steps']} steps")
    print(f"✅ Success rate: {result['execution_result']['success_rate']:.1%}")
    print(f"✅ Should continue: {result['should_continue']}")
    print(f"✅ Next steps suggested: {len(result['next_steps'])}")
    
    # Test 2: Test LLM hesitation detection
    print("\n🤔 Test 2: Testing LLM Hesitation Detection")
    hesitation_messages = [
        "Should I continue with this implementation?",
        "I'm not sure if this is the right approach...",
        "This seems complex, maybe we should stop here?",
        "Do you want me to proceed with the next step?"
    ]
    
    for message in hesitation_messages:
        coaching_result = await orchestrator.detect_llm_stuck_and_coach(message)
        print(f"📝 Message: '{message[:40]}...'")
        print(f"   🎯 Stuck detected: {coaching_result['stuck_detected']}")
        print(f"   💪 Should continue: {coaching_result['should_continue']}")
        if coaching_result['stuck_detected']:
            print(f"   🚀 Coaching: {coaching_result['coaching_response'][:60]}...")
    
    # Test 3: Test session resumption
    print("\n🔄 Test 3: Testing Session Resumption")
    
    # Get current session info
    current_session = await orchestrator.state_manager.get_latest_session()
    print(f"✅ Current session: {current_session['session_id']}")
    
    # Simulate shutdown and restart
    await orchestrator.shutdown()
    
    # Create new orchestrator and resume
    orchestrator2 = TaskOrchestrator(config)
    await orchestrator2.initialize()
    
    # Try to resume the same project
    result2 = await orchestrator2.process_user_request(
        "Continue working on the authentication system", 
        project_name
    )
    
    print(f"✅ Resumed session: {result2['session_id']}")
    print(f"✅ Session resumed: {orchestrator2.is_session_resumed}")
    
    # Test 4: Test state persistence
    print("\n💾 Test 4: Testing State Persistence")
    
    # Get project summary
    summary = await orchestrator2.state_manager.get_project_summary(project_name)
    print(f"✅ Project: {summary['project_name']}")
    print(f"✅ Total tasks: {summary['tasks']['total']}")
    print(f"✅ Completed tasks: {summary['tasks']['completed']}")
    print(f"✅ Momentum level: {summary['momentum_level']}")
    print(f"✅ Generated code files: {summary['generated_code_files']}")
    print(f"✅ Coaching interactions: {summary['coaching_interactions']}")
    
    # Test 5: Test coaching history
    print("\n📚 Test 5: Testing Coaching History")
    
    coaching_history = await orchestrator2.state_manager.get_coaching_history(5)
    print(f"✅ Coaching history entries: {len(coaching_history)}")
    for i, entry in enumerate(coaching_history[-3:], 1):
        print(f"   {i}. {entry['timestamp']}: {entry['data'].get('message', 'No message')[:50]}...")
    
    # Cleanup
    await orchestrator2.shutdown()
    
    print("\n🎉 All tests completed successfully!")
    print("✅ Persistent coaching system is fully functional")
    
    return True


async def test_llm_critic_standalone():
    """Test LLM Critic functionality standalone"""
    print("\n🧠 Testing LLM Critic Standalone")
    print("-" * 40)
    
    config = {
        "coaching_enabled": True,
        "hesitation_threshold": 0.7,
        "encouragement_frequency": 3
    }
    
    critic = LLMCritic(config)
    
    # Test hesitation detection
    test_messages = [
        "I think we should implement this feature",  # No hesitation
        "Should I continue with this approach?",     # Asking permission
        "I'm not sure about this implementation",    # Uncertainty
        "This error is blocking progress",           # Error
        "Let's build the next component"             # Confidence
    ]
    
    for message in test_messages:
        result = await critic.detect_llm_hesitation(message)
        print(f"📝 '{message}'")
        print(f"   🎯 Hesitating: {result['is_hesitating']}")
        print(f"   📊 Confidence: {result['confidence_score']:.2f}")
        print(f"   🏷️ Type: {result['hesitation_type']}")
        
        if result['is_hesitating']:
            push_response = await critic.generate_push_forward_response(
                result['hesitation_type'], message
            )
            print(f"   💪 Push response: {push_response[:60]}...")
        print()
    
    # Test standard encouragement
    encouragement = await critic.generate_standard_encouragement()
    print(f"🌟 Standard encouragement: {encouragement}")
    
    return True


async def main():
    """Run all tests"""
    print("🧪 Starting Persistent Coaching System Tests")
    print("=" * 80)
    
    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    
    # Run tests
    await test_llm_critic_standalone()
    await test_coaching_integration()
    
    print("\n🎊 ALL TESTS PASSED! 🎊")
    print("The persistent coaching system is ready for production!")


if __name__ == "__main__":
    asyncio.run(main())
