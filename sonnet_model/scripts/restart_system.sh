#!/bin/bash

# Sonnet Model System Restart Script
# Gracefully restarts the system with state preservation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    SONNET MODEL SYSTEM                      ║"
echo "║                    Restarting System...                     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if running from correct directory
if [ ! -f "config/config.yaml" ]; then
    echo -e "${RED}❌ Please run this script from the sonnet_model root directory${NC}"
    exit 1
fi

log "Starting graceful system restart..."

# Step 1: Save current state
log "Saving system state..."
export SAVE_STATE=true

# Step 2: Stop the system gracefully
log "Stopping system gracefully..."
./scripts/stop_system.sh

# Wait a moment for cleanup
sleep 3

# Step 3: Start the system
log "Starting system..."
./scripts/start_system.sh

# Step 4: Verify restart
log "Verifying system restart..."
sleep 5

if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    success "System restart completed successfully!"
    
    echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗"
    echo "║                  RESTART COMPLETED                          ║"
    echo "╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo -e "\n${BLUE}🎯 System is ready for use!${NC}"
    echo "• API: http://localhost:8000"
    echo "• Docs: http://localhost:8000/docs"
    echo "• Status: ./scripts/system_status.sh"
    
else
    echo -e "${RED}❌ System restart may have failed. Check status:${NC}"
    echo "• Run: ./scripts/system_status.sh"
    echo "• Check logs: tail -f logs/sonnet.log"
fi
