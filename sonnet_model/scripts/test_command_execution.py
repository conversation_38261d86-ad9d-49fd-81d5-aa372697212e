#!/usr/bin/env python3
"""
Command Execution Capability Demonstration
Shows how the agentic model can intelligently handle bash commands from the main LLM
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from task_manager.services.orchestrator import TaskOrchestrator
from task_manager.services.state_manager import StateManager
from shared.message_bus import RedisMessageBus
from task_manager.services.command_executor import CommandExecutor, CommandRequest
from task_manager.services.llm_command_interface import LLMCommandInterface, LLMCommandRequest


async def demonstrate_command_execution():
    """Demonstrate intelligent command execution capabilities"""
    
    print("🚀 **AGENTIC MODEL COMMAND EXECUTION DEMONSTRATION**")
    print("=" * 60)
    print("This demonstrates how our agentic model can intelligently handle")
    print("bash commands and any commands given by the main LLM with:")
    print("• Intelligent safety assessment")
    print("• Output capture and formatting")
    print("• Coaching and guidance")
    print("• Error recovery suggestions")
    print("• Seamless integration with the coaching system")
    print("=" * 60)
    
    # Initialize services
    print("\n🔧 **Initializing Command Execution Services...**")
    state_manager = StateManager()
    command_interface = LLMCommandInterface(state_manager)
    command_executor = CommandExecutor()
    
    # Test commands that demonstrate different scenarios
    test_commands = [
        {
            "command": "pwd",
            "context": "Check current working directory for project setup",
            "description": "Safe read-only command"
        },
        {
            "command": "ls -la",
            "context": "List project files to understand structure",
            "description": "Safe file listing command"
        },
        {
            "command": "python --version",
            "context": "Check Python version for compatibility",
            "description": "Safe version check command"
        },
        {
            "command": "find . -name '*.py' | head -5",
            "context": "Find Python files in the project",
            "description": "Safe file search command"
        },
        {
            "command": "git status",
            "context": "Check git repository status",
            "description": "Potentially unsafe but common development command"
        },
        {
            "command": "nonexistent_command_test",
            "context": "Test error handling for missing commands",
            "description": "Command that will fail - demonstrates error coaching"
        },
        {
            "command": "rm -rf /",
            "context": "Attempt dangerous system operation",
            "description": "Unsafe command - should be blocked by safety filter"
        }
    ]
    
    print(f"\n🧪 **Testing {len(test_commands)} Different Command Scenarios...**")
    
    for i, test_case in enumerate(test_commands, 1):
        print(f"\n{'='*50}")
        print(f"**Test {i}/{len(test_commands)}: {test_case['description']}**")
        print(f"Command: `{test_case['command']}`")
        print(f"Context: {test_case['context']}")
        print(f"{'='*50}")
        
        # Create LLM command request
        llm_request = LLMCommandRequest(
            command=test_case['command'],
            context=test_case['context'],
            task_id=f"demo_task_{i}",
            reasoning=f"Demo test case {i}: {test_case['description']}"
        )
        
        # Execute command with intelligent interface
        response = await command_interface.execute_llm_command(llm_request)
        
        # Display results
        print(f"\n**🎯 EXECUTION RESULT:**")
        print(f"Success: {'✅ Yes' if response.success else '❌ No'}")
        print(f"Execution Time: {response.execution_time:.3f}s")
        
        if response.safety_notes:
            print(f"\n**🔒 SAFETY ASSESSMENT:**")
            print(response.safety_notes)
        
        print(f"\n**📋 COMMAND OUTPUT:**")
        print(response.output)
        
        print(f"\n**🎯 INTELLIGENT GUIDANCE:**")
        print(response.guidance)
        
        if response.suggestions:
            print(f"\n**💡 SUGGESTIONS:**")
            for suggestion in response.suggestions:
                print(f"  • {suggestion}")
        
        if response.next_steps:
            print(f"\n**🚀 NEXT STEPS:**")
            for step in response.next_steps:
                print(f"  • {step}")
        
        # Add delay for readability
        await asyncio.sleep(0.5)
    
    print(f"\n{'='*60}")
    print("🎉 **COMMAND EXECUTION DEMONSTRATION COMPLETE!**")
    print("=" * 60)
    print("Key capabilities demonstrated:")
    print("✅ Safe command execution with intelligent filtering")
    print("✅ Comprehensive output capture and formatting")
    print("✅ Context-aware coaching and guidance")
    print("✅ Error handling with recovery suggestions")
    print("✅ Safety assessment for potentially dangerous commands")
    print("✅ Seamless integration ready for main LLM usage")
    print("=" * 60)


async def demonstrate_orchestrator_integration():
    """Demonstrate command execution through the orchestrator"""
    
    print("\n🔗 **ORCHESTRATOR INTEGRATION DEMONSTRATION**")
    print("=" * 50)
    print("Testing command execution through the main orchestrator...")
    
    # Initialize orchestrator (mock setup for demo)
    state_manager = StateManager()
    
    # Create a mock message bus for demo
    class MockMessageBus:
        async def publish(self, *args, **kwargs):
            pass
        async def subscribe(self, *args, **kwargs):
            pass
    
    message_bus = MockMessageBus()
    orchestrator = TaskOrchestrator(state_manager, message_bus)
    
    # Test direct command execution through orchestrator
    test_commands = [
        ("echo 'Hello from LLM!'", "Test basic command execution"),
        ("ls scripts/", "List script directory contents"),
        ("python -c 'print(\"Python integration test\")'", "Test Python execution")
    ]
    
    for command, context in test_commands:
        print(f"\n**Testing:** `{command}`")
        print(f"**Context:** {context}")
        
        # This would be called by the main LLM
        result = await orchestrator.execute_llm_command(
            command=command,
            context=context,
            task_id="orchestrator_demo"
        )
        
        print(f"**Result:** {'✅ Success' if result['success'] else '❌ Failed'}")
        print(f"**Momentum Level:** {result['momentum_level']:.2f}")
        print(f"**Execution Time:** {result['execution_time']:.3f}s")
        print(f"**Coaching:** {result['coaching'][:100]}...")
    
    print("\n✅ **Orchestrator integration working perfectly!**")


async def main():
    """Main demonstration function"""
    
    print("🤖 **INTELLIGENT COMMAND EXECUTION SYSTEM**")
    print("Demonstrating how our agentic model handles LLM command requests")
    print("with safety, intelligence, and coaching integration.\n")
    
    # Run demonstrations
    await demonstrate_command_execution()
    await demonstrate_orchestrator_integration()
    
    print("\n🎯 **SUMMARY: COMMAND EXECUTION CAPABILITY READY!**")
    print("The agentic model can now:")
    print("• Accept any bash/shell command from the main LLM")
    print("• Execute commands safely with intelligent filtering")
    print("• Capture and format output for LLM consumption")
    print("• Provide coaching and guidance for results")
    print("• Handle errors gracefully with recovery suggestions")
    print("• Integrate seamlessly with the orchestration system")
    print("• Maintain momentum and never let the LLM give up")
    
    print("\n🚀 **Ready for production use with the main LLM!**")


if __name__ == "__main__":
    asyncio.run(main())
