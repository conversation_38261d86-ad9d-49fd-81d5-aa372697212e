#!/usr/bin/env python3
"""
System Verification Script
Comprehensive verification of all Agentic Code Development System components
"""

import os
import sys
import asyncio
import importlib
import logging
from pathlib import Path
from typing import Dict, List, Any, Tuple

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SystemVerifier:
    """System verification utility"""
    
    def __init__(self):
        self.results = {
            "imports": {"passed": 0, "failed": 0, "errors": []},
            "components": {"passed": 0, "failed": 0, "errors": []},
            "auto_fixing": {"passed": 0, "failed": 0, "errors": []},
            "integration": {"passed": 0, "failed": 0, "errors": []}
        }
    
    async def verify_all(self) -> Dict[str, Any]:
        """Run comprehensive system verification"""
        logger.info("🔍 Starting comprehensive system verification...")
        
        # Verify imports
        await self.verify_imports()
        
        # Verify core components
        await self.verify_components()
        
        # Verify auto-fixing capabilities
        await self.verify_auto_fixing()
        
        # Verify integration
        await self.verify_integration()
        
        # Generate report
        return self.generate_report()
    
    async def verify_imports(self):
        """Verify all critical imports work"""
        logger.info("📦 Verifying imports...")
        
        critical_imports = [
            "task_manager.models.task",
            "task_manager.services.orchestrator",
            "code_generator.models.generation_request",
            "code_generator.services.code_generator",
            "critique_engine.models.critique_request",
            "critique_engine.services.critique_engine",
            "critique_engine.services.auto_fixer",
            "system_integration"
        ]
        
        for module_name in critical_imports:
            if await self._test_import(module_name):
                self.results["imports"]["passed"] += 1
            else:
                self.results["imports"]["failed"] += 1
    
    async def _test_import(self, module_name: str) -> bool:
        """Test importing a module"""
        try:
            importlib.import_module(module_name)
            logger.info(f"✅ Import successful: {module_name}")
            return True
        except Exception as e:
            error_msg = f"❌ Import failed: {module_name} - {str(e)}"
            logger.error(error_msg)
            self.results["imports"]["errors"].append(error_msg)
            return False
    
    async def verify_components(self):
        """Verify core component functionality"""
        logger.info("🔧 Verifying core components...")
        
        # Test Task Manager
        if await self._test_task_manager():
            self.results["components"]["passed"] += 1
        else:
            self.results["components"]["failed"] += 1
        
        # Test Code Generator
        if await self._test_code_generator():
            self.results["components"]["passed"] += 1
        else:
            self.results["components"]["failed"] += 1
        
        # Test Critique Engine
        if await self._test_critique_engine():
            self.results["components"]["passed"] += 1
        else:
            self.results["components"]["failed"] += 1
    
    async def _test_task_manager(self) -> bool:
        """Test Task Manager functionality"""
        try:
            from task_manager.models.task import Task, TaskStatus
            from task_manager.models.plan import Plan
            
            # Create test task
            task = Task(
                id="test_task_1",
                description="Test task",
                status=TaskStatus.PENDING,
                priority=1
            )
            
            # Create test plan
            plan = Plan(
                id="test_plan_1",
                description="Test plan",
                tasks=[task]
            )
            
            logger.info("✅ Task Manager models work correctly")
            return True
            
        except Exception as e:
            error_msg = f"❌ Task Manager test failed: {str(e)}"
            logger.error(error_msg)
            self.results["components"]["errors"].append(error_msg)
            return False
    
    async def _test_code_generator(self) -> bool:
        """Test Code Generator functionality"""
        try:
            from code_generator.models.generation_request import GenerationRequest, ProgrammingLanguage
            from code_generator.models.generation_result import GenerationResponse, CodeFile
            
            # Create test request
            request = GenerationRequest(
                task_id="test_task_1",
                description="Generate a simple function",
                language=ProgrammingLanguage.PYTHON,
                max_tokens=100
            )
            
            # Create test response
            code_file = CodeFile(
                filename="test.py",
                content="def hello():\n    return 'Hello World'",
                language=ProgrammingLanguage.PYTHON
            )
            
            response = GenerationResponse(
                request_id="test_req_1",
                task_id="test_task_1",
                files=[code_file]
            )
            
            logger.info("✅ Code Generator models work correctly")
            return True
            
        except Exception as e:
            error_msg = f"❌ Code Generator test failed: {str(e)}"
            logger.error(error_msg)
            self.results["components"]["errors"].append(error_msg)
            return False
    
    async def _test_critique_engine(self) -> bool:
        """Test Critique Engine functionality"""
        try:
            from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory
            from critique_engine.models.critique_result import CritiqueResult
            from critique_engine.models.code_issue import CodeIssue, IssueSeverity
            
            # Create test request
            request = CritiqueRequest(
                task_id="test_task_1",
                code_content="def hello():\n    print('Hello')",
                file_path="test.py",
                categories=[CritiqueCategory.QUALITY]
            )
            
            # Create test issue
            issue = CodeIssue(
                description="Test issue",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            )
            
            # Create test result
            result = CritiqueResult(
                task_id="test_task_1",
                request_id="test_req_1"
            )
            
            logger.info("✅ Critique Engine models work correctly")
            return True
            
        except Exception as e:
            error_msg = f"❌ Critique Engine test failed: {str(e)}"
            logger.error(error_msg)
            self.results["components"]["errors"].append(error_msg)
            return False
    
    async def verify_auto_fixing(self):
        """Verify auto-fixing functionality"""
        logger.info("🔧 Verifying auto-fixing capabilities...")
        
        try:
            from critique_engine.services.auto_fixer import AutoFixer
            from critique_engine.models.code_issue import CodeIssue, IssueSeverity
            
            # Initialize auto-fixer
            config = {"min_confidence_threshold": 0.7}
            auto_fixer = AutoFixer(config)
            
            # Test code with issues
            code = """def hello():
print("Hello World")
x=10+20"""
            
            # Create test issues
            issues = [
                CodeIssue(
                    description="Indentation error",
                    severity=IssueSeverity.ERROR,
                    line_number=2,
                    column_number=1,
                    file_path="test.py"
                ),
                CodeIssue(
                    description="Whitespace issue",
                    severity=IssueSeverity.WARNING,
                    line_number=3,
                    column_number=1,
                    file_path="test.py"
                )
            ]
            
            # Test auto-fixing
            result = await auto_fixer.fix_issues(code, issues, "python")
            
            if result["fix_count"] > 0:
                logger.info(f"✅ Auto-fixer applied {result['fix_count']} fixes")
                self.results["auto_fixing"]["passed"] += 1
            else:
                logger.warning("⚠️ Auto-fixer didn't apply any fixes")
                self.results["auto_fixing"]["failed"] += 1
            
            # Test fix suggestions
            suggestions = auto_fixer.get_fix_suggestions(issues)
            logger.info(f"✅ Auto-fixer generated {len(suggestions)} suggestions")
            
        except Exception as e:
            error_msg = f"❌ Auto-fixing test failed: {str(e)}"
            logger.error(error_msg)
            self.results["auto_fixing"]["errors"].append(error_msg)
            self.results["auto_fixing"]["failed"] += 1
    
    async def verify_integration(self):
        """Verify system integration"""
        logger.info("🔗 Verifying system integration...")
        
        try:
            # Test basic integration without external dependencies
            from system_integration import AgenticSystem
            
            # Mock configuration
            config = {
                "task_manager": {"storage": {"type": "memory"}},
                "code_generator": {"llm": {"model_endpoint": "mock://localhost"}},
                "critique_engine": {"static_analysis": {"enabled_tools": []}}
            }
            
            # This would normally require Redis/LLM services, so we just test instantiation
            logger.info("✅ System integration components can be imported")
            self.results["integration"]["passed"] += 1
            
        except Exception as e:
            error_msg = f"❌ Integration test failed: {str(e)}"
            logger.error(error_msg)
            self.results["integration"]["errors"].append(error_msg)
            self.results["integration"]["failed"] += 1
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate verification report"""
        total_passed = sum(category["passed"] for category in self.results.values())
        total_failed = sum(category["failed"] for category in self.results.values())
        total_tests = total_passed + total_failed
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed": total_passed,
                "failed": total_failed,
                "success_rate": f"{success_rate:.1f}%"
            },
            "details": self.results,
            "status": "PASS" if total_failed == 0 else "FAIL"
        }
        
        return report
    
    def print_report(self, report: Dict[str, Any]):
        """Print verification report"""
        print("\n" + "="*60)
        print("🎯 AGENTIC CODE DEVELOPMENT SYSTEM VERIFICATION REPORT")
        print("="*60)
        
        summary = report["summary"]
        print(f"📊 Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"📈 Success Rate: {summary['success_rate']}")
        print(f"🎭 Overall Status: {report['status']}")
        
        print("\n📋 DETAILED RESULTS:")
        for category, results in report["details"].items():
            print(f"\n{category.upper()}:")
            print(f"  ✅ Passed: {results['passed']}")
            print(f"  ❌ Failed: {results['failed']}")
            
            if results["errors"]:
                print("  🔍 Errors:")
                for error in results["errors"]:
                    print(f"    - {error}")
        
        print("\n" + "="*60)
        
        if report["status"] == "PASS":
            print("🎉 SYSTEM VERIFICATION COMPLETED SUCCESSFULLY!")
            print("✨ All core components are functional and ready for deployment.")
        else:
            print("⚠️ SYSTEM VERIFICATION FOUND ISSUES!")
            print("🔧 Please review and fix the errors above before deployment.")
        
        print("="*60)


async def main():
    """Main verification function"""
    verifier = SystemVerifier()
    report = await verifier.verify_all()
    verifier.print_report(report)
    
    # Exit with appropriate code
    sys.exit(0 if report["status"] == "PASS" else 1)


if __name__ == "__main__":
    asyncio.run(main())
