#!/bin/bash

# Sonnet Model System Status Script
# Comprehensive system health and status monitoring

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Status indicators
CHECK_MARK="✅"
CROSS_MARK="❌"
WARNING_MARK="⚠️"
INFO_MARK="ℹ️"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check service status
check_service() {
    local service_name=$1
    local check_command=$2
    local status_message=$3
    
    if eval "$check_command" >/dev/null 2>&1; then
        echo -e "${GREEN}${CHECK_MARK} $service_name${NC}: $status_message"
        return 0
    else
        echo -e "${RED}${CROSS_MARK} $service_name${NC}: Not running"
        return 1
    fi
}

# Function to get service info
get_service_info() {
    local service_name=$1
    local info_command=$2
    
    local info=$(eval "$info_command" 2>/dev/null || echo "N/A")
    echo -e "${CYAN}  └─ $service_name Info:${NC} $info"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    SONNET MODEL SYSTEM                      ║"
echo "║                      Status Report                          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}📊 System Status Check - $(date)${NC}\n"

# 1. Core System Components
echo -e "${YELLOW}🔧 Core System Components:${NC}"

# API Server
API_PORT=${API_PORT:-8000}
if check_service "API Server" "curl -s http://localhost:$API_PORT/health" "Running on port $API_PORT"; then
    get_service_info "Health" "curl -s http://localhost:$API_PORT/health | jq -r '.status' 2>/dev/null"
    get_service_info "Version" "curl -s http://localhost:$API_PORT/health | jq -r '.version' 2>/dev/null"
    get_service_info "Uptime" "curl -s http://localhost:$API_PORT/api/v1/status | jq -r '.uptime' 2>/dev/null"
fi

# Redis
if check_service "Redis" "redis-cli ping" "Connected"; then
    get_service_info "Memory Usage" "redis-cli info memory | grep used_memory_human | cut -d: -f2"
    get_service_info "Connected Clients" "redis-cli info clients | grep connected_clients | cut -d: -f2"
fi

# Ollama (if configured)
if check_service "Ollama" "curl -s http://localhost:11434/api/tags" "Running"; then
    get_service_info "Models" "curl -s http://localhost:11434/api/tags | jq -r '.models[].name' 2>/dev/null | tr '\n' ', ' | sed 's/,$//' || echo 'Unable to fetch'"
fi

echo ""

# 2. LLM Configuration
echo -e "${YELLOW}🤖 LLM Configuration:${NC}"

LLM_CONFIG=$(python -c "
import yaml
try:
    with open('config/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    llm_config = config.get('code_generator', {}).get('llm', {})
    print(f\"Type: {llm_config.get('type', 'Unknown')}\")
    print(f\"Model: {llm_config.get('model', 'Unknown')}\")
    print(f\"Temperature: {llm_config.get('temperature', 'Unknown')}\")
    print(f\"Max Tokens: {llm_config.get('max_tokens', 'Unknown')}\")
except Exception as e:
    print(f\"Error reading config: {e}\")
" 2>/dev/null)

echo -e "${CYAN}$LLM_CONFIG${NC}"

# Check environment variables for cloud providers
echo -e "${CYAN}  Cloud Provider Keys:${NC}"
if [ ! -z "${OPENAI_API_KEY}" ]; then
    echo -e "${GREEN}  ${CHECK_MARK} OpenAI API Key: Configured${NC}"
else
    echo -e "${YELLOW}  ${WARNING_MARK} OpenAI API Key: Not set${NC}"
fi

if [ ! -z "${ANTHROPIC_API_KEY}" ]; then
    echo -e "${GREEN}  ${CHECK_MARK} Anthropic API Key: Configured${NC}"
else
    echo -e "${YELLOW}  ${WARNING_MARK} Anthropic API Key: Not set${NC}"
fi

echo ""

# 3. GPU Status
echo -e "${YELLOW}🎮 GPU Status:${NC}"

if command_exists nvidia-smi; then
    if nvidia-smi >/dev/null 2>&1; then
        echo -e "${GREEN}${CHECK_MARK} NVIDIA GPU: Available${NC}"
        
        # GPU details
        gpu_info=$(nvidia-smi --query-gpu=name,memory.total,memory.used,utilization.gpu --format=csv,noheader,nounits)
        echo -e "${CYAN}  GPU Info:${NC}"
        echo "$gpu_info" | while IFS=, read -r name memory_total memory_used utilization; do
            echo -e "${CYAN}    └─ $name${NC}"
            echo -e "${CYAN}       Memory: ${memory_used}MB / ${memory_total}MB ($(( memory_used * 100 / memory_total ))% used)${NC}"
            echo -e "${CYAN}       Utilization: ${utilization}%${NC}"
        done
        
        # GPU processes
        gpu_processes=$(nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader,nounits 2>/dev/null)
        if [ ! -z "$gpu_processes" ]; then
            echo -e "${CYAN}  Active GPU Processes:${NC}"
            echo "$gpu_processes" | while IFS=, read -r pid process_name memory; do
                echo -e "${CYAN}    └─ PID $pid: $process_name (${memory}MB)${NC}"
            done
        else
            echo -e "${CYAN}  └─ No active GPU processes${NC}"
        fi
    else
        echo -e "${RED}${CROSS_MARK} NVIDIA GPU: Error accessing GPU${NC}"
    fi
else
    echo -e "${YELLOW}${WARNING_MARK} NVIDIA GPU: nvidia-smi not found${NC}"
fi

echo ""

# 4. System Resources
echo -e "${YELLOW}💻 System Resources:${NC}"

# Memory
memory_info=$(free -h | grep '^Mem:')
memory_used=$(echo $memory_info | awk '{print $3}')
memory_total=$(echo $memory_info | awk '{print $2}')
memory_percent=$(echo $memory_info | awk '{printf "%.1f", ($3/$2)*100}')

echo -e "${CYAN}  Memory: $memory_used / $memory_total (${memory_percent}% used)${NC}"

# CPU
if command_exists nproc; then
    cpu_cores=$(nproc)
    echo -e "${CYAN}  CPU Cores: $cpu_cores${NC}"
fi

# Load average
if [ -f /proc/loadavg ]; then
    load_avg=$(cat /proc/loadavg | awk '{print $1, $2, $3}')
    echo -e "${CYAN}  Load Average: $load_avg${NC}"
fi

# Disk usage
disk_info=$(df -h . | tail -1)
disk_used=$(echo $disk_info | awk '{print $3}')
disk_total=$(echo $disk_info | awk '{print $2}')
disk_percent=$(echo $disk_info | awk '{print $5}')

echo -e "${CYAN}  Disk Usage: $disk_used / $disk_total ($disk_percent used)${NC}"

echo ""

# 5. Network Status
echo -e "${YELLOW}🌐 Network Status:${NC}"

# Check if ports are listening
ports_to_check="8000 6379 11434"
for port in $ports_to_check; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        service_name=""
        case $port in
            8000) service_name="API Server" ;;
            6379) service_name="Redis" ;;
            11434) service_name="Ollama" ;;
        esac
        echo -e "${GREEN}  ${CHECK_MARK} Port $port ($service_name): Listening${NC}"
    else
        echo -e "${RED}  ${CROSS_MARK} Port $port: Not listening${NC}"
    fi
done

echo ""

# 6. Process Information
echo -e "${YELLOW}🔄 Process Information:${NC}"

# Find Sonnet-related processes
sonnet_processes=$(pgrep -f "sonnet|api.app|ollama" 2>/dev/null || true)
if [ ! -z "$sonnet_processes" ]; then
    echo -e "${GREEN}  ${CHECK_MARK} Active Processes:${NC}"
    for pid in $sonnet_processes; do
        if kill -0 $pid 2>/dev/null; then
            process_info=$(ps -p $pid -o pid,ppid,cmd --no-headers 2>/dev/null || echo "$pid - Process info unavailable")
            echo -e "${CYAN}    └─ $process_info${NC}"
        fi
    done
else
    echo -e "${YELLOW}  ${WARNING_MARK} No Sonnet-related processes found${NC}"
fi

echo ""

# 7. Configuration Status
echo -e "${YELLOW}⚙️  Configuration Status:${NC}"

# Check config file
if [ -f "config/config.yaml" ]; then
    echo -e "${GREEN}  ${CHECK_MARK} Configuration file: Found${NC}"
    
    # Validate YAML
    if python -c "import yaml; yaml.safe_load(open('config/config.yaml'))" 2>/dev/null; then
        echo -e "${GREEN}  ${CHECK_MARK} Configuration syntax: Valid${NC}"
    else
        echo -e "${RED}  ${CROSS_MARK} Configuration syntax: Invalid${NC}"
    fi
else
    echo -e "${RED}  ${CROSS_MARK} Configuration file: Missing${NC}"
fi

# Check environment variables
echo -e "${CYAN}  Environment Variables:${NC}"
env_vars="PYTHONPATH USE_REDIS REDIS_URL API_PORT"
for var in $env_vars; do
    if [ ! -z "${!var}" ]; then
        echo -e "${CYAN}    └─ $var: ${!var}${NC}"
    fi
done

echo ""

# 8. Recent Logs
echo -e "${YELLOW}📝 Recent Activity:${NC}"

if [ -f "logs/sonnet.log" ]; then
    echo -e "${CYAN}  Last 3 log entries:${NC}"
    tail -3 logs/sonnet.log | while read line; do
        echo -e "${CYAN}    └─ $line${NC}"
    done
else
    echo -e "${YELLOW}  ${WARNING_MARK} Log file not found${NC}"
fi

echo ""

# 9. Quick Actions
echo -e "${YELLOW}🚀 Quick Actions:${NC}"
echo -e "${CYAN}  • Start system: ./scripts/start_system.sh${NC}"
echo -e "${CYAN}  • Stop system: ./scripts/stop_system.sh${NC}"
echo -e "${CYAN}  • Restart system: ./scripts/restart_system.sh${NC}"
echo -e "${CYAN}  • Free GPU memory: ./scripts/free_gpu_memory.sh${NC}"
echo -e "${CYAN}  • View logs: tail -f logs/sonnet.log${NC}"
echo -e "${CYAN}  • API docs: http://localhost:$API_PORT/docs${NC}"

echo ""

# 10. Overall Status Summary
echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗"

# Determine overall status
api_running=$(curl -s http://localhost:$API_PORT/health >/dev/null 2>&1 && echo "true" || echo "false")
redis_running=$(redis-cli ping >/dev/null 2>&1 && echo "true" || echo "false")

if [ "$api_running" = "true" ]; then
    echo -e "║  ${GREEN}🎉 SYSTEM STATUS: OPERATIONAL${NC}                            ║"
    echo -e "║  ${GREEN}   All core services are running normally${NC}                ║"
elif [ "$api_running" = "false" ] && [ "$redis_running" = "true" ]; then
    echo -e "║  ${YELLOW}⚠️  SYSTEM STATUS: PARTIAL${NC}                               ║"
    echo -e "║  ${YELLOW}   Some services are running, API server is down${NC}         ║"
else
    echo -e "║  ${RED}❌ SYSTEM STATUS: DOWN${NC}                                   ║"
    echo -e "║  ${RED}   Core services are not running${NC}                         ║"
fi

echo "╚══════════════════════════════════════════════════════════════╝"

echo -e "\n${BLUE}Status check completed at $(date)${NC}"
