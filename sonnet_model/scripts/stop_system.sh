#!/bin/bash

# Sonnet Model System Shutdown Script
# This script gracefully stops all components and frees GPU memory

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    SONNET MODEL SYSTEM                      ║"
echo "║                     Shutting Down...                        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to kill process by PID file
kill_by_pidfile() {
    local pidfile=$1
    local service_name=$2
    
    if [ -f "$pidfile" ]; then
        local pid=$(cat "$pidfile")
        if kill -0 "$pid" 2>/dev/null; then
            log "Stopping $service_name (PID: $pid)..."
            kill -TERM "$pid" 2>/dev/null || true
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                warning "Force killing $service_name..."
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            success "$service_name stopped"
        fi
        rm -f "$pidfile"
    fi
}

# Function to stop Docker services
stop_docker_services() {
    if command_exists docker-compose && [ -f "docker-compose.yml" ]; then
        log "Stopping Docker services..."
        
        if docker-compose ps -q | grep -q .; then
            docker-compose down
            success "Docker services stopped"
        else
            log "No Docker services running"
        fi
    fi
}

# Function to stop API server
stop_api_server() {
    log "Stopping API server..."
    
    # Try to stop by PID file first
    kill_by_pidfile ".api.pid" "API Server"
    
    # Also try to kill by process name
    pkill -f "python -m api.app" 2>/dev/null || true
    pkill -f "uvicorn.*api.app" 2>/dev/null || true
    
    # Check if port is still in use
    local api_port=${API_PORT:-8000}
    local pid=$(lsof -ti:$api_port 2>/dev/null || true)
    if [ ! -z "$pid" ]; then
        warning "Force stopping process on port $api_port (PID: $pid)"
        kill -KILL "$pid" 2>/dev/null || true
    fi
    
    success "API server stopped"
}

# Function to stop Ollama
stop_ollama() {
    log "Stopping Ollama..."
    
    # Try to stop by PID file first
    kill_by_pidfile ".ollama.pid" "Ollama"
    
    # Also try to kill by process name
    pkill -f "ollama serve" 2>/dev/null || true
    pkill ollama 2>/dev/null || true
    
    success "Ollama stopped"
}

# Function to stop Redis (if we started it)
stop_redis() {
    if [ "${STOP_REDIS:-false}" = "true" ]; then
        log "Stopping Redis..."
        
        if command_exists redis-cli; then
            redis-cli shutdown 2>/dev/null || true
            success "Redis stopped"
        fi
    else
        log "Leaving Redis running (system service)"
    fi
}

# Function to free GPU memory
free_gpu_memory() {
    log "Freeing GPU memory..."
    
    if command_exists nvidia-smi; then
        # Get GPU processes
        local gpu_processes=$(nvidia-smi --query-compute-apps=pid --format=csv,noheader,nounits 2>/dev/null || true)
        
        if [ ! -z "$gpu_processes" ]; then
            log "Found GPU processes: $gpu_processes"
            
            # Kill GPU processes related to our system
            for pid in $gpu_processes; do
                local cmd=$(ps -p $pid -o comm= 2>/dev/null || true)
                if [[ "$cmd" =~ (python|ollama|llama) ]]; then
                    warning "Killing GPU process: $pid ($cmd)"
                    kill -TERM "$pid" 2>/dev/null || true
                    sleep 2
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
        fi
        
        # Clear PyTorch cache if available
        python -c "
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print('✅ GPU cache cleared')
else:
    print('ℹ️  CUDA not available')
" 2>/dev/null || log "PyTorch not available for GPU cleanup"
        
        # Show GPU status
        local gpu_memory=$(nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits 2>/dev/null | head -1)
        if [ ! -z "$gpu_memory" ]; then
            success "GPU memory usage: ${gpu_memory}MB"
        fi
    else
        log "nvidia-smi not available, skipping GPU cleanup"
    fi
}

# Function to cleanup temporary files
cleanup_temp_files() {
    log "Cleaning up temporary files..."
    
    # Remove PID files
    rm -f .api.pid .ollama.pid
    
    # Clean temporary data (optional)
    if [ "${CLEAN_TEMP:-false}" = "true" ]; then
        rm -rf data/temp/* 2>/dev/null || true
        rm -rf __pycache__ 2>/dev/null || true
        find . -name "*.pyc" -delete 2>/dev/null || true
        success "Temporary files cleaned"
    fi
}

# Function to save system state
save_system_state() {
    if [ "${SAVE_STATE:-true}" = "true" ]; then
        log "Saving system state..."
        
        # Save conversation state if Redis is available
        if command_exists redis-cli && redis-cli ping >/dev/null 2>&1; then
            redis-cli bgsave >/dev/null 2>&1 || true
        fi
        
        # Save any other persistent state
        python -c "
try:
    from shared.conversation_state import ConversationStateManager
    manager = ConversationStateManager()
    manager.save_all_states()
    print('✅ System state saved')
except Exception as e:
    print(f'ℹ️  State save skipped: {e}')
" 2>/dev/null || log "State save skipped"
    fi
}

# Main shutdown sequence
log "Starting graceful shutdown sequence..."

# 1. Save system state first
save_system_state

# 2. Stop Docker services
stop_docker_services

# 3. Stop API server
stop_api_server

# 4. Stop Ollama
stop_ollama

# 5. Stop Redis (if configured)
stop_redis

# 6. Free GPU memory
free_gpu_memory

# 7. Cleanup temporary files
cleanup_temp_files

# Final status check
log "Checking final system status..."

# Check if any services are still running
local remaining_processes=$(pgrep -f "sonnet|ollama|api.app" 2>/dev/null || true)
if [ ! -z "$remaining_processes" ]; then
    warning "Some processes may still be running: $remaining_processes"
    if [ "${FORCE_KILL:-false}" = "true" ]; then
        warning "Force killing remaining processes..."
        echo "$remaining_processes" | xargs kill -KILL 2>/dev/null || true
    fi
fi

# Check port availability
local api_port=${API_PORT:-8000}
if ! nc -z localhost $api_port 2>/dev/null; then
    success "Port $api_port is now available"
else
    warning "Port $api_port may still be in use"
fi

# Display final status
echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗"
echo "║                   SYSTEM STOPPED SUCCESSFULLY               ║"
echo "╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "\n${BLUE}📊 Shutdown Summary:${NC}"
echo "🛑 API Server: Stopped"
echo "🛑 Ollama: Stopped"
echo "🛑 GPU Memory: Freed"
echo "🧹 Cleanup: Complete"

if [ "${SAVE_STATE:-true}" = "true" ]; then
    echo "💾 State: Saved"
else
    echo "💾 State: Not saved"
fi

echo -e "\n${BLUE}🚀 To restart the system:${NC}"
echo "• Full restart: ./scripts/start_system.sh"
echo "• Quick restart: ./scripts/restart_system.sh"

echo -e "\n${BLUE}🔧 Troubleshooting:${NC}"
echo "• Check logs: tail -f logs/sonnet.log"
echo "• System status: ./scripts/system_status.sh"
echo "• Emergency reset: ./scripts/emergency_reset.sh"

success "Sonnet Model system shutdown complete!"

# Optional: Show system resource status
if [ "${SHOW_RESOURCES:-false}" = "true" ]; then
    echo -e "\n${BLUE}💻 System Resources:${NC}"
    echo "Memory: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
    echo "Disk: $(df -h . | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"
    
    if command_exists nvidia-smi; then
        echo "GPU: $(nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits | head -1 | awk '{print $1 "/" $2 "MB"}')"
    fi
fi
