#!/usr/bin/env python3
"""
Core System Verification Script
Lightweight verification without external dependencies
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CoreVerifier:
    """Core system verification utility"""
    
    def __init__(self):
        self.results = {"passed": 0, "failed": 0, "errors": []}
    
    async def verify_all(self) -> Dict[str, Any]:
        """Run core verification"""
        logger.info("🔍 Starting core system verification...")
        
        # Test core models without external dependencies
        await self.test_core_models()
        
        # Test auto-fixing functionality
        await self.test_auto_fixing()
        
        # Test utility functions
        await self.test_utilities()
        
        return self.generate_report()
    
    async def test_core_models(self):
        """Test core models functionality"""
        logger.info("📦 Testing core models...")
        
        # Test Code Generator models
        await self._test_code_generator_models()
        
        # Test Critique Engine models
        await self._test_critique_engine_models()
    
    async def _test_code_generator_models(self):
        """Test Code Generator models"""
        try:
            from code_generator.models.generation_request import GenerationRequest, ProgrammingLanguage
            from code_generator.models.generation_result import GenerationResponse, CodeFile
            
            # Test GenerationRequest
            request = GenerationRequest(
                task_id="test_task_1",
                description="Generate a simple function",
                language=ProgrammingLanguage.PYTHON,
                max_tokens=100
            )
            
            assert request.task_id == "test_task_1"
            assert request.language == ProgrammingLanguage.PYTHON
            
            # Test CodeFile
            code_file = CodeFile(
                filename="test.py",
                content="def hello():\n    return 'Hello World'",
                language=ProgrammingLanguage.PYTHON
            )
            
            assert code_file.filename == "test.py"
            assert code_file.get_extension() == ".py"
            
            # Test GenerationResponse
            response = GenerationResponse(
                request_id="test_req_1",
                task_id="test_task_1",
                files=[code_file]
            )
            
            assert response.task_id == "test_task_1"
            assert len(response.files) == 1
            assert response.get_file_by_name("test.py") is not None
            
            logger.info("✅ Code Generator models work correctly")
            self.results["passed"] += 1
            
        except Exception as e:
            error_msg = f"❌ Code Generator models test failed: {str(e)}"
            logger.error(error_msg)
            self.results["errors"].append(error_msg)
            self.results["failed"] += 1
    
    async def _test_critique_engine_models(self):
        """Test Critique Engine models"""
        try:
            from critique_engine.models.critique_request import CritiqueRequest, CritiqueCategory
            from critique_engine.models.critique_result import CritiqueResult
            from critique_engine.models.code_issue import CodeIssue, IssueSeverity
            
            # Test CodeIssue
            issue = CodeIssue(
                description="Test issue",
                severity=IssueSeverity.WARNING,
                line_number=1,
                column_number=1,
                file_path="test.py"
            )
            
            assert issue.description == "Test issue"
            assert issue.severity == IssueSeverity.WARNING
            
            # Test CritiqueRequest
            request = CritiqueRequest(
                task_id="test_task_1",
                code_content="def hello():\n    print('Hello')",
                file_path="test.py",
                categories=[CritiqueCategory.QUALITY]
            )
            
            assert request.task_id == "test_task_1"
            assert CritiqueCategory.QUALITY in request.categories
            
            # Test CritiqueResult
            result = CritiqueResult(
                task_id="test_task_1",
                request_id="test_req_1"
            )
            result.add_issue(issue)
            
            assert result.task_id == "test_task_1"
            assert len(result.issues) == 1
            assert result.has_errors() == False  # WARNING is not ERROR
            
            logger.info("✅ Critique Engine models work correctly")
            self.results["passed"] += 1
            
        except Exception as e:
            error_msg = f"❌ Critique Engine models test failed: {str(e)}"
            logger.error(error_msg)
            self.results["errors"].append(error_msg)
            self.results["failed"] += 1
    
    async def test_auto_fixing(self):
        """Test auto-fixing functionality"""
        logger.info("🔧 Testing auto-fixing capabilities...")
        
        try:
            from critique_engine.services.auto_fixer import AutoFixer, FixType
            from critique_engine.models.code_issue import CodeIssue, IssueSeverity
            
            # Initialize auto-fixer
            config = {"min_confidence_threshold": 0.7}
            auto_fixer = AutoFixer(config)
            
            # Test code with indentation issue
            code = """def hello():
print("Hello World")
return True"""
            
            # Create test issue
            issue = CodeIssue(
                description="Indentation error",
                severity=IssueSeverity.ERROR,
                line_number=2,
                column_number=1,
                file_path="test.py"
            )
            
            # Test auto-fixing
            result = await auto_fixer.fix_issues(code, [issue], "python")
            
            assert result["original_code"] == code
            assert result["fixed_code"] != code
            assert "    print" in result["fixed_code"]  # Should be indented
            
            logger.info(f"✅ Auto-fixer applied {result['fix_count']} fixes")
            self.results["passed"] += 1
            
        except Exception as e:
            error_msg = f"❌ Auto-fixing test failed: {str(e)}"
            logger.error(error_msg)
            self.results["errors"].append(error_msg)
            self.results["failed"] += 1
    
    async def test_utilities(self):
        """Test utility functions"""
        logger.info("🔧 Testing utility functions...")
        
        try:
            from code_generator.utils.code_utils import extract_code_blocks, get_file_extension
            from code_generator.utils.validation import validate_filename
            
            # Test code utils
            extension = get_file_extension("python")
            assert extension == ".py"
            
            # Test validation
            is_valid, error = validate_filename("test.py")
            assert is_valid == True
            
            is_valid, error = validate_filename("invalid/file?.py")
            assert is_valid == False
            
            logger.info("✅ Utility functions work correctly")
            self.results["passed"] += 1
            
        except Exception as e:
            error_msg = f"❌ Utility functions test failed: {str(e)}"
            logger.error(error_msg)
            self.results["errors"].append(error_msg)
            self.results["failed"] += 1
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate verification report"""
        total_tests = self.results["passed"] + self.results["failed"]
        success_rate = (self.results["passed"] / total_tests * 100) if total_tests > 0 else 0
        
        return {
            "total_tests": total_tests,
            "passed": self.results["passed"],
            "failed": self.results["failed"],
            "success_rate": f"{success_rate:.1f}%",
            "status": "PASS" if self.results["failed"] == 0 else "FAIL",
            "errors": self.results["errors"]
        }
    
    def print_report(self, report: Dict[str, Any]):
        """Print verification report"""
        print("\n" + "="*60)
        print("🎯 CORE SYSTEM VERIFICATION REPORT")
        print("="*60)
        
        print(f"📊 Total Tests: {report['total_tests']}")
        print(f"✅ Passed: {report['passed']}")
        print(f"❌ Failed: {report['failed']}")
        print(f"📈 Success Rate: {report['success_rate']}")
        print(f"🎭 Overall Status: {report['status']}")
        
        if report["errors"]:
            print("\n🔍 Errors:")
            for error in report["errors"]:
                print(f"  - {error}")
        
        print("\n" + "="*60)
        
        if report["status"] == "PASS":
            print("🎉 CORE SYSTEM VERIFICATION COMPLETED SUCCESSFULLY!")
            print("✨ All core components are functional.")
        else:
            print("⚠️ CORE SYSTEM VERIFICATION FOUND ISSUES!")
            print("🔧 Please review and fix the errors above.")
        
        print("="*60)


async def main():
    """Main verification function"""
    verifier = CoreVerifier()
    report = await verifier.verify_all()
    verifier.print_report(report)
    
    # Exit with appropriate code
    sys.exit(0 if report["status"] == "PASS" else 1)


if __name__ == "__main__":
    asyncio.run(main())
