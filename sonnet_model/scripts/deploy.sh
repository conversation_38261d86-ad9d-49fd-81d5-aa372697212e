#!/bin/bash

# Deployment script for Agentic Code Development System

set -e

echo "🚀 Deploying Agentic Code Development System..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is required but not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is required but not installed"
    exit 1
fi

echo "✅ Docker requirements check passed"

# Build the application
echo "🔨 Building application..."
docker-compose build

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check health
echo "🏥 Checking service health..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Application is healthy and ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Application failed to start properly"
        docker-compose logs agentic-system
        exit 1
    fi
    sleep 2
done

echo "📊 Service status:"
docker-compose ps

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "🌐 Application URLs:"
echo "  - API: http://localhost:8000"
echo "  - Docs: http://localhost:8000/docs"
echo "  - Health: http://localhost:8000/health"
echo ""
echo "📋 Useful commands:"
echo "  - View logs: docker-compose logs -f"
echo "  - Stop services: docker-compose down"
echo "  - Restart: docker-compose restart"
