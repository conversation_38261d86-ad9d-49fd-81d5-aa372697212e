#!/usr/bin/env python3
"""
Test Models Only - Isolated model testing without external dependencies
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_code_generator_models():
    """Test Code Generator models in isolation"""
    print("🔍 Testing Code Generator models...")
    
    try:
        # Test basic enums and models
        from code_generator.models.generation_request import ProgrammingLanguage, Framework
        
        # Test enum values
        assert ProgrammingLanguage.PYTHON == "python"
        assert Framework.FASTAPI == "fastapi"
        
        print("✅ Code Generator enums work correctly")
        
        # Test basic model without complex imports
        from pydantic import BaseModel, Field
        from typing import List, Optional, Dict, Any
        
        class SimpleGenerationRequest(BaseModel):
            task_id: str
            description: str
            language: ProgrammingLanguage = ProgrammingLanguage.PYTHON
            
        request = SimpleGenerationRequest(
            task_id="test_1",
            description="Test description"
        )
        
        assert request.task_id == "test_1"
        assert request.language == ProgrammingLanguage.PYTHON
        
        print("✅ Code Generator models work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Code Generator models failed: {e}")
        return False

def test_critique_engine_models():
    """Test Critique Engine models in isolation"""
    print("🔍 Testing Critique Engine models...")
    
    try:
        from critique_engine.models.code_issue import CodeIssue, IssueSeverity, IssueCategory
        
        # Test CodeIssue creation with all required fields
        issue = CodeIssue(
            id="test_issue_1",
            title="Test Issue",
            description="Test issue",
            severity=IssueSeverity.WARNING,
            line_start=1,
            column_start=1,
            file_path="test.py"
        )
        
        assert issue.id == "test_issue_1"
        assert issue.title == "Test Issue"
        assert issue.description == "Test issue"
        assert issue.severity == IssueSeverity.WARNING
        assert issue.line_start == 1
        
        print("✅ CodeIssue model works correctly")
        
        from critique_engine.models.critique_request import CritiqueCategory
        
        # Test enum
        assert CritiqueCategory.QUALITY == "quality"
        
        print("✅ Critique Engine models work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Critique Engine models failed: {e}")
        return False

def test_auto_fixer_basic():
    """Test auto-fixer basic functionality"""
    print("🔍 Testing Auto-fixer basic functionality...")
    
    try:
        # Test basic string operations that auto-fixer would use
        code = """def hello():
print("Hello World")
return True"""
        
        # Test basic indentation fix logic
        lines = code.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            if i == 1 and not line.startswith('    ') and line.strip():
                # Fix indentation
                fixed_lines.append('    ' + line)
            else:
                fixed_lines.append(line)
        
        fixed_code = '\n'.join(fixed_lines)
        
        assert "    print" in fixed_code
        print("✅ Basic auto-fixing logic works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Auto-fixer basic test failed: {e}")
        return False

def main():
    """Run all isolated tests"""
    print("🎯 ISOLATED MODEL TESTING")
    print("="*50)
    
    tests = [
        test_code_generator_models,
        test_critique_engine_models,
        test_auto_fixer_basic
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("="*50)
    print(f"📊 Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL ISOLATED TESTS PASSED!")
        return 0
    else:
        print("⚠️ SOME TESTS FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
