#!/usr/bin/env python3
"""
Advanced Project Critique Demonstration
Shows the comprehensive project analysis and expert-level feedback system
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from critique_engine.services.llm_critic import LLMCritic
from critique_engine.services.project_critic import generate_expert_project_critique


async def demonstrate_project_critique():
    """Demonstrate the advanced project critique functionality"""
    
    print("🎯" + "=" * 79)
    print("🎯 ADVANCED PROJECT CRITIQUE DEMONSTRATION")
    print("🎯" + "=" * 79)
    print()
    print("🔍 This system provides world-class developer-level feedback")
    print("📊 Analyzing entire project for architectural, quality, and deployment issues")
    print("💡 Generating 5 key feedback points with specific recommendations")
    print()
    
    # Initialize LLM Critic
    config = {
        "model_name": "critique-expert",
        "temperature": 0.1,
        "max_tokens": 4000,
        "coaching_enabled": True,
        "project_analysis": True
    }
    
    llm_critic = LLMCritic(config)
    project_path = str(project_root)
    
    print("🚀 SCENARIO 1: Project in Development State")
    print("-" * 50)
    
    # Test feedback for project in development
    dev_feedback = await llm_critic.provide_overall_project_feedback(
        project_path=project_path,
        project_state="development"
    )
    
    print(f"📋 Feedback Ready: {dev_feedback['feedback_ready']}")
    print(f"💬 Message: {dev_feedback['message']}")
    print()
    print("📝 Recommendations for Development State:")
    for rec in dev_feedback['recommendations']:
        print(f"   • {rec}")
    print()
    
    print("🎯 SCENARIO 2: Project Completed - Expert Analysis")
    print("-" * 50)
    
    # Test feedback for completed project
    completed_feedback = await llm_critic.provide_overall_project_feedback(
        project_path=project_path,
        project_state="completed"
    )
    
    print(f"📋 Feedback Ready: {completed_feedback['feedback_ready']}")
    print(f"📊 Analysis Timestamp: {completed_feedback['analysis_timestamp']}")
    print()
    
    print("🏗️ OVERALL PROJECT ASSESSMENT")
    print("-" * 40)
    print(completed_feedback['overall_assessment'])
    print()
    
    print("💪 PROJECT COACHING MESSAGE")
    print("-" * 40)
    print(completed_feedback['coaching_message'])
    print()
    
    print("🎯 KEY FEEDBACK POINTS (Top 5)")
    print("=" * 50)
    
    for point in completed_feedback['key_feedback_points']:
        print(f"\n📌 {point['feedback_number']}. {point['title']}")
        print(f"   🔥 Level: {point['level'].upper()}")
        print(f"   ⚡ Priority: {point['priority']}/5")
        print(f"   📝 {point['description']}")
        
        if point['specific_issues']:
            print(f"   🚨 Specific Issues:")
            for issue in point['specific_issues'][:3]:  # Show first 3
                print(f"      • {issue}")
            if len(point['specific_issues']) > 3:
                print(f"      • ... and {len(point['specific_issues']) - 3} more")
        
        if point['recommendations']:
            print(f"   💡 Key Recommendations:")
            for rec in point['recommendations'][:3]:  # Show first 3
                print(f"      • {rec}")
            if len(point['recommendations']) > 3:
                print(f"      • ... and {len(point['recommendations']) - 3} more")
        
        print(f"   🎯 Impact: {point['impact']}")
        print(f"   💪 Coach Says: {point['coaching_response']}")
        print()
    
    print("📋 PRIORITIZED NEXT STEPS")
    print("-" * 40)
    for step in completed_feedback['next_steps']:
        print(f"   {step}")
    print()
    
    print("🔄 SCENARIO 3: Iterative Feedback Coaching")
    print("-" * 50)
    
    # Demonstrate iterative coaching
    feedback_points = completed_feedback['key_feedback_points']
    for iteration in range(1, 4):
        coaching_msg = await llm_critic.provide_iterative_feedback_coaching(
            feedback_points=feedback_points,
            current_iteration=iteration
        )
        print(f"🔄 Iteration {iteration} Coaching:")
        print(coaching_msg)
        print()
    
    print("📊 EXPERT CRITIQUE PREVIEW")
    print("-" * 40)
    
    # Show preview of expert critique
    expert_critique_lines = completed_feedback['expert_critique'].split('\n')
    print("📝 First 20 lines of expert critique:")
    for line in expert_critique_lines[:20]:
        print(line)
    print("...")
    print(f"📄 Full critique contains {len(expert_critique_lines)} lines of detailed analysis")
    print()
    
    print("🎊 ADVANCED PROJECT CRITIQUE FEATURES")
    print("=" * 50)
    print("✅ Comprehensive project structure analysis")
    print("✅ Architectural issue detection (model proliferation, service duplication)")
    print("✅ Critical bug identification (syntax errors, broken imports)")
    print("✅ Code quality assessment (type hints, magic numbers, complexity)")
    print("✅ Testing coverage and failure analysis")
    print("✅ Deployment configuration validation")
    print("✅ Expert-level feedback with specific recommendations")
    print("✅ Prioritized action items with impact assessment")
    print("✅ Encouraging coaching messages for continuous improvement")
    print("✅ Iterative feedback support for systematic refinement")
    print()
    
    print("🚀 SYSTEM CAPABILITIES SUMMARY")
    print("-" * 40)
    print("🎯 Provides 5 high-level feedback points after project completion")
    print("🔍 Analyzes entire codebase for architectural and quality issues")
    print("💡 Generates specific, actionable recommendations")
    print("🏗️ Identifies critical bugs and structural problems")
    print("📊 Assesses testing coverage and deployment readiness")
    print("💪 Provides encouraging coaching to maintain momentum")
    print("🔄 Supports iterative improvement workflows")
    print("📝 Generates expert-level critique reports")
    print()
    
    return completed_feedback


async def demonstrate_expert_critique_generation():
    """Demonstrate standalone expert critique generation"""
    
    print("🎯 STANDALONE EXPERT CRITIQUE GENERATION")
    print("=" * 60)
    
    project_path = str(Path(__file__).parent.parent)
    
    print(f"🔍 Analyzing project: {project_path}")
    print("📊 Generating comprehensive expert critique...")
    print()
    
    # Generate expert critique
    expert_critique = await generate_expert_project_critique(project_path)
    
    # Show the critique
    print(expert_critique)
    
    return expert_critique


async def main():
    """Main demonstration function"""
    
    print("🎯 STARTING ADVANCED PROJECT CRITIQUE DEMONSTRATION")
    print("=" * 80)
    print()
    
    # Demonstrate integrated project critique
    project_feedback = await demonstrate_project_critique()
    
    print("\n" + "=" * 80)
    
    # Demonstrate standalone expert critique
    expert_critique = await demonstrate_expert_critique_generation()
    
    print("\n" + "=" * 80)
    print("🎊 ADVANCED PROJECT CRITIQUE DEMONSTRATION COMPLETE!")
    print("=" * 80)
    print()
    print("✅ Project critique system fully operational")
    print("✅ Expert-level feedback generation working")
    print("✅ Iterative coaching support implemented")
    print("✅ Comprehensive analysis capabilities verified")
    print()
    print("🚀 Ready to provide world-class developer feedback!")
    print("💪 System will help LLMs continuously improve their code!")
    
    return {
        "project_feedback": project_feedback,
        "expert_critique": expert_critique
    }


if __name__ == "__main__":
    asyncio.run(main())
