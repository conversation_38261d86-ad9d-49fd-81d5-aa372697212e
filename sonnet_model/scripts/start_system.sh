#!/bin/bash

# Sonnet Model System Startup Script
# This script starts all components of the Sonnet Model system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    SONNET MODEL SYSTEM                      ║"
echo "║                      Starting Up...                         ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if running from correct directory
if [ ! -f "config/config.yaml" ]; then
    error "Please run this script from the sonnet_model root directory"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost $1 2>/dev/null
}

# Function to wait for service
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    log "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    error "$service_name failed to start within timeout"
    return 1
}

# Prerequisites check
log "Checking prerequisites..."

# Check Python
if ! command_exists python; then
    error "Python is not installed"
    exit 1
fi

python_version=$(python --version 2>&1 | cut -d' ' -f2)
log "Python version: $python_version"

# Check pip
if ! command_exists pip; then
    error "pip is not installed"
    exit 1
fi

# Check if virtual environment is recommended
if [[ "$VIRTUAL_ENV" == "" ]]; then
    warning "Not running in a virtual environment. Consider using venv or conda."
fi

# Check GPU availability (optional)
if command_exists nvidia-smi; then
    log "Checking GPU availability..."
    if nvidia-smi >/dev/null 2>&1; then
        gpu_info=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits | head -1)
        success "GPU detected: $gpu_info"
        export GPU_AVAILABLE=true
    else
        warning "GPU not available or nvidia-smi failed"
        export GPU_AVAILABLE=false
    fi
else
    warning "nvidia-smi not found. GPU support disabled."
    export GPU_AVAILABLE=false
fi

# Install/update dependencies
log "Installing/updating dependencies..."
pip install -r requirements.txt --quiet

# Check and start Redis (if configured)
if [ "${USE_REDIS:-true}" = "true" ]; then
    log "Checking Redis..."
    
    if command_exists redis-server && command_exists redis-cli; then
        if ! redis-cli ping >/dev/null 2>&1; then
            log "Starting Redis server..."
            if command_exists systemctl; then
                sudo systemctl start redis-server 2>/dev/null || redis-server --daemonize yes
            else
                redis-server --daemonize yes
            fi
            sleep 2
        fi
        
        if redis-cli ping >/dev/null 2>&1; then
            success "Redis is running"
        else
            warning "Redis failed to start. Using in-memory state management."
            export USE_REDIS=false
        fi
    else
        warning "Redis not installed. Using in-memory state management."
        export USE_REDIS=false
    fi
fi

# Check and start Ollama (if using local LLM)
LLM_TYPE=$(python -c "
import yaml
with open('config/config.yaml', 'r') as f:
    config = yaml.safe_load(f)
print(config.get('code_generator', {}).get('llm', {}).get('type', 'http_api'))
" 2>/dev/null || echo "http_api")

if [ "$LLM_TYPE" = "http_api" ]; then
    log "Checking Ollama service..."
    
    if command_exists ollama; then
        if ! curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
            log "Starting Ollama server..."
            ollama serve &
            OLLAMA_PID=$!
            sleep 5
        fi
        
        if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
            success "Ollama is running"
            
            # Check if required model is available
            MODEL_NAME=$(python -c "
import yaml
with open('config/config.yaml', 'r') as f:
    config = yaml.safe_load(f)
print(config.get('code_generator', {}).get('llm', {}).get('model', 'deepseek-coder-v2:16b'))
" 2>/dev/null || echo "deepseek-coder-v2:16b")
            
            log "Checking for model: $MODEL_NAME"
            if ! ollama list | grep -q "$MODEL_NAME"; then
                warning "Model $MODEL_NAME not found. Pulling..."
                ollama pull "$MODEL_NAME"
            fi
            success "Model $MODEL_NAME is available"
        else
            warning "Ollama failed to start. Please check Ollama installation."
        fi
    else
        warning "Ollama not installed. Make sure to configure cloud LLM providers."
    fi
fi

# Check API port availability
API_PORT=${API_PORT:-8000}
if ! port_available $API_PORT; then
    error "Port $API_PORT is already in use"
    log "To use a different port: export API_PORT=8001"
    exit 1
fi

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Start the system
log "Starting Sonnet Model API server..."

# Choose startup method
if [ "${USE_DOCKER:-false}" = "true" ] && command_exists docker-compose; then
    log "Starting with Docker Compose..."
    docker-compose up -d
    
    # Wait for services
    wait_for_service "API Server" "http://localhost:$API_PORT/health"
    
else
    log "Starting with Python directly..."
    
    # Start API server
    python -m api.app &
    API_PID=$!
    
    # Wait for API to be ready
    wait_for_service "API Server" "http://localhost:$API_PORT/health"
fi

# Run system verification
log "Running system verification..."
if python test_complete_system.py >/dev/null 2>&1; then
    success "System verification passed!"
else
    warning "System verification had some issues. Check logs for details."
fi

# Display system status
echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗"
echo "║                    SYSTEM STARTED SUCCESSFULLY              ║"
echo "╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "\n${BLUE}📊 System Information:${NC}"
echo "🌐 API Server: http://localhost:$API_PORT"
echo "📚 API Documentation: http://localhost:$API_PORT/docs"
echo "❤️  Health Check: http://localhost:$API_PORT/health"
echo "📈 System Status: http://localhost:$API_PORT/api/v1/status"

if [ "${USE_REDIS:-true}" = "true" ]; then
    echo "🔄 Redis: Running (Multi-worker ready)"
else
    echo "🔄 State Management: In-memory (Single worker)"
fi

if [ "$LLM_TYPE" = "http_api" ] && command_exists ollama; then
    echo "🤖 Local LLM: Ollama ($MODEL_NAME)"
else
    echo "☁️  LLM: Cloud provider configured"
fi

echo -e "\n${BLUE}🚀 Quick Commands:${NC}"
echo "• Check status: curl http://localhost:$API_PORT/health"
echo "• View logs: tail -f logs/sonnet.log"
echo "• Stop system: ./scripts/stop_system.sh"
echo "• System status: ./scripts/system_status.sh"

echo -e "\n${BLUE}📖 Documentation:${NC}"
echo "• System Management: docs/SYSTEM_MANAGEMENT.md"
echo "• Enhanced Features: docs/ENHANCED_FEATURES_GUIDE.md"
echo "• Architecture: docs/ARCHITECTURE.md"

# Save PIDs for cleanup
if [ ! -z "$API_PID" ]; then
    echo $API_PID > .api.pid
fi
if [ ! -z "$OLLAMA_PID" ]; then
    echo $OLLAMA_PID > .ollama.pid
fi

success "Sonnet Model system is ready for use!"

# Optional: Open browser
if command_exists xdg-open && [ "${OPEN_BROWSER:-false}" = "true" ]; then
    xdg-open "http://localhost:$API_PORT/docs"
elif command_exists open && [ "${OPEN_BROWSER:-false}" = "true" ]; then
    open "http://localhost:$API_PORT/docs"
fi
