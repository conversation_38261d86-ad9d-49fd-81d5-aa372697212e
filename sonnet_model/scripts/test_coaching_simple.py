#!/usr/bin/env python3
"""
Simple test script for the persistent coaching system core functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test the core coaching logic without complex dependencies
async def test_llm_critic_core():
    """Test LLM Critic core functionality"""
    print("🧠 Testing LLM Critic Core Functionality")
    print("=" * 50)
    
    # Import the LLM Critic directly
    from critique_engine.services.llm_critic import LLMCritic
    
    config = {
        "coaching_enabled": True,
        "hesitation_threshold": 0.7,
        "encouragement_frequency": 3
    }
    
    critic = LLMCritic(config)
    
    # Test 1: Hesitation Detection
    print("\n🎯 Test 1: Hesitation Detection")
    test_messages = [
        "I think we should implement this feature",  # No hesitation
        "Should I continue with this approach?",     # Asking permission
        "I'm not sure about this implementation",    # Uncertainty
        "This error is blocking progress",           # Error
        "Maybe we should stop here?",                # Giving up
        "Let's build the next component"             # Confidence
    ]
    
    for message in test_messages:
        result = await critic.detect_llm_hesitation(message)
        status = "🚨 HESITATING" if result['is_hesitating'] else "✅ CONFIDENT"
        print(f"📝 '{message[:40]}...'")
        print(f"   {status} (confidence: {result['confidence_score']:.2f})")
        print(f"   🏷️ Type: {result['hesitation_type']}")
        
        if result['is_hesitating']:
            push_response = await critic.generate_push_forward_response(
                result['hesitation_type'], message
            )
            print(f"   💪 Coaching: {push_response[:80]}...")
        print()
    
    # Test 2: Standard Encouragement
    print("🌟 Test 2: Standard Encouragement Generation")
    for i in range(3):
        encouragement = await critic.generate_standard_encouragement()
        print(f"   {i+1}. {encouragement}")
    
    # Test 3: Resumption Coaching
    print("\n🔄 Test 3: Session Resumption Coaching")
    resumption_data = {
        "project_name": "test_project",
        "last_activity": "2024-01-15T10:30:00",
        "momentum_level": "medium",
        "session_info": {
            "total_tasks_completed": 3,
            "session_duration": "2 hours"
        },
        "incomplete_tasks": ["task1", "task2"]
    }
    
    resumption_coaching = await critic.generate_resumption_coaching(resumption_data)
    print(f"📋 Project: {resumption_data['project_name']}")
    print(f"🕐 Last activity: {resumption_data['last_activity']}")
    print(f"⚡ Momentum: {resumption_data['momentum_level']}")
    print(f"💬 Coaching: {resumption_coaching.get('message', 'No message')}")
    
    # Test 4: Push Forward Responses
    print("\n💪 Test 4: Push Forward Response Types")
    scenarios = [
        ("asking_permission", "Should I continue with the implementation?"),
        ("uncertainty", "I'm not sure if this is the right approach"),
        ("error_encountered", "There's an error in the code"),
        ("complexity_concern", "This seems too complex to handle"),
        ("giving_up", "Maybe we should stop here")
    ]
    
    for hesitation_type, message in scenarios:
        response = await critic.generate_push_forward_response(hesitation_type, message)
        print(f"🎯 {hesitation_type.replace('_', ' ').title()}:")
        print(f"   📝 Input: '{message}'")
        print(f"   🚀 Response: {response[:100]}...")
        print()
    
    print("✅ LLM Critic core functionality test completed!")
    return True


async def test_state_manager_core():
    """Test State Manager core functionality"""
    print("\n💾 Testing State Manager Core Functionality")
    print("=" * 50)
    
    # Import the State Manager directly  
    from task_manager.services.state_manager import StateManager
    from task_manager.models.task import Task, TaskStatus, TaskPriority
    from task_manager.models.plan import Plan, PlanStep, StepStatus
    from datetime import datetime
    
    config = {
        "persistence_enabled": True,
        "state_file_path": "data/test_state.json",
        "backup_interval_minutes": 1
    }
    
    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    
    state_manager = StateManager(config)
    await state_manager.initialize()
    
    # Test 1: Session Management
    print("🎯 Test 1: Session Management")
    session_id = await state_manager.create_session("test_project")
    print(f"✅ Created session: {session_id}")
    
    latest_session = await state_manager.get_latest_session()
    print(f"✅ Latest session: {latest_session['session_id']}")
    print(f"✅ Project: {latest_session['project_name']}")
    
    # Test 2: Coaching State Management
    print("\n🎯 Test 2: Coaching State Management")
    coaching_data = {
        "momentum_level": "high",
        "message": "Great progress! Keep going!",
        "hesitation_detected": False
    }
    
    await state_manager.update_coaching_state(coaching_data)
    momentum = await state_manager.get_momentum_level()
    print(f"✅ Updated momentum level: {momentum}")
    
    coaching_history = await state_manager.get_coaching_history(3)
    print(f"✅ Coaching history entries: {len(coaching_history)}")
    
    # Test 3: Task and Plan Management
    print("\n🎯 Test 3: Task and Plan Management")
    
    # Create a test task
    task = Task(
        id="test_task_1",
        name="Test Implementation",
        description="Implement test functionality",
        status=TaskStatus.IN_PROGRESS,
        priority=TaskPriority.HIGH,
        created_at=datetime.now()
    )
    
    await state_manager.save_task(task)
    retrieved_task = await state_manager.get_task("test_task_1")
    print(f"✅ Task saved and retrieved: {retrieved_task.name}")
    
    # Create a test plan
    plan = Plan(
        id="test_plan_1",
        name="Test Development Plan",
        description="Plan for testing",
        created_at=datetime.now(),
        priority=TaskPriority.HIGH
    )
    
    # Add steps to plan
    step1 = PlanStep(
        id="step_1",
        name="Setup",
        description="Setup the environment",
        status=StepStatus.COMPLETED
    )
    step2 = PlanStep(
        id="step_2", 
        name="Implementation",
        description="Implement the feature",
        status=StepStatus.IN_PROGRESS
    )
    
    plan.add_step(step1)
    plan.add_step(step2)
    
    await state_manager.save_plan(session_id, plan)
    retrieved_plan = await state_manager.get_plan("test_plan_1")
    print(f"✅ Plan saved and retrieved: {retrieved_plan.name}")
    print(f"✅ Plan has {len(retrieved_plan.steps)} steps")
    
    # Test 4: Project Summary
    print("\n🎯 Test 4: Project Summary")
    summary = await state_manager.get_project_summary("test_project")
    print(f"✅ Project: {summary['project_name']}")
    print(f"✅ Session: {summary['session_id']}")
    print(f"✅ Momentum: {summary['momentum_level']}")
    print(f"✅ Total tasks: {summary['tasks']['total']}")
    print(f"✅ Coaching interactions: {summary['coaching_interactions']}")
    
    # Cleanup
    await state_manager.shutdown()
    print("✅ State Manager core functionality test completed!")
    return True


async def test_integration_flow():
    """Test the integration flow without complex dependencies"""
    print("\n🔗 Testing Integration Flow")
    print("=" * 50)
    
    from critique_engine.services.llm_critic import LLMCritic
    from task_manager.services.state_manager import StateManager
    
    # Initialize components
    critic_config = {"coaching_enabled": True, "hesitation_threshold": 0.7}
    state_config = {"persistence_enabled": True, "state_file_path": "data/integration_test.json"}
    
    critic = LLMCritic(critic_config)
    state_manager = StateManager(state_config)
    await state_manager.initialize()
    
    # Simulate a coaching workflow
    print("🎯 Simulating Coaching Workflow")
    
    # 1. Start session
    session_id = await state_manager.create_session("integration_test_project")
    print(f"✅ Started session: {session_id}")
    
    # 2. Detect hesitation and provide coaching
    llm_message = "Should I continue with this complex implementation?"
    hesitation_result = await critic.detect_llm_hesitation(llm_message)
    
    if hesitation_result['is_hesitating']:
        # Generate coaching response
        coaching_response = await critic.generate_push_forward_response(
            hesitation_result['hesitation_type'], 
            llm_message
        )
        
        # Update state with coaching
        coaching_data = {
            "momentum_level": "medium",
            "message": coaching_response,
            "hesitation_detected": True,
            "hesitation_type": hesitation_result['hesitation_type']
        }
        
        await state_manager.update_coaching_state(coaching_data)
        
        print(f"🚨 Hesitation detected: {hesitation_result['hesitation_type']}")
        print(f"💪 Coaching provided: {coaching_response[:80]}...")
        print(f"📊 Momentum updated: {await state_manager.get_momentum_level()}")
    
    # 3. Simulate progress and success
    success_coaching = await critic.generate_standard_encouragement()
    await state_manager.update_coaching_state({
        "momentum_level": "high",
        "message": success_coaching,
        "progress_made": True
    })
    
    print(f"🎉 Success coaching: {success_coaching}")
    print(f"⚡ Final momentum: {await state_manager.get_momentum_level()}")
    
    # 4. Get final summary
    summary = await state_manager.get_project_summary()
    print(f"📋 Final project summary:")
    print(f"   - Momentum: {summary['momentum_level']}")
    print(f"   - Coaching interactions: {summary['coaching_interactions']}")
    
    await state_manager.shutdown()
    print("✅ Integration flow test completed!")
    return True


async def main():
    """Run all core functionality tests"""
    print("🧪 Starting Persistent Coaching System Core Tests")
    print("=" * 80)
    
    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)
    
    # Run tests
    success1 = await test_llm_critic_core()
    success2 = await test_state_manager_core()
    success3 = await test_integration_flow()
    
    if success1 and success2 and success3:
        print("\n🎊 ALL CORE TESTS PASSED! 🎊")
        print("✅ LLM Critic: Hesitation detection and coaching working")
        print("✅ State Manager: Session and coaching state persistence working")
        print("✅ Integration: Components work together seamlessly")
        print("\n🚀 The persistent coaching system core is fully functional!")
    else:
        print("\n❌ Some tests failed. Check the output above.")


if __name__ == "__main__":
    asyncio.run(main())
