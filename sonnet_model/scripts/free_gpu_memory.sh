#!/bin/bash

# GPU Memory Management Script
# Frees GPU memory and manages GPU resources for the Sonnet Model system

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Banner
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    GPU MEMORY MANAGER                       ║"
echo "║                   Freeing GPU Resources                     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check if NVIDIA GPU is available
if ! command_exists nvidia-smi; then
    error "nvidia-smi not found. No NVIDIA GPU detected or drivers not installed."
    exit 1
fi

if ! nvidia-smi >/dev/null 2>&1; then
    error "Cannot access NVIDIA GPU. Check drivers and permissions."
    exit 1
fi

# Display initial GPU status
log "Initial GPU status:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu --format=table

echo ""

# Get GPU processes
log "Checking for GPU processes..."
gpu_processes=$(nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader,nounits 2>/dev/null)

if [ -z "$gpu_processes" ]; then
    success "No GPU processes found"
else
    log "Found GPU processes:"
    echo "$gpu_processes" | while IFS=, read -r pid process_name memory; do
        echo -e "${YELLOW}  PID $pid: $process_name (${memory}MB)${NC}"
    done
    
    echo ""
    
    # Ask for confirmation unless force mode
    if [ "${FORCE:-false}" != "true" ]; then
        echo -e "${YELLOW}Do you want to terminate these GPU processes? (y/N):${NC}"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log "GPU process termination cancelled"
            exit 0
        fi
    fi
    
    # Terminate GPU processes
    log "Terminating GPU processes..."
    echo "$gpu_processes" | while IFS=, read -r pid process_name memory; do
        if kill -0 "$pid" 2>/dev/null; then
            log "Terminating PID $pid ($process_name)..."
            
            # Try graceful termination first
            kill -TERM "$pid" 2>/dev/null || true
            sleep 2
            
            # Check if process is still running
            if kill -0 "$pid" 2>/dev/null; then
                warning "Process $pid still running, force killing..."
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            success "Process $pid terminated"
        fi
    done
fi

# Clear PyTorch GPU cache
log "Clearing PyTorch GPU cache..."
python -c "
import sys
try:
    import torch
    if torch.cuda.is_available():
        # Clear cache for all available GPUs
        for i in range(torch.cuda.device_count()):
            with torch.cuda.device(i):
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
        print('✅ PyTorch GPU cache cleared for all devices')
    else:
        print('ℹ️  CUDA not available in PyTorch')
except ImportError:
    print('ℹ️  PyTorch not installed')
except Exception as e:
    print(f'⚠️  Error clearing PyTorch cache: {e}')
" 2>/dev/null

# Clear TensorFlow GPU memory (if available)
log "Clearing TensorFlow GPU memory..."
python -c "
try:
    import tensorflow as tf
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print('✅ TensorFlow GPU memory growth enabled')
    else:
        print('ℹ️  No TensorFlow GPUs found')
except ImportError:
    print('ℹ️  TensorFlow not installed')
except Exception as e:
    print(f'⚠️  Error configuring TensorFlow GPU: {e}')
" 2>/dev/null

# Reset GPU if requested
if [ "${RESET_GPU:-false}" = "true" ]; then
    log "Resetting GPU..."
    
    # This requires root privileges
    if [ "$EUID" -eq 0 ]; then
        nvidia-smi --gpu-reset
        success "GPU reset completed"
    else
        warning "GPU reset requires root privileges. Run with sudo for full reset."
    fi
fi

# Wait a moment for cleanup to complete
sleep 2

# Display final GPU status
echo ""
log "Final GPU status:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total,utilization.gpu --format=table

# Calculate memory freed
initial_memory=$(nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits | head -1)
final_memory=$(nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits | head -1)

if [ ! -z "$initial_memory" ] && [ ! -z "$final_memory" ]; then
    memory_freed=$((initial_memory - final_memory))
    if [ $memory_freed -gt 0 ]; then
        success "Freed ${memory_freed}MB of GPU memory"
    else
        log "GPU memory usage unchanged"
    fi
fi

# Show detailed memory info
echo ""
log "Detailed GPU memory information:"
nvidia-smi --query-gpu=memory.used,memory.free,memory.total --format=csv,units

# Check for any remaining GPU processes
remaining_processes=$(nvidia-smi --query-compute-apps=pid --format=csv,noheader,nounits 2>/dev/null)
if [ -z "$remaining_processes" ]; then
    success "No GPU processes remaining"
else
    warning "Some GPU processes are still running:"
    nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=table
fi

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗"
echo "║                  GPU CLEANUP COMPLETED                      ║"
echo "╚══════════════════════════════════════════════════════════════╝${NC}"

echo -e "\n${BLUE}💡 Tips for GPU memory management:${NC}"
echo "• Monitor GPU usage: watch -n 1 nvidia-smi"
echo "• Set GPU memory limits in config/config.yaml"
echo "• Use smaller models if memory is limited"
echo "• Enable GPU memory growth for TensorFlow"
echo "• Use mixed precision training to reduce memory usage"

echo -e "\n${BLUE}🚀 Next steps:${NC}"
echo "• Restart Sonnet Model: ./scripts/start_system.sh"
echo "• Check system status: ./scripts/system_status.sh"
echo "• Monitor GPU usage: nvidia-smi -l 1"

# Optional: Show GPU temperature and power
if [ "${SHOW_DETAILED:-false}" = "true" ]; then
    echo -e "\n${BLUE}🌡️  GPU Temperature and Power:${NC}"
    nvidia-smi --query-gpu=temperature.gpu,power.draw,power.limit --format=csv,units
fi

success "GPU memory management completed!"
