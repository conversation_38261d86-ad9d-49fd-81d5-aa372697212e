system:
  name: "Sonnet Model"
  version: "0.1.0"
  log_level: "INFO"
  environment: "development"  # development, testing, production

task_manager:
  max_concurrent_tasks: 3
  task_timeout_seconds: 600
  retry_attempts: 3
  retry_delay_seconds: 30
  priority_levels: 5
  database:
    type: "sqlite"  # sqlite, postgres
    path: "data/sonnet.db"
    # For postgres:
    # host: "localhost"
    # port: 5432
    # username: "sonnet"
    # password: "password"
    # database: "sonnet"

code_generator:
  llm:
    type: "http_api"
    api_url: "http://localhost:11434/api/generate"
    model: "deepseek-coder-v2:16b"
    temperature: 0.2
    max_tokens: 4096
    timeout: 300
  timeout_seconds: 120
  max_retries: 2
  cache_enabled: true
  cache_ttl_seconds: 3600

critique_engine:
  local_llm:
    enabled: true
    type: "http_api"
    api_url: "http://localhost:11434/api/generate"
    model: "deepseek-coder-v2:16b"  # Primary model for critique
    fallback_model: "deepseek-coder-v2:16b"  # Same model for now
    context_window: 8192
    temperature: 0.1
    max_tokens: 2048
    timeout: 300
  static_analysis:
    enabled: true
    tools:
      python:
        - "pylint"
        - "mypy"
        - "bandit"
        - "black"
      javascript:
        - "eslint"
        - "tsc"
      general:
        - "sonarqube"
    max_line_length: 88
    strictness_level: "medium"  # low, medium, high
  testing:
    enabled: true
    frameworks:
      python:
        - "pytest"
      javascript:
        - "jest"
    timeout_seconds: 60
    max_test_cases: 10
  feedback:
    detail_level: "high"  # low, medium, high
    include_code_snippets: true
    include_references: true
    max_issues_to_report: 10

api:
  host: "0.0.0.0"
  port: 8000
  debug: true
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
  rate_limit:
    enabled: true
    requests_per_minute: 60
  auth:
    enabled: false
    # jwt_secret: "your-secret-key"
    # token_expiry_minutes: 60

message_bus:
  type: "redis"  # redis, rabbitmq, memory
  host: "localhost"
  port: 6379
  # For RabbitMQ:
  # username: "guest"
  # password: "guest"
  # vhost: "/"
  channel_prefetch: 10
  message_ttl_seconds: 3600

monitoring:
  enabled: true
  prometheus:
    enabled: true
    port: 9090
  logging:
    file: "logs/sonnet.log"
    max_size_mb: 10
    backup_count: 5
    format: "json"  # json, text

resources:
  cpu_limit: 0.8  # 80% of available CPU
  memory_limit_gb: 16
  gpu_enabled: true
  gpu_memory_limit_gb: 20
