"""
Real File-Based Feedback Loop Test

This creates ACTUAL FILES in a project folder and improves them iteratively:
1. Creates real project folder (calendar_app/)
2. Generates actual code files 
3. LLM critiques the files
4. LLM improves the files based on feedback
5. Files are updated on disk
6. Process repeats until quality is acceptable

You can see the actual files being created and modified!
"""

import asyncio
import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List
import httpx

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

class RealFileBasedFeedbackLoop:
    """Creates and improves actual files using LLM feedback"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model = "deepseek-coder-v2:16b"
        
        # Project setup
        self.project_name = "calendar_app"
        self.project_path = Path(self.project_name)
        
        # Calendar app tasks
        self.tasks = [
            {
                "id": 1,
                "name": "Event Class",
                "description": "Create an Event class to represent calendar events",
                "filename": "event.py",
                "requirements": [
                    "Event class with title, date, time, description",
                    "Validation for date/time formats",
                    "String representation",
                    "Comparison methods for sorting",
                    "Duration calculation"
                ]
            },
            {
                "id": 2,
                "name": "Calendar Manager",
                "description": "Create a Calendar class to manage events",
                "filename": "calendar.py", 
                "requirements": [
                    "Add, remove, update events",
                    "Find events by date/title",
                    "List events in date range",
                    "Save/load from file",
                    "Conflict detection for overlapping events"
                ]
            },
            {
                "id": 3,
                "name": "Calendar CLI",
                "description": "Create a command-line interface for the calendar",
                "filename": "cli.py",
                "requirements": [
                    "Add event command",
                    "List events command", 
                    "Delete event command",
                    "Search events command",
                    "Interactive menu system"
                ]
            }
        ]
        
    async def run_real_file_feedback_loop(self):
        """Run the complete file-based feedback loop"""
        
        print("🚀 REAL FILE-BASED FEEDBACK LOOP")
        print("=" * 60)
        print(f"📁 Project: {self.project_name}")
        print("🎯 Creating actual files and improving them iteratively")
        print("=" * 60)
        
        # Test Ollama connection
        if not await self._test_ollama_connection():
            print("❌ Cannot connect to Ollama")
            return
        
        print("✅ Connected to Ollama successfully!")
        
        # Setup project folder
        self._setup_project_folder()
        
        # Process each task
        for task in self.tasks:
            print(f"\n📋 PROCESSING TASK {task['id']}: {task['name']}")
            print(f"📄 File: {task['filename']}")
            print("-" * 60)
            
            await self._process_task_with_file_feedback(task)
            
            print(f"✅ TASK {task['id']} COMPLETED!")
            print(f"📄 File {task['filename']} created and improved")
        
        # Show final project structure
        self._show_final_project()
        
        print("\n🎉 ALL FILES CREATED AND IMPROVED!")
        print(f"📁 Check the '{self.project_name}' folder to see actual files!")
        
    def _setup_project_folder(self):
        """Setup the project folder"""
        
        # Remove existing folder if it exists
        if self.project_path.exists():
            shutil.rmtree(self.project_path)
        
        # Create new project folder
        self.project_path.mkdir()
        
        # Create README
        readme_content = f"""# {self.project_name.title().replace('_', ' ')}

A Python calendar application with event management.

## Files:
- `event.py` - Event class for representing calendar events
- `calendar.py` - Calendar manager for event operations  
- `cli.py` - Command-line interface

## Usage:
```bash
python cli.py
```

Generated by Real LLM Feedback Loop System.
"""
        
        with open(self.project_path / "README.md", "w") as f:
            f.write(readme_content)
        
        print(f"📁 Created project folder: {self.project_path}")
        
    async def _process_task_with_file_feedback(self, task: Dict):
        """Process a task with real file creation and improvement"""
        
        file_path = self.project_path / task['filename']
        iteration = 1
        max_iterations = 30
        
        while iteration <= max_iterations:
            print(f"\n🔄 ITERATION {iteration} - {task['filename']}")
            print("-" * 30)
            
            # Read existing file content if it exists
            existing_content = ""
            if file_path.exists():
                with open(file_path, 'r') as f:
                    existing_content = f.read()
            
            # Generate/improve code
            print("🤖 CODE GENERATOR: Working on file...")
            new_content = await self._generate_file_content(task, existing_content, iteration)
            
            if not new_content:
                print("❌ Code generation failed")
                break
            
            # Write to actual file
            with open(file_path, 'w') as f:
                f.write(new_content)
            
            print(f"📄 File written: {file_path}")
            print(f"📊 File size: {len(new_content)} characters")
            
            # Show file preview
            lines = new_content.split('\n')[:10]
            print("📝 File Preview:")
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line}")
            if len(new_content.split('\n')) > 10:
                print("   ...: (more lines)")
            
            # Critique the file
            print("\n🔍 CRITIQUE ENGINE: Analyzing file...")
            critique = await self._critique_file_content(new_content, task)
            
            if not critique:
                print("❌ Critique failed")
                break
            
            print(f"📊 Quality Score: {critique['quality_score']}/10")
            print(f"🔍 Issues Found: {len(critique['issues'])}")
            
            if critique['issues']:
                print("⚠️  Issues:")
                for issue in critique['issues'][:3]:
                    print(f"   - {issue}")
            
            # Decision point
            if critique['quality_score'] >= 7 or iteration == max_iterations:
                print(f"\n✅ File {task['filename']} completed!")
                if iteration < max_iterations:
                    print("   Quality threshold reached")
                else:
                    print("   Maximum iterations reached")
                break
            else:
                print(f"\n🔧 Preparing iteration {iteration + 1}...")
                iteration += 1
                await asyncio.sleep(1)
    
    async def _test_ollama_connection(self) -> bool:
        """Test Ollama connection"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
                return response.status_code == 200
        except:
            return False
    
    async def _generate_file_content(self, task: Dict, existing_content: str, iteration: int) -> str:
        """Generate content for a specific file"""
        
        if iteration == 1:
            prompt = f"""You are an expert Python developer. Create a complete Python file for this task:

Task: {task['description']}
Filename: {task['filename']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Please provide a complete, working Python implementation with:
- Proper imports
- Class/function definitions
- Docstrings
- Type hints
- Error handling
- Example usage (if appropriate)

Python code:"""
        else:
            prompt = f"""You are an expert Python developer. Improve this Python file based on the requirements:

Task: {task['description']}
Filename: {task['filename']}

Requirements:
{chr(10).join(f"- {req}" for req in task['requirements'])}

Current file content:
```python
{existing_content}
```

Please provide an improved version that better meets all requirements.

Improved Python code:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.2,
                            "top_p": 0.9,
                            "num_predict": 4096
                        }
                    },
                    timeout=180.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    generated_text = result.get("response", "")
                    return self._extract_code_from_response(generated_text)
                else:
                    return ""
                    
        except Exception as e:
            self.logger.error(f"File generation failed: {e}")
            return ""
    
    async def _critique_file_content(self, content: str, task: Dict) -> Dict[str, Any]:
        """Critique file content"""
        
        prompt = f"""You are an expert code reviewer. Analyze this Python file:

Task: {task['description']}
Filename: {task['filename']}

Requirements to check:
{chr(10).join(f"- {req}" for req in task['requirements'])}

File content:
```python
{content}
```

Provide analysis in JSON format:
{{
    "quality_score": <number 1-10>,
    "issues": ["issue1", "issue2"],
    "suggestions": ["suggestion1", "suggestion2"],
    "requirements_met": <number of requirements satisfied>
}}

Analysis:"""
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.ollama_url,
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": 0.1,
                            "top_p": 0.8,
                            "num_predict": 1024
                        }
                    },
                    timeout=120.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    critique_text = result.get("response", "")
                    return self._parse_critique_response(critique_text)
                else:
                    return {"quality_score": 5, "issues": ["Analysis failed"], "suggestions": []}
                    
        except Exception as e:
            self.logger.error(f"File critique failed: {e}")
            return {"quality_score": 5, "issues": ["Analysis failed"], "suggestions": []}
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from LLM response"""
        
        if "```python" in response:
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        elif "```" in response:
            start = response.find("```") + 3
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        # Look for Python code patterns
        lines = response.split('\n')
        code_lines = []
        in_code = False
        
        for line in lines:
            if line.strip().startswith(('class ', 'def ', 'import ', 'from ', '#')):
                in_code = True
            
            if in_code:
                code_lines.append(line)
        
        return '\n'.join(code_lines) if code_lines else response.strip()
    
    def _parse_critique_response(self, response: str) -> Dict[str, Any]:
        """Parse critique JSON response"""
        try:
            start = response.find('{')
            end = response.rfind('}') + 1
            
            if start != -1 and end > start:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        
        # Fallback
        return {
            "quality_score": 6,
            "issues": ["Manual review needed"],
            "suggestions": ["Check implementation"],
            "requirements_met": 3
        }
    
    def _show_final_project(self):
        """Show the final project structure"""
        
        print(f"\n📁 FINAL PROJECT STRUCTURE:")
        print("=" * 40)
        
        for item in sorted(self.project_path.iterdir()):
            if item.is_file():
                size = item.stat().st_size
                print(f"📄 {item.name} ({size} bytes)")
                
                # Show first few lines of each Python file
                if item.suffix == '.py':
                    try:
                        with open(item, 'r') as f:
                            lines = f.readlines()[:5]
                        print("   Preview:")
                        for line in lines:
                            print(f"   {line.rstrip()}")
                        if len(lines) >= 5:
                            print("   ...")
                        print()
                    except:
                        pass


async def main():
    """Run the real file-based feedback loop"""
    
    print("🎯 REAL FILE-BASED FEEDBACK LOOP TEST")
    print("Creating actual files and improving them with LLM feedback")
    print("=" * 60)
    
    tester = RealFileBasedFeedbackLoop()
    await tester.run_real_file_feedback_loop()


if __name__ == "__main__":
    asyncio.run(main())
