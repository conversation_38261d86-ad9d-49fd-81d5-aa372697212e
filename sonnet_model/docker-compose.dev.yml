version: '3.8'

# Development overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

services:
  agentic-system:
    environment:
      # Development-specific settings
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - API_WORKERS=1
      - USE_REDIS=false  # Use in-memory for development
      
      # Development features
      - DEBUG=true
      - RELOAD=true
      - OPEN_BROWSER=false
    
    # Development volume mounts for hot reload
    volumes:
      - ./:/app
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    
    # Override command for development
    command: python -m api.app --reload --workers 1
    
    # Development ports (expose more for debugging)
    ports:
      - "${API_PORT:-8000}:8000"
      - "8001:8001"  # Debug port
    
    # Development dependencies (optional Redis)
    depends_on:
      - redis
    
    # No resource limits in development
    deploy: {}

  redis:
    # Development Redis configuration (lighter)
    command: redis-server --appendonly no --save ""
    
    # Expose Redis port for development tools
    ports:
      - "${REDIS_PORT:-6379}:6379"

  # Remove PostgreSQL in development (use SQLite)
  postgres:
    profiles:
      - postgres  # Only start if explicitly requested

  # Make LLM server optional in development
  llm-server:
    profiles:
      - ollama  # Only start if explicitly requested
    
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_ORIGINS=*
      - OLLAMA_DEBUG=1
    
    # Expose Ollama port for development
    ports:
      - "${OLLAMA_PORT:-11434}:11434"

# Development-specific volumes (faster for development)
volumes:
  redis_data:
    driver: local
  
  postgres_data:
    driver: local
  
  ollama_data:
    driver: local
